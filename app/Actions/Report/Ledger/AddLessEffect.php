<?php

namespace App\Actions\Report\Ledger;

use App\Actions\CommonAction\IsNegative;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class AddLessEffect
{
    use AsAction;

    public function handle($data, $ledgerId)
    {

        $modelType = get_class($data);

        return match ($modelType) {
            SaleTransaction::class => $this->saleTransaction($data, $ledgerId),
            SaleReturnTransaction::class => $this->saleReturnTransaction($data, $ledgerId),
            IncomeDebitNoteTransaction::class => $this->incomeDebitNoteTransaction($data, $ledgerId),
            IncomeCreditNoteTransaction::class => $this->incomeCreditNoteTransaction($data, $ledgerId),
            PurchaseTransaction::class => $this->purchaseTransaction($data, $ledgerId),
            PurchaseReturnTransaction::class => $this->purchaseReturnTransaction($data, $ledgerId),
            ExpenseDebitNoteTransaction::class => $this->expenseDebitNoteTransaction($data, $ledgerId),
            ExpenseCreditNoteTransaction::class => $this->expenseCreditNoteTransaction($data, $ledgerId),
            default => [],
        };
    }

    public function saleTransaction($transaction, $ledgerId)
    {

        return $transaction->addLess
            ->where('ledger_id', $ledgerId)
            ->map(function ($addLess) use ($transaction) {
                $debit = 0;
                $credit = 0;
                if (IsNegative::run($addLess->total)) {
                    $debit = abs($addLess->total);
                } else {
                    $credit = $addLess->total;
                }

                return [
                    'transaction_id' => $transaction->id,
                    'date' => $transaction->date->format('d-m-y'),
                    'ledger_name' => $transaction->customer->name,
                    'transaction_type' => 'Sales',
                    'voucher_no' => $transaction->full_invoice_number,
                    'invoice_no' => '',
                    'debit_amount' => $debit,
                    'credit_amount' => $credit,
                    'narration' => $transaction->narration ?? null,
                    'balance' => 0,
                ];
            })->toArray();
    }

    public function saleReturnTransaction($transaction, $ledgerId)
    {
        return $transaction->addLess
            ->where('ledger_id', $ledgerId)
            ->map(function ($addLess) use ($transaction) {
                $debit = 0;
                $credit = 0;
                if (IsNegative::run($addLess->total)) {
                    $credit = abs($addLess->total);
                } else {
                    $debit = $addLess->total;
                }

                return [
                    'transaction_id' => $transaction->id,
                    'date' => $transaction->date->format('d-m-y'),
                    'ledger_name' => $transaction->customer->name,
                    'transaction_type' => 'Sale Return',
                    'voucher_no' => $transaction->full_invoice_number,
                    'invoice_no' => '',
                    'debit_amount' => $debit,
                    'credit_amount' => $credit,
                    'narration' => $transaction->narration ?? null,
                    'balance' => 0,
                ];
            })->toArray();
    }

    public function incomeDebitNoteTransaction($transaction, $ledgerId)
    {
        return $transaction->addLess
            ->where('ledger_id', $ledgerId)
            ->map(function ($addLess) use ($transaction) {
                $debit = 0;
                $credit = 0;
                if (IsNegative::run($addLess->total)) {
                    $debit = abs($addLess->total);
                } else {
                    $credit = $addLess->total;
                }

                return [
                    'transaction_id' => $transaction->id,
                    'date' => $transaction->date->format('d-m-y'),
                    'ledger_name' => $transaction->customer->name,
                    'transaction_type' => 'Sale Return',
                    'voucher_no' => $transaction->full_invoice_number,
                    'invoice_no' => '',
                    'debit_amount' => $debit,
                    'credit_amount' => $credit,
                    'narration' => $transaction->narration ?? null,
                    'balance' => 0,
                ];
            })->toArray();
    }

    public function incomeCreditNoteTransaction($transaction, $ledgerId)
    {
        return $transaction->addLess
            ->where('ledger_id', $ledgerId)
            ->map(function ($addLess) use ($transaction) {
                $debit = 0;
                $credit = 0;
                if (IsNegative::run($addLess->total)) {
                    $credit = abs($addLess->total);
                } else {
                    $debit = $addLess->total;
                }

                return [
                    'transaction_id' => $transaction->id,
                    'date' => $transaction->date->format('d-m-y'),
                    'ledger_name' => $transaction->customer->name,
                    'transaction_type' => 'Sale Return',
                    'voucher_no' => $transaction->full_invoice_number,
                    'invoice_no' => '',
                    'debit_amount' => $debit,
                    'credit_amount' => $credit,
                    'narration' => $transaction->narration ?? null,
                    'balance' => 0,
                ];
            })->toArray();
    }

    public function purchaseTransaction($transaction, $ledgerId)
    {
        return $transaction->addLess
            ->where('ledger_id', $ledgerId)
            ->map(function ($addLess) use ($transaction) {
                $debit = 0;
                $credit = 0;
                if (IsNegative::run($addLess->total)) {
                    $credit = abs($addLess->total);
                } else {
                    $debit = $addLess->total;
                }

                return [
                    'transaction_id' => $transaction->id,
                    'date' => $transaction->voucher_date->format('d-m-y'),
                    'ledger_name' => $transaction->supplier->name,
                    'transaction_type' => 'Purchase',
                    'voucher_no' => $transaction->voucher_number,
                    'invoice_no' => '',
                    'debit_amount' => $debit,
                    'credit_amount' => $credit,
                    'narration' => $transaction->narration ?? null,
                    'balance' => 0,
                ];
            })->toArray();
    }

    public function purchaseReturnTransaction($transaction, $ledgerId)
    {
        return $transaction->addLess
            ->where('ledger_id', $ledgerId)
            ->map(function ($addLess) use ($transaction) {
                $debit = 0;
                $credit = 0;
                if (IsNegative::run($addLess->total)) {
                    $debit = abs($addLess->total);
                } else {
                    $credit = $addLess->total;
                }

                return [
                    'transaction_id' => $transaction->id,
                    'date' => $transaction->voucher_date->format('d-m-y'),
                    'ledger_name' => $transaction->supplier->name,
                    'transaction_type' => 'Purchase Return',
                    'voucher_no' => $transaction->voucher_number,
                    'invoice_no' => '',
                    'debit_amount' => $debit,
                    'credit_amount' => $credit,
                    'narration' => $transaction->narration ?? null,
                    'balance' => 0,
                ];
            })->toArray();
    }

    public function expenseDebitNoteTransaction($transaction, $ledgerId)
    {
        return $transaction->addLess
            ->where('ledger_id', $ledgerId)
            ->map(function ($addLess) use ($transaction) {
                $debit = 0;
                $credit = 0;
                if (IsNegative::run($addLess->total)) {
                    $debit = abs($addLess->total);
                } else {
                    $credit = $addLess->total;
                }

                return [
                    'transaction_id' => $transaction->id,
                    'date' => $transaction->voucher_date->format('d-m-y'),
                    'ledger_name' => $transaction->supplier->name,
                    'transaction_type' => 'Purchase Return',
                    'voucher_no' => $transaction->voucher_number,
                    'invoice_no' => '',
                    'debit_amount' => $debit,
                    'credit_amount' => $credit,
                    'narration' => $transaction->narration ?? null,
                    'balance' => 0,
                ];
            })->toArray();
    }

    public function expenseCreditNoteTransaction($transaction, $ledgerId)
    {
        return $transaction->addLess
            ->where('ledger_id', $ledgerId)
            ->map(function ($addLess) use ($transaction) {
                $debit = 0;
                $credit = 0;
                if (IsNegative::run($addLess->total)) {
                    $credit = abs($addLess->total);
                } else {
                    $debit = $addLess->total;
                }

                return [
                    'transaction_id' => $transaction->id,
                    'date' => $transaction->voucher_date->format('d-m-y'),
                    'ledger_name' => $transaction->supplier->name,
                    'transaction_type' => 'Purchase Return',
                    'voucher_no' => $transaction->voucher_number,
                    'invoice_no' => '',
                    'debit_amount' => $debit,
                    'credit_amount' => $credit,
                    'narration' => $transaction->narration ?? null,
                    'balance' => 0,
                ];
            })->toArray();
    }
}
