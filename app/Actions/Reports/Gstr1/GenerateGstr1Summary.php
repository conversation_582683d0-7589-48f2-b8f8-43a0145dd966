<?php

namespace App\Actions\Reports\Gstr1;

use App\Models\GstrLogin;
use App\Services\FileGstService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateGstr1Summary
{
    use AsAction;

    /** @var FileGstService */
    public mixed $fileGstService;

    public function __construct()
    {
        $this->fileGstService = new FileGstService();
    }

    public function handle($input)
    {
        $gstLogin = GstrLogin::first();
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;
        $filingPeriod = ! empty($input['end_date']) ? Carbon::parse($input['end_date'])->format('mY') : null;
        $statecd = substr($gstin, 0, 2);

        $headers = [
            'gstin' => $gstin,
            'username' => $gstLogin->username,
            'otp' => $gstLogin->otp,
            'statecd' => $statecd,
            'retperiod' => $filingPeriod,
        ];

        $fileGst = $this->fileGstService;

        $data = $fileGst->generateGstr1Summary($headers);

        Log::channel('gstr1')->alert('GSTR1Summary Generate', [$data]);

        return $data;
    }
}
