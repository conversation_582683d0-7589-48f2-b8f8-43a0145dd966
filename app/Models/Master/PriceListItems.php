<?php

namespace App\Models\Master;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PriceListItems extends Model
{
    use HasFactory;

    protected $fillable = [
        'item_id',
        'price_list_id',
        'mrp',
        'selling_price_without_gst',
        'selling_price_with_gst',
        'discount',
    ];

    /** @return BelongsTo<ItemMaster> */
    public function itemMaster(): BelongsTo
    {
        return $this->belongsTo(ItemMaster::class, 'item_id', 'id');
    }
}
