<?php

namespace App\Models\Master;

use App\Models\PriceList;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * App\Models\Master\PriceListGroups
 *
 * @property int $id
 * @property int $model_id
 * @property string $model_type
 * @property int|null $price_list_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|PriceListGroups newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PriceListGroups newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PriceListGroups query()
 * @method static \Illuminate\Database\Eloquent\Builder|PriceListGroups whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PriceListGroups whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PriceListGroups whereModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PriceListGroups whereModelType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PriceListGroups wherePriceListId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PriceListGroups whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class PriceListGroups extends Model
{
    use HasFactory;

    protected $fillable = [
        'model_id',
        'model_type',
        'price_list_id',
        'is_item_group',
    ];

    /** @return BelongsTo<PriceList> */
    public function priceList(): BelongsTo
    {
        return $this->belongsTo(PriceList::class, 'price_list_id', 'id');
    }

    /** @return MorphTo */
    public function model(): MorphTo
    {
        return $this->morphTo();
    }
}
