<?php

namespace App\Models;

use App\Actions\CommonAction\GstCalculateAction;
use App\Actions\CommonAction\IsNegative;
use App\Actions\ItemMaster\GetItemClosingStock;
use App\Actions\ItemMaster\GetOpeningStockRepo;
use App\Actions\Ledger\GetLedgerClosingBalanceAndType;
use App\Models\Master\Broker;
use App\Models\Master\Transport;
use App\Traits\HasCompany;
use App\Traits\HasDeleted;
use App\Traits\HasJsonResourcefulData;
use App\Traits\HasRecurring;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * App\Models\SaleTransaction
 *
 * @property int $id
 * @property int $company_id
 * @property int|null $payment_mode
 * @property int|null $payment_type_ledger_id
 * @property string|null $full_invoice_number
 * @property string|null $invoice_number
 * @property Carbon $date
 * @property int $customer_ledger_id
 * @property string|null $gstin
 * @property int|null $broker_id
 * @property float|null $brokerage_for_sale
 * @property int|null $brokerage_on_value_type
 * @property int|null $credit_period
 * @property int|null $credit_period_type
 * @property int|null $transport_id
 * @property int $billing_state_id
 * @property string|null $transporter_document_number
 * @property Carbon|null $transporter_document_date
 * @property string|null $po_no
 * @property Carbon|null $po_date
 * @property int $sales_item_type
 * @property float|null $shipping_freight
 * @property float|null $packing_charge
 * @property int|null $tcs_tax_id
 * @property float|null $tcs_rate
 * @property float|null $tcs_amount
 * @property float|null $cgst
 * @property float|null $sgst
 * @property float|null $igst
 * @property float $shipping_freight_with_gst
 * @property float $packing_charge_with_gst
 * @property float|null $shipping_freight_sgst_amount
 * @property float|null $shipping_freight_cgst_amount
 * @property float|null $shipping_freight_igst_amount
 * @property float|null $packing_charge_sgst_amount
 * @property float|null $packing_charge_cgst_amount
 * @property float|null $packing_charge_igst_amount
 * @property float|null $rounding_amount
 * @property float|null $total
 * @property float $grand_total
 * @property string|null $narration
 * @property string|null $term_and_condition
 * @property int $is_gst_enabled
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property float $cess
 * @property float|null $cess_amount
 * @property int $is_consolidating_items_to_invoice
 * @property int|null $credit_limit_period
 * @property-read string|null $paymentStatus
 * @property-read Collection|Address[] $addresses
 * @property-read int|null $addresses_count
 * @property-read Collection|IncomeCreditNoteTransaction[] $creditNote
 * @property-read int|null $credit_note_count
 * @property-read Ledger $customer
 * @property-read Collection|PaymentTransactionItem[] $paymentTransactionItem
 * @property-read int|null $payment_transaction_item_count
 * @property-read Collection|ReceiptTransactionItem[] $receiptTransactionItem
 * @property-read int|null $receipt_transaction_item_count
 * @property-read Collection|SaleTransactionItem[] $saleItems
 * @property-read int|null $sale_items_count
 * @property-read Collection|SaleTransactionLedger[] $saleLedgers
 * @property-read int|null $sale_ledgers_count
 * @property-read Collection|SaleReturnTransaction[] $saleReturn
 * @property-read int|null $sale_return_count
 * @property-read Ledger|null $tcsLedger
 * @property-read Transport|null $transport
 * @property-read Broker|null $brokerDetails
 * @property-read Address|null $shippingAddress
 * @property-read Address|null $billingAddress
 *
 * @method static Builder|SaleTransaction newModelQuery()
 * @method static Builder|SaleTransaction newQuery()
 * @method static Builder|SaleTransaction query()
 * @method static Builder|SaleTransaction whereBrokerId($value)
 * @method static Builder|SaleTransaction whereBrokerageForSale($value)
 * @method static Builder|SaleTransaction whereBrokerageOnValueType($value)
 * @method static Builder|SaleTransaction whereCess($value)
 * @method static Builder|SaleTransaction whereCgst($value)
 * @method static Builder|SaleTransaction whereCompanyId($value)
 * @method static Builder|SaleTransaction whereCreatedAt($value)
 * @method static Builder|SaleTransaction whereCreditPeriod($value)
 * @method static Builder|SaleTransaction whereCreditPeriodType($value)
 * @method static Builder|SaleTransaction whereCustomerLedgerId($value)
 * @method static Builder|SaleTransaction whereDate($value)
 * @method static Builder|SaleTransaction whereFullInvoiceNumber($value)
 * @method static Builder|SaleTransaction whereGrandTotal($value)
 * @method static Builder|SaleTransaction whereGstin($value)
 * @method static Builder|SaleTransaction whereId($value)
 * @method static Builder|SaleTransaction whereIgst($value)
 * @method static Builder|SaleTransaction whereInvoiceNumber($value)
 * @method static Builder|SaleTransaction whereIsGstEnabled($value)
 * @method static Builder|SaleTransaction whereNarration($value)
 * @method static Builder|SaleTransaction wherePackingCharge($value)
 * @method static Builder|SaleTransaction wherePackingChargeCgstAmount($value)
 * @method static Builder|SaleTransaction wherePackingChargeIgstAmount($value)
 * @method static Builder|SaleTransaction wherePackingChargeSgstAmount($value)
 * @method static Builder|SaleTransaction wherePackingChargeWithGst($value)
 * @method static Builder|SaleTransaction wherePaymentMode($value)
 * @method static Builder|SaleTransaction wherePaymentTypeLedgerId($value)
 * @method static Builder|SaleTransaction wherePoDate($value)
 * @method static Builder|SaleTransaction wherePoNo($value)
 * @method static Builder|SaleTransaction whereRoundingAmount($value)
 * @method static Builder|SaleTransaction whereSalesItemType($value)
 * @method static Builder|SaleTransaction whereSgst($value)
 * @method static Builder|SaleTransaction whereShippingFreight($value)
 * @method static Builder|SaleTransaction whereShippingFreightCgstAmount($value)
 * @method static Builder|SaleTransaction whereShippingFreightIgstAmount($value)
 * @method static Builder|SaleTransaction whereShippingFreightSgstAmount($value)
 * @method static Builder|SaleTransaction whereShippingFreightWithGst($value)
 * @method static Builder|SaleTransaction whereTcsAmount($value)
 * @method static Builder|SaleTransaction whereTcsRate($value)
 * @method static Builder|SaleTransaction whereTcsTaxId($value)
 * @method static Builder|SaleTransaction whereTermAndCondition($value)
 * @method static Builder|SaleTransaction whereTotal($value)
 * @method static Builder|SaleTransaction whereTransportId($value)
 * @method static Builder|SaleTransaction whereTransporterDocumentDate($value)
 * @method static Builder|SaleTransaction whereTransporterDocumentNumber($value)
 * @method static Builder|SaleTransaction whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property int $is_cgst_sgst_igst_calculated
 * @property-read array|string $invoice_attachment
 * @property-read Collection|JournalCreditCustomerTransaction[] $journalTransactionItem
 * @property-read int|null $journal_transaction_item_count
 * @property-read MediaCollection|Media[] $media
 * @property-read int|null $media_count
 *
 * @method static Builder|SaleTransaction whereBillingStateId($value)
 * @method static Builder|SaleTransaction whereIsCgstSgstIgstCalculated($value)
 * @method static Builder|SaleTransaction withoutAppends()
 *
 * @property int $is_gst_na
 *
 * @method static Builder|SaleTransaction whereIsGstNa($value)
 *
 * @property int|null $created_by
 * @property string|null $credit_period_due_date
 * @property string|null $transporter_vehicle_number
 * @property float|null $taxable_value
 * @property string|null $payment_status
 * @property float|null $due_amount
 * @property int $via_api
 * @property string|null $estimate_quote_no
 * @property string|null $delivery_challan_no
 * @property int|null $is_import
 * @property string|null $shipping_gstin
 * @property string|null $shipping_name
 * @property-read \App\Models\Company $company
 * @property-read \App\Models\User|null $createdBy
 * @property-read array|string $sale_file
 * @property-read \App\Models\IncomeEstimateQuoteTransaction|null $incomeEstimateQuoteTransaction
 * @property-read \App\Models\Ledger|null $paymentTypeLedger
 *
 * @method static \Database\Factories\SaleTransactionFactory factory($count = null, $state = [])
 * @method static Builder|SaleTransaction financialYearDate()
 * @method static Builder|SaleTransaction whereCreatedBy($value)
 * @method static Builder|SaleTransaction whereCreditPeriodDueDate($value)
 * @method static Builder|SaleTransaction whereDueAmount($value)
 * @method static Builder|SaleTransaction whereEstimateQuoteNo($value)
 * @method static Builder|SaleTransaction whereIsImport($value)
 * @method static Builder|SaleTransaction wherePaymentStatus($value)
 * @method static Builder|SaleTransaction whereShippingGstin($value)
 * @method static Builder|SaleTransaction whereShippingName($value)
 * @method static Builder|SaleTransaction whereTaxableValue($value)
 * @method static Builder|SaleTransaction whereTransporterVehicleNumber($value)
 * @method static Builder|SaleTransaction whereViaApi($value)
 *
 * @property string|null $next_sent_at_reminder
 * @property string|null $eway_bill_number
 * @property string|null $eway_bill_date
 * @property int|null $tds_tax_id
 * @property float|null $tds_rate
 * @property float|null $tds_amount
 * @property float|null $gross_value
 * @property int|null $dispatch_address_id
 * @property int|null $deleted_by
 * @property Carbon|null $deleted_at
 * @property int|null $is_round_off_not_changed
 * @property-read Collection<int, \App\Models\AddLessForSalesTransaction> $addLess
 * @property-read int|null $add_less_count
 * @property-read Collection<int, \App\Models\AdditionalChargesForSalesTransaction> $additionalCharges
 * @property-read int|null $additional_charges_count
 * @property-read Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @property-read \App\Models\User|null $deletedBy
 * @property-read \App\Models\Address|null $dispatchAddress
 * @property-read Collection<int, \App\Models\PaymentDetailsForSalesTransaction> $paymentDetails
 * @property-read int|null $payment_details_count
 * @property-read \App\Models\Ledger|null $tdsLedger
 * @property-read float $paid_amount
 *
 * @method static Builder|SaleTransaction onlyTrashed()
 * @method static Builder|SaleTransaction sorting($sorting)
 * @method static Builder|SaleTransaction whereDeletedAt($value)
 * @method static Builder|SaleTransaction whereDeletedBy($value)
 * @method static Builder|SaleTransaction whereDeliveryChallanNo($value)
 * @method static Builder|SaleTransaction whereDispatchAddressId($value)
 * @method static Builder|SaleTransaction whereEwayBillDate($value)
 * @method static Builder|SaleTransaction whereEwayBillNumber($value)
 * @method static Builder|SaleTransaction whereGrossValue($value)
 * @method static Builder|SaleTransaction whereIsRoundOffNotChanged($value)
 * @method static Builder|SaleTransaction whereNextSentAtReminder($value)
 * @method static Builder|SaleTransaction whereTdsAmount($value)
 * @method static Builder|SaleTransaction whereTdsRate($value)
 * @method static Builder|SaleTransaction whereTdsTaxId($value)
 * @method static Builder|SaleTransaction withTrashed()
 * @method static Builder|SaleTransaction withoutTrashed()
 *
 * @mixin \Eloquent
 */
class SaleTransaction extends Model implements Auditable, HasMedia
{
    use HasCompany, HasFactory, HasJsonResourcefulData, InteractsWithMedia;
    use HasDeleted, HasRecurring, SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    /**
     * Attributes to exclude from the Audit.
     *
     * @var array
     */
    protected $auditExclude = [
        'taxable_value',
        'payment_status',
        'due_amount',
        'deleted_by',
    ];

    public const SALE_DOCUMENT = 'sale_document';

    public const CASH = 'Cash';

    public const CREDIT = 'Credit';

    public const CASH_MODE = 1;

    public const CREDIT_MODE = 2;

    public const PAYMENT_MODE = [
        self::CASH_MODE => 'Cash',
        self::CREDIT_MODE => 'Credit',
    ];

    public const BROKERAGE_ON_INVOICE_VALUE = 1;

    public const BROKERAGE_ON_TAXABLE_VALUE = 2;

    public const BROKERAGE_ON_GROSS_VALUE = 3;

    public const BROKERAGE_ON_VALUE_TYPE = [
        self::BROKERAGE_ON_INVOICE_VALUE => 'Invoice Value',
        self::BROKERAGE_ON_TAXABLE_VALUE => 'Taxable Value',
        self::BROKERAGE_ON_GROSS_VALUE => 'Gross Value',
    ];

    public const CREDIT_PERIOD_TYPE_MONTH = 1;

    public const CREDIT_PERIOD_TYPE_DAY = 2;

    public const CREDIT_PERIOD_TYPE_VALUE = [
        'Month' => self::CREDIT_PERIOD_TYPE_MONTH,
        'Day' => self::CREDIT_PERIOD_TYPE_DAY,
    ];

    public const CREDIT_PERIOD_TYPE = [
        self::CREDIT_PERIOD_TYPE_DAY => 'Days',
        self::CREDIT_PERIOD_TYPE_MONTH => 'Month',
    ];

    public const ACCOUNTING_INVOICE = 1;

    public const ITEM_INVOICE = 2;

    public const SALE_TRANSACTION_TYPE = [
        self::ACCOUNTING_INVOICE => 'Accounting Invoice',
        self::ITEM_INVOICE => 'Item Invoice',
    ];

    public const BILLING_ADDRESS = 1;

    public const SHIPPING_ADDRESS = 2;

    public const DISPATCH_ADDRESS = 3;

    public const DISCOUNT_TYPE_AMOUNT = 1;

    public const DISCOUNT_TYPE_PERCENTAGE = 2;

    public const DISCOUNT_TYPES = [
        self::DISCOUNT_TYPE_AMOUNT => '₹',
        self::DISCOUNT_TYPE_PERCENTAGE => '%',
    ];

    public const REVERSE_CHARGE_APPLICABLE_YES = 1;

    public const REVERSE_CHARGE_APPLICABLE_NO = 0;

    public const REVERSE_CHARGE_APPLICABLE_TYPE = [
        self::REVERSE_CHARGE_APPLICABLE_YES => 'Yes',
        self::REVERSE_CHARGE_APPLICABLE_NO => 'No',
    ];

    public const SUB_SUPPLY_TYPE_SUPPLY = 1;

    public const SUB_SUPPLY_TYPE_IMPORT = 2;

    public const SUB_SUPPLY_TYPE_SKD = 3;

    public const SUB_SUPPLY_TYPE_JOB_WORK_RETURN = 4;

    public const SUB_SUPPLY_TYPE_SALE_RETURN = 5;

    public const SUB_SUPPLY_TYPE_FAIRS = 6;

    public const SUB_SUPPLY_TYPE_FOR_OWN_USE = 7;

    public const SUB_SUPPLY_TYPE_OTHER = 8;

    public const E_BILL_TRANS_TYPE_REGULAR = 1;

    public const E_BILL_TRANS_TYPE_BILL_TO_SHIP_TO = 2;

    public const E_BILL_TRANS_TYPE_BILL_FROM_DISPATCH_TO = 3;

    public const E_BILL_TRANS_TYPE_COMBINATION = 4;

    public const E_BILL_TRANSACTION_TYPE_ARR = [
        self::E_BILL_TRANS_TYPE_REGULAR => 'Regular',
        self::E_BILL_TRANS_TYPE_BILL_TO_SHIP_TO => 'Bill To - Ship To',
        self::E_BILL_TRANS_TYPE_BILL_FROM_DISPATCH_TO => 'Bill From - Dispatch From',
        self::E_BILL_TRANS_TYPE_COMBINATION => 'Combination of 2 & 3',
    ];

    public const TRANSPORTER_MODE_ROAD = 1;

    public const TRANSPORTER_MODE_RAIL = 2;

    public const TRANSPORTER_MODE_AIR = 3;

    public const TRANSPORTER_MODE_SHIP = 4;

    public const TRANSPORTER_MODE = [
        self::TRANSPORTER_MODE_ROAD => 'Road',
        self::TRANSPORTER_MODE_RAIL => 'Rail',
        self::TRANSPORTER_MODE_AIR => 'Air',
        self::TRANSPORTER_MODE_SHIP => 'Ship or Ship Cum Road/Rail',
    ];

    public const VEHICLE_TYPE_REGULAR = 'R';

    public const VEHICLE_TYPE_OVER_DIMENSIONAL_CARGO = 'O';

    public const SAVE_BUTTON = 1;

    public const SAVE_AND_NEW_BUTTON = 2;

    public const SAVE_AND_PRINT_BUTTON = 3;

    public const PAYMENT_STATUS_ARR = [
        self::PAYMENT_STATUS_PAID => self::PAYMENT_STATUS_PAID,
        self::PAYMENT_STATUS_UNPAID => self::PAYMENT_STATUS_UNPAID,
        self::PAYMENT_STATUS_PARTLY_UNPAID => self::PAYMENT_STATUS_PARTLY_UNPAID,
    ];

    public const SALE_TRANSACTION = 'Sale';

    public const SALE_RETURN_TRANSACTION = 'Sale Return';

    public const TYPE_OF_TRANSACTION = 'Sale';

    public const INCOME_DEBIT_NOTE_TRANSACTION = 'Income Dr. Note';

    public const INCOME_CREDIT_NOTE_TRANSACTION = 'Income Cr. Note';

    public const PAYMENT_STATUS_PAID = 'Paid';

    public const PAYMENT_STATUS_UNPAID = 'Unpaid';

    public const PAYMENT_STATUS_PARTLY_UNPAID = 'Partially Unpaid';

    public const REPORT_SUMMARY = 1;

    public const REPORT_DETAIL = 2;

    public const PAID = 1;

    public const UNPAID = 2;

    public const PARTLY_UNPAID = 3;

    public const PAYMENT_STATUS = [
        self::PAID => 'Paid',
        self::UNPAID => 'Unpaid',
        self::PARTLY_UNPAID => 'Partially Unpaid',
    ];

    public const TRANSACTION_TYPE = 'saleTransaction';

    public $table = 'sales_transactions';

    public const TYPE = 0;

    public const SALES = 1;

    public const SALES_RETURN = 2;

    public const DEBIT_NOTES = 3;

    public const CREDIT_NOTES = 4;

    public const TRANSACTION_TYPE_NAME = [
        self::TYPE => 'Type',
        self::SALES => 'Sale',
        self::SALES_RETURN => 'Sale Return',
        self::DEBIT_NOTES => 'Income Dr. Note',
        self::CREDIT_NOTES => 'Income Cr. Note',
    ];

    public const TAX_INVOICE = 'INV';

    public const BILL_OF_SUPPLY = 'BIL';

    public const BILL_OF_ENTRY = 'BOE';

    public const DELIVERY_CHALLAN = 'CHL';

    public const OTHERS = 'OTH';

    public const DOCUMENT_TYPE = [
        self::TAX_INVOICE => 'Tax Invoice',
        self::BILL_OF_SUPPLY => 'Bill of supply',
        self::BILL_OF_ENTRY => 'Bill of Entry',
        self::DELIVERY_CHALLAN => 'Delivery Challan',
        self::OTHERS => 'Others',
    ];

    public const OUTWARD = 0;

    public const INWARD = 1;

    public const JAMMU_AND_KASHMIR = '01';                          // 4029

    public const HIMACHAL_PRADESH = '02';                           // 4020

    public const PUNJAB = '03';                                     // 4015

    public const CHANDIGARH = '04';                                 // 4031

    public const UTTARAKHAND = '05';                                // 4016

    public const HARYANA = '06';                                    // 4007

    public const DELHI = '07';                                      // 4021

    public const RAJASTHAN = '08';                                  // 4014

    public const UTTAR_PRADESH = '09';                              // 4022

    public const BIHAR = '10';                                      // 4037

    public const SIKKIM = '11';                                     // 4034

    public const ARUNACHAL_PRADESH = '12';                          // 4024

    public const NAGALAND = '13';                                   // 4018

    public const MANIPUR = '14';                                    // 4010

    public const MIZORAM = '15';                                    // 4036

    public const TRIPURA = '16';                                    // 4038

    public const MEGHALAYA = '17';                                  // 4006

    public const ASSAM = '18';                                      // 4027

    public const WEST_BENGAL = '19';                                // 4853

    public const JHARKHAND = '20';                                  // 4025

    public const ODISHA = '21';                                     // 4013

    public const CHHATTISGARH = '22';                               // 4040

    public const MADHYA_PRADESH = '23';                             // 4039

    public const GUJARAT = '24';                                    // 4030

    public const DAMAN_DIU = '25';                                  // Not save state database

    public const DADRA_AND_NAGAR_HAVELI_AND_DAMAN_AND_DIU = '26';   // 4033

    public const MAHARASHTRA = '27';                                // 4008

    public const ANDHRA_PRADESH = '37';                             // 4017

    public const KARNATAKA = '29';                                  // 4026

    public const GOA = '30';                                        // 4009

    public const LAKSHADWEEP = '31';                                // 4019

    public const KERALA = '32';                                     // 4028

    public const TAMIL_NADU = '33';                                 // 4035

    public const PUDUCHERRY = '34';                                 // 4011

    public const ANDAMAN_AND_NICOBAR_ISLANDS = '35';                // 4023

    public const TELANGANA = '36';                                  // 4012

    public const ANDHRA_PRADESH_NEW = '37';                         // 4017

    public const LADAKH = '38';                                     // 4852

    public const OTHER_TERRITORY = '97';                            // -----

    public const CENTRE_JURISDICTION = '99';                        // -----

    public const STATE_CODE = [
        4006 => self::MEGHALAYA,
        4007 => self::HARYANA,
        4008 => self::MAHARASHTRA,
        4009 => self::GOA,
        4010 => self::MANIPUR,
        4011 => self::PUDUCHERRY,
        4012 => self::TELANGANA,
        4013 => self::ODISHA,
        4014 => self::RAJASTHAN,
        4015 => self::PUNJAB,
        4016 => self::UTTARAKHAND,
        4017 => self::ANDHRA_PRADESH,
        4018 => self::NAGALAND,
        4019 => self::LAKSHADWEEP,
        4020 => self::HIMACHAL_PRADESH,
        4021 => self::DELHI,
        4022 => self::UTTAR_PRADESH,
        4023 => self::ANDAMAN_AND_NICOBAR_ISLANDS,
        4024 => self::ARUNACHAL_PRADESH,
        4025 => self::JHARKHAND,
        4026 => self::KARNATAKA,
        4027 => self::ASSAM,
        4028 => self::KERALA,
        4029 => self::JAMMU_AND_KASHMIR,
        4030 => self::GUJARAT,
        4031 => self::CHANDIGARH,
        4033 => self::DADRA_AND_NAGAR_HAVELI_AND_DAMAN_AND_DIU,
        4034 => self::SIKKIM,
        4035 => self::TAMIL_NADU,
        4036 => self::MIZORAM,
        4037 => self::BIHAR,
        4038 => self::TRIPURA,
        4039 => self::MADHYA_PRADESH,
        4040 => self::CHHATTISGARH,
        4853 => self::WEST_BENGAL,
        4852 => self::LADAKH,
    ];

    public const INVOICE_ATTACHMENT = 'invoice_attachment';

    public $fillable = [
        'company_id',
        'payment_mode',
        'full_invoice_number',
        'invoice_number',
        'date',
        'customer_ledger_id',
        'party_phone_number',
        'region_iso',
        'region_code',
        'gstin',
        'broker_id',
        'brokerage_for_sale',
        'brokerage_on_value_type',
        'credit_period',
        'credit_period_type',
        'transport_id',
        'transporter_document_number',
        'transporter_document_date',
        'transporter_vehicle_number',
        'eway_bill_number',
        'eway_bill_date',
        'po_no',
        'po_date',
        'sales_item_type',
        'shipping_freight',
        'packing_charge',
        'tcs_rate',
        'tcs_amount',
        'cgst',
        'sgst',
        'igst',
        'rounding_amount',
        'total',
        'grand_total',
        'narration',
        'term_and_condition',
        'dispatch_address_id',
        'shipping_address_id',
        'same_as_billing',
        'tcs_tax_id',
        'tds_tax_id',
        'tds_rate',
        'tds_amount',
        'payment_type_ledger_id',
        'is_gst_enabled',
        'cess',
        'shipping_freight_with_gst',
        'shipping_gstin',
        'address_name',
        'party_name_same_as_address_name',
        'shipping_name',
        'packing_charge_with_gst',
        'shipping_freight_sgst_amount',
        'shipping_freight_cgst_amount',
        'shipping_freight_igst_amount',
        'packing_charge_sgst_amount',
        'packing_charge_cgst_amount',
        'packing_charge_igst_amount',
        'billing_state_id',
        'is_cgst_sgst_igst_calculated',
        'is_gst_na',
        'created_by',
        'taxable_value',
        'gross_value',
        'payment_status',
        'due_amount',
        'via_api',
        'is_import',
        'estimate_quote_no',
        'credit_period_due_date',
        'next_sent_at_reminder',
        'deleted_by',
        'delivery_challan_no',
        'is_round_off_not_changed',
        'created_at',
        'updated_at',
        'is_recurring_approve',
        'template_id',
        'recurring_rejected_at',
        'vastra_delivery_challan_id',
        'round_off_method',
        'bank_id',
    ];

    public $casts = [
        'payment_mode' => 'integer',
        'date' => 'date',
        'brokerage_on_value_type' => 'integer',
        'credit_period' => 'integer',
        'credit_period_type' => 'integer',
        'sales_item_type' => 'integer',
        'po_date' => 'date',
        'brokerage_for_sale' => 'double',
        'shipping_freight' => 'double',
        'packing_charge' => 'double',
        'tcs_rate' => 'double',
        'tcs_amount' => 'double',
        'tds_rate' => 'double',
        'tds_amount' => 'double',
        'cgst' => 'double',
        'sgst' => 'double',
        'igst' => 'double',
        'rounding_amount' => 'double',
        'total' => 'double',
        'grand_total' => 'double',
        'transporter_document_date' => 'date',
        'is_gst_enabled' => 'integer',
        'cess' => 'double',
        'shipping_freight_with_gst' => 'double',
        'packing_charge_with_gst' => 'double',
        'shipping_freight_sgst_amount' => 'double',
        'shipping_freight_cgst_amount' => 'double',
        'shipping_freight_igst_amount' => 'double',
        'packing_charge_sgst_amount' => 'double',
        'packing_charge_cgst_amount' => 'double',
        'packing_charge_igst_amount' => 'double',
        'taxable_value' => 'double',
        'gross_value' => 'double',
        'due_amount' => 'double',
        'party_name_same_as_address_name' => 'boolean',
        'same_as_billing' => 'boolean',
        'recurring_rejected_at' => 'date',
        'round_off_method' => 'integer',
        'bank_id' => 'integer',
    ];

    // public $appends = ['taxable_value', 'payment_status', 'due_amount'];

    public $appends = ['paid_amount'];

    public static bool $withoutAppends = true;

    public function scopeWithoutAppends($query)
    {
        self::$withoutAppends = false;

        return $query;
    }

    public function getSaleFileAttribute(): array|string
    {

        $mediaUrl = [];
        /** @var Media $medias */
        $medias = $this->getMedia(self::SALE_DOCUMENT);

        foreach ($medias as $media) {
            $mediaUrl[] = $media->getFullUrl();
        }

        if (! empty($mediaUrl)) {
            return $mediaUrl;
        }

        return '';
    }

    public function getInvoiceAttachmentAttribute(): array|string
    {
        $mediaUrl = [];
        /** @var Media $medias */
        $medias = $this->getMedia(self::INVOICE_ATTACHMENT);

        foreach ($medias as $media) {
            $mediaUrl[] = $media->getFullUrl();
        }

        if (! empty($mediaUrl)) {
            return $mediaUrl;
        }

        return '';
    }

    protected function getArrayableAppends()
    {
        if (self::$withoutAppends) {
            return $this->appends;
        }

        return parent::getArrayableAppends();
    }

    public function transformAudit(array $data): array
    {
        if ($data['event'] == 'created') {
            Arr::set($data, 'title', '<b>Sale</b> Invoice <b>'.$this->getAttribute('full_invoice_number').'</b> was created.');
        } elseif ($data['event'] == 'updated') {
            Arr::set($data, 'title', '<b>Sale</b> Invoice <b>'.$this->getAttribute('full_invoice_number').'</b> was edited.');
        } elseif ($data['event'] == 'deleted') {
            if (checkThisDeleted($data) == 'hard-delete' || checkThisDeleted($data) == 'bulk-delete' || checkThisDeleted($data) == 'empty-transaction' || checkThisDeleted($data) == 'empty-whole-bin') {
                Arr::set($data, 'title', '<b>Sale</b> Invoice <b>'.$this->getAttribute('full_invoice_number').'</b> was deleted from recycle bin.');
            } else {
                Arr::set($data, 'title', '<b>Sale</b> Invoice <b>'.$this->getAttribute('full_invoice_number').'</b> was moved to recycle bin.');
            }
        } elseif ($data['event'] == 'restored') {
            Arr::set($data, 'title', '<b>Sale</b> Invoice <b>'.$this->getAttribute('full_invoice_number').'</b> was restored from recycle bin.');
        }

        return $data;
    }

    public function saleItems(): HasMany
    {
        return $this->hasMany(SaleTransactionItem::class, 'sale_transactions_id');
    }

    public function saleLedgers(): HasMany
    {
        return $this->hasMany(SaleTransactionLedger::class, 'sale_transactions_id');
    }

    public function advancePayment(): MorphMany
    {
        return $this->morphMany(SettleAdvancePayment::class, 'model');
    }

    public function settleAdvancePayment(): HasMany
    {
        return $this->hasMany(SettleAdvancePayment::class, 'transaction_id', 'id')
            ->where('transaction_type', SettleAdvancePayment::SALE);
    }

    /** @return BelongsTo<Company> */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'customer_ledger_id', 'id');
    }

    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'model');
    }

    public function billingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::BILLING_ADDRESS);
    }

    public function shippingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::SHIPPING_ADDRESS);
    }

    public function dispatchAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'dispatch_address_id', 'id');
    }

    public function transport(): BelongsTo
    {
        return $this->belongsTo(Transport::class, 'transport_id', 'id');
    }

    public function brokerDetails(): BelongsTo
    {
        return $this->belongsTo(Broker::class, 'broker_id', 'id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function tcsLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'tcs_tax_id', 'id');
    }

    public function tdsLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'tds_tax_id', 'id');
    }

    public function paymentTypeLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'payment_type_ledger_id', 'id');
    }

    public function recurringInvoice(): BelongsTo
    {
        return $this->belongsTo(RecurringInvoice::class, 'template_id', 'id');
    }

    public function customFieldValues(): MorphMany
    {
        return $this->morphMany(TransactionCustomFieldValue::class, 'model');
    }

    public function bankLedgerDetails(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'bank_id', 'id');
    }

    public function getDayBookData(): array
    {
        return [
            'date' => $this->date->format('d-m-Y'),
            'particulars' => $this->customer->name,
            'voucher_type' => 'Sale',
            'voucher_number' => $this->full_invoice_number,
            'debit_amount' => $this->grand_total,
            'credit_amount' => 0,
            'narration' => $this->narration,
            'ledger_id' => $this->customer_ledger_id,
        ];
    }

    public function receiptTransactionItem(): HasMany
    {
        return $this->hasMany(ReceiptTransactionItem::class, 'sale_id', 'id');
    }

    public function journalTransactionItem(): HasMany
    {
        return $this->hasMany(JournalCreditCustomerTransaction::class, 'sale_id', 'id');
    }

    public function paymentTransactionItem(): HasMany
    {
        return $this->hasMany(PaymentTransactionItem::class, 'sale_id', 'id');
    }

    protected function getTaxableValue(): Attribute
    {
        $taxableValue = 0;
        if ($this->sales_item_type == self::ACCOUNTING_INVOICE) {
            foreach ($this->saleLedgers as $saleLedgers) {
                $taxableValue += (float) $saleLedgers->rpu_without_gst - (float) $saleLedgers->total_discount_amount;
            }
        }

        if ($this->sales_item_type == self::ITEM_INVOICE) {
            foreach ($this->saleItems as $saleItem) {
                $taxableValue += ((float) $saleItem->quantity * (float) $saleItem->rpu_without_gst) - (float) $saleItem->total_discount_amount;
            }
        }
        if (! empty($this->shipping_freight) && $this->shipping_freight > 0) {
            $taxableValue += $this->shipping_freight;
        }

        if (! empty($this->packing_charge) && $this->packing_charge > 0) {
            $taxableValue += $this->packing_charge;
        }

        return Attribute::make(
            get: fn ($value) => $taxableValue,
        );
    }

    public function saleReturn(): HasMany
    {
        return $this->hasMany(SaleReturnTransaction::class, 'original_inv_no', 'id');
    }

    public function creditNote(): HasMany
    {
        return $this->hasMany(IncomeCreditNoteTransaction::class, 'original_inv_no', 'id');
    }

    protected function getDueAmount(): Attribute
    {
        // if ($this->payment_mode == self::CASH_MODE) {
        //     return Attribute::make(
        //         get: static fn ($value) => 0.00,
        //     );
        // }

        $validReceiptTransactionItems = $this->receiptTransactionItem->filter(function ($item) {
            return $item->receiptTransaction && ! $item->receiptTransaction->trashed();
        });

        $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
            return $item->journalTransaction && ! $item->journalTransaction->trashed();
        });

        $receivedAmount = (
            ($validReceiptTransactionItems->sum('received_amount') ?? 0)
            + ($validReceiptTransactionItems->sum('discount') ?? 0)
            + ($validReceiptTransactionItems->sum('round_off') ?? 0)
            + ($validJournalTransactionItems->sum('received_amount') ?? 0)
            + ($validJournalTransactionItems->sum('discount') ?? 0)
            + ($validJournalTransactionItems->sum('round_off') ?? 0)
            + ($this->tds_amount ?? 0)
            + ($this->advancePayment->sum('adjusted_amount') ?? 0)
        );

        $receivedAmount = round($receivedAmount, getCompanyFixedDigitNumber());
        $grandTotal = round($this->grand_total, getCompanyFixedDigitNumber());
        $saleReturnAmount = round($this->saleReturn->sum('grand_total') ?? 0, getCompanyFixedDigitNumber());
        $creditNoteAmount = round($this->creditNote->sum('grand_total') ?? 0, getCompanyFixedDigitNumber());

        return Attribute::make(
            get: static fn ($value) => $grandTotal - ($receivedAmount + $saleReturnAmount + $creditNoteAmount),
        );
    }

    protected function paidAmount(): Attribute
    {
        if ($this->payment_mode == self::CASH_MODE) {
            $paidAmount = $this->grand_total;
        } else {
            $validReceiptTransactionItems = $this->receiptTransactionItem->filter(function ($item) {
                return $item->receiptTransaction && $item->deleted_at == null;
            });

            $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
                return $item->journalTransaction && $item->deleted_at == null;
            });

            $receivedAmount = (
                ($validReceiptTransactionItems->sum('received_amount') ?? 0)
                + ($validReceiptTransactionItems->sum('discount') ?? 0)
                + ($validReceiptTransactionItems->sum('round_off') ?? 0)
                + ($validJournalTransactionItems->sum('received_amount') ?? 0)
                + ($validJournalTransactionItems->sum('discount') ?? 0)
                + ($validJournalTransactionItems->sum('round_off') ?? 0)
                + ($this->tds_amount ?? 0)
                + ($this->advancePayment->sum('adjusted_amount') ?? 0)
            );

            $receivedAmount = round($receivedAmount, getCompanyFixedDigitNumber());
            $saleReturnAmount = round($this->saleReturn->sum('grand_total') ?? 0, getCompanyFixedDigitNumber());
            $creditNoteAmount = round($this->creditNote->sum('grand_total') ?? 0, getCompanyFixedDigitNumber());
            $paidAmount = $receivedAmount + $saleReturnAmount + $creditNoteAmount;
        }

        return Attribute::make(
            get: static fn ($value) => $paidAmount,
        );
    }

    protected function getPaymentStatus(): Attribute
    {
        // if ($this->payment_mode == self::CASH_MODE) {
        //     return Attribute::make(
        //         get: static fn ($value) => self::PAYMENT_STATUS_PAID,
        //     );
        // }

        $validReceiptTransactionItems = $this->receiptTransactionItem->filter(function ($item) {
            return $item->receiptTransaction && ! $item->receiptTransaction->trashed();
        });

        $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
            return $item->journalTransaction && ! $item->journalTransaction->trashed();
        });

        $receivedAmount = (
            ($validReceiptTransactionItems->sum('received_amount') ?? 0)
            + ($validReceiptTransactionItems->sum('discount') ?? 0)
            + ($validReceiptTransactionItems->sum('round_off') ?? 0)
            + ($validJournalTransactionItems->sum('received_amount') ?? 0)
            + ($validJournalTransactionItems->sum('discount') ?? 0)
            + ($validJournalTransactionItems->sum('round_off') ?? 0)
            + ($this->saleReturn->sum('grand_total') ?? 0)
            + ($this->creditNote->sum('grand_total') ?? 0)
            + ($this->tds_amount ?? 0)
            + ($this->advancePayment->sum('adjusted_amount') ?? 0)
        );

        $receivedAmount = round($receivedAmount, getCompanyFixedDigitNumber());
        $totalAmount = round($this->grand_total, getCompanyFixedDigitNumber());

        if ($receivedAmount >= $totalAmount) {
            $paymentStatus = self::PAYMENT_STATUS_PAID;
        } elseif (round($receivedAmount, 2) == 0) {
            $paymentStatus = self::PAYMENT_STATUS_UNPAID;
        } else {
            $paymentStatus = self::PAYMENT_STATUS_PARTLY_UNPAID;
        }

        return Attribute::make(
            get: static fn ($value) => $paymentStatus,
        );
    }

    public function getLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-Y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sales',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => $this->grand_total,
            'credit_amount' => 0,
            'balance' => 0,
            'data' => [],
        ];
    }

    public function getCgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sales',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->cgst,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getSgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sales',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->sgst,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getIgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sales',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->igst,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getTcsReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sale',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->tcs_amount,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getTdsLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sale',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => $this->tds_amount,
            'credit_amount' => 0,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getCustomerTdsLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->tdsLedger->name,
            'transaction_type' => 'Sale',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->tds_amount,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getCessReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sale',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->cess,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getPackingChargeReport(): array
    {
        $packingCharge = $this->packing_charge;

        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sales',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $packingCharge,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getFreightChargeReport(): array
    {
        $freightCharge = $this->shipping_freight;

        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sales',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $freightCharge,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getAdditionalChargeReport($value): array
    {
        $debit = 0;
        $credit = 0;
        if (IsNegative::run($value)) {
            $debit = abs($value);
        } else {
            $credit = $value;
        }

        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sales',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => $debit,
            'credit_amount' => $credit,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getRoundOffReport(): array
    {
        if ($this->rounding_amount < 0) {
            $roundAmount = abs($this->rounding_amount);
            $data = [
                'transaction_id' => $this->id,
                'date' => $this->date->format('d-m-y'),
                'ledger_name' => $this->customer->name,
                'transaction_type' => 'Sales',
                'voucher_no' => $this->full_invoice_number,
                'invoice_no' => '',
                'debit_amount' => $roundAmount,
                'credit_amount' => 0,
                'narration' => $this->narration ?? null,
                'balance' => 0,
            ];
        } else {
            $data = [
                'transaction_id' => $this->id,
                'date' => $this->date->format('d-m-y'),
                'ledger_name' => $this->customer->name,
                'transaction_type' => 'Sales',
                'voucher_no' => $this->full_invoice_number,
                'invoice_no' => '',
                'debit_amount' => 0,
                'credit_amount' => $this->rounding_amount,
                'balance' => 0,
            ];
        }

        return $data;
    }

    public function getCustomerLedgerReport(): array
    {
        $data = [];
        $ledgerName = '';
        $saleItems = $this->saleItems;
        if ($this->sales_item_type == self::ITEM_INVOICE && $saleItems->count() >= 1) {
            foreach ($saleItems as $saleItem) {
                // $saleItem?->load('ledger');
                if (empty($data)) {
                    // If $data is empty, just add the first saleItem
                    $data[] = [
                        'name' => $saleItem->ledger->name ?? '',
                        'amount' => $saleItem->total ?? 0,
                    ];
                } else {
                    $found = false; // Flag to check if the ledger name is found in $data
                    foreach ($data as &$item) {
                        if ($item['name'] == $saleItem->ledger->name) {
                            // If the ledger name matches, add the amount to existing data
                            $item['amount'] += $saleItem->total ?? 0;
                            $found = true;
                            break; // Exit the loop once found
                        }
                    }
                    if (! $found) {
                        // If the ledger name is not found in $data, add it as a new entry
                        $data[] = [
                            'name' => $saleItem->ledger->name ?? '',
                            'amount' => $saleItem->total ?? 0,
                        ];
                    }
                }
            }
            $ledger = $saleItems->firstWhere('ledger_id', '!=', null);
            // $ledger?->load('ledger');
            $ledgerName = $ledger->ledger->name ?? '';
        }
        $saleLedgers = $this->saleLedgers;
        if ($this->sales_item_type == self::ACCOUNTING_INVOICE && $saleLedgers->count() >= 1) {
            foreach ($saleLedgers as $saleLedger) {
                // $saleLedger?->load('ledgers');
                if (empty($data)) {
                    // If $data is empty, just add the first saleItem
                    $data[] = [
                        'name' => $saleLedger->ledgers->name ?? '',
                        'amount' => $saleLedger->total ?? 0,
                    ];
                } else {
                    $found = false; // Flag to check if the ledger name is found in $data
                    foreach ($data as &$item) {
                        if ($item['name'] == $saleLedger->ledgers->name) {
                            // If the ledger name matches, add the amount to existing data
                            $item['amount'] += $saleLedger->total ?? 0;
                            $found = true;
                            break; // Exit the loop once found
                        }
                    }
                    if (! $found) {
                        // If the ledger name is not found in $data, add it as a new entry
                        $data[] = [
                            'name' => $saleLedger->ledgers->name ?? '',
                            'amount' => $saleLedger->total ?? 0,
                        ];
                    }
                }
            }

            $ledger = $saleLedgers->where('ledger_id', '!=', null)->first();
            // $ledger?->load('ledgers');
            $ledgerName = $ledger->ledgers->name ?? '';
        }

        $data['ledger'] = $data;

        if (isCompanyGstApplicable()) {
            $data[] = [
                'name' => 'CGST',
                'amount' => $this->cgst,
            ];
            $data[] = [
                'name' => 'SGST',
                'amount' => $this->sgst,
            ];
            $data[] = [
                'name' => 'IGST',
                'amount' => $this->igst,
            ];
            $data[] = [
                'name' => 'Round Off',
                'amount' => $this->rounding_amount,
            ];
        }

        $additionalChargeAndAddless = [];
        foreach ($this->additionalCharges as $additionalCharge) {
            $additionalChargeAndAddless[] = [
                'name' => $additionalCharge->ledger->name ?? '',
                'amount' => $additionalCharge->total_without_tax,
            ];
        }

        foreach ($this->addLess as $addLess) {
            $additionalChargeAndAddless[] = [
                'name' => $addLess->ledger->name ?? '',
                'amount' => $addLess->total_without_tax,
            ];
        }

        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $ledgerName != '' ? $ledgerName : $this->customer->name,
            'transaction_type' => 'Sale',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => $this->grand_total,
            'credit_amount' => 0,
            'balance' => 0,
            'tcs_amount' => $this->tcs_amount ?? 0,
            'cgst' => $this->cgst ?? 0,
            'sgst' => $this->sgst ?? 0,
            'igst' => $this->igst ?? 0,
            'additional_charges_addless' => $additionalChargeAndAddless,
            'cess' => $this->cess ?? 0,
            'rounding_amount' => $this->rounding_amount ?? 0,
            'ledger_list' => $data['ledger'],
            'data' => $data,
            'narration' => $this->narration ?? null,
            'payment_status' => $this->payment_status ?? null,
            'transaction_item_list' => $this->transactionItemListAsString(),
        ];
    }

    public function transactionItemListAsString(): string
    {
        return $this->saleItems
            ->map(function ($transactionItem) {
                $itemName = $transactionItem->items?->item_name;
                $quantity = number_format($transactionItem->quantity, 2);
                $uom = $transactionItem->unit?->code ?? '';
                $totalDiscountAmount = number_format($transactionItem->total_discount_amount, 2);
                $rate = number_format($transactionItem->rpu_without_gst, 2);
                $total = number_format($transactionItem->total, 2);
                if ($totalDiscountAmount == 0) {
                    return "{$itemName}: {$quantity} {$uom} * {$rate} = {$total}";
                }

                return "{$itemName}: {$quantity} {$uom} * {$rate} - {$totalDiscountAmount} = {$total}";
            })
            ->filter()
            ->implode('<br>');
    }

    public function prepareShippingAmount()
    {
        $prepareData = [];
        $prepareNewData = [];

        if ($this->shipping_freight != 0 && empty($this->shipping_freight)) {
            return $prepareNewData;
        }

        if ($this->sales_item_type == self::ITEM_INVOICE) {
            $transactionItems = $this->saleItems;
            $totalTaxableAmount = $this->saleItems->sum('taxable_value') ?? 0;

            if ($transactionItems->count() >= 1) {
                foreach ($transactionItems as $saleItem) {
                    $shippingAmount = ($totalTaxableAmount != 0) ? (($saleItem->taxable_value * $this->shipping_freight) / $totalTaxableAmount) : 0;
                    $prepareData[] = [
                        'gst_tax' => $saleItem->gst_tax_percentage ?? 0,
                        'shipping_amount' => $shippingAmount,
                        'shipping_tax_amount' => ($shippingAmount * 18) / 100,
                    ];
                }
            }
        } else {
            $transactionLedgers = $this->saleLedgers;
            $totalTaxableAmount = $this->saleLedgers->sum('taxable_value') ?? 0;

            if ($transactionLedgers->count() >= 1) {
                foreach ($transactionLedgers as $saleLedgers) {
                    $shippingAmount = ($totalTaxableAmount != 0) ? (($saleLedgers->taxable_value * $this->shipping_freight) / $totalTaxableAmount) : 0;
                    $prepareData[] = [
                        'gst_tax' => $saleLedgers->gst_tax_percentage ?? 0,
                        'shipping_amount' => $shippingAmount,
                        'shipping_tax_amount' => ($shippingAmount * 18) / 100,
                    ];
                }
            }
        }
        if ($totalTaxableAmount == 0) {
            return $prepareNewData;
        }

        $gstTaxArray = [];
        $totalShippingAmount = 0;
        $totalShippingTaxAmount = 0;
        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalShippingAmount += $checkGstTax['shipping_amount'];
                        $totalShippingTaxAmount += $checkGstTax['shipping_tax_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->customer->name,
                    'ledger_id' => $this->customer_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'transaction_type' => 'Sale',
                    'ledger_name' => 'Shipping Charge',
                    'item_name' => 'Shipping Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    // 'hsn_code' => ! empty($this->saleItems->first()->items->model->hsn_sac_code) ? $this->saleItems->first()->items->model->hsn_sac_code : '',
                    'hsn_code' => '996812', // Fix HSN code for shipping and freight after discussion with Gopal bhai change it here
                    'taxable_value' => $totalShippingAmount,
                    'rate_of_gst' => 18,
                    'cgst' => (! empty($this->cgst) && $this->cgst != 0) ? ($totalShippingTaxAmount / 2) : '0',
                    'sgst' => (! empty($this->cgst) && $this->sgst != 0) ? ($totalShippingTaxAmount / 2) : '0',
                    'igst' => (! empty($this->igst) && $this->igst != 0) ? ($totalShippingTaxAmount) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'rcm_yes_or_no' => $this->saleItems->first()->classification_is_rcm_applicable ?? 'NO',
                    'invoice_amount' => $this->grand_total,
                ];
                $totalShippingAmount = 0;
                $totalShippingTaxAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->customer->name,
                    'ledger_id' => $this->customer_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'transaction_type' => 'Sale',
                    'ledger_name' => 'Shipping Charge',
                    'item_name' => 'Shipping Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    // 'hsn_code' => ! empty($this->saleItems->first()->items->model->hsn_sac_code) ? $this->saleItems->first()->items->model->hsn_sac_code : '',
                    'hsn_code' => '996812', // Fix HSN code for shipping and freight after discussion with Gopal bhai change it here
                    'taxable_value' => $data['shipping_amount'],
                    'rate_of_gst' => 18,
                    'cgst' => (! empty($this->cgst) || $this->cgst != 0) ? ($data['shipping_tax_amount'] / 2) : '0',
                    'sgst' => (! empty($this->cgst) || $this->sgst != 0) ? ($data['shipping_tax_amount'] / 2) : '0',
                    'igst' => (! empty($this->igst) || $this->igst != 0) ? ($data['shipping_tax_amount']) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'rcm_yes_or_no' => $this->saleItems->first()->classification_is_rcm_applicable ?? 'NO',
                    'invoice_amount' => $this->grand_total,
                ];
            }
        }

        return $prepareNewData;
    }

    public function preparePackingAmount()
    {
        $prepareData = [];
        $prepareNewData = [];
        if ($this->packing_charge != 0 && empty($this->packing_charge)) {
            return $prepareNewData;
        }
        if ($this->sales_item_type == self::ITEM_INVOICE) {
            $transactionItems = $this->saleItems;
            $totalTaxableAmount = $this->saleItems->sum('taxable_value') ?? 0;
            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }
            if ($transactionItems->count() >= 1) {
                foreach ($transactionItems as $saleItem) {
                    $packingAmount = ($totalTaxableAmount != 0) ? (($saleItem->taxable_value * $this->packing_charge) / $totalTaxableAmount) : 0;
                    $prepareData[] = [
                        'gst_tax' => $saleItem->gst_tax_percentage ?? 0,
                        'packing_amount' => $packingAmount,
                        'packing_tax_amount' => ($packingAmount * 18) / 100,
                    ];
                }
            }
        } else {
            $transactionLedger = $this->saleLedgers;
            $totalTaxableAmount = $this->saleLedgers->sum('taxable_value') ?? 0;
            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }
            if ($transactionLedger->count() >= 1) {
                foreach ($transactionLedger as $saleLedgers) {
                    $packingAmount = ($totalTaxableAmount != 0) ? (($saleLedgers->taxable_value * $this->packing_charge) / $totalTaxableAmount) : 0;
                    $prepareData[] = [
                        'gst_tax' => $saleLedgers->gst_tax_percentage ?? 0,
                        'packing_amount' => $packingAmount,
                        'packing_tax_amount' => ($packingAmount * 18) / 100,
                    ];
                }
            }
        }

        $gstTaxArray = [];
        $totalPackingAmount = 0;
        $totalPackingTaxAmount = 0;
        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalPackingAmount += $checkGstTax['packing_amount'];
                        $totalPackingTaxAmount += $checkGstTax['packing_tax_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->customer->name,
                    'ledger_id' => $this->customer_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'transaction_type' => 'Sale',
                    'ledger_name' => 'Packing Charge',
                    'item_name' => 'Packing Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '998541', // Fix HSN code for packing charge after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->saleItems->first()->items->model->hsn_sac_code) ? $this->saleItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $totalPackingAmount,
                    'rate_of_gst' => 18,
                    'cgst' => (! empty($this->cgst) && $this->cgst != 0) ? ($totalPackingTaxAmount / 2) : '0',
                    'sgst' => (! empty($this->cgst) && $this->sgst != 0) ? ($totalPackingTaxAmount / 2) : '0',
                    'igst' => (! empty($this->igst) && $this->igst != 0) ? ($totalPackingTaxAmount) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'rcm_yes_or_no' => $this->saleItems->first()->classification_is_rcm_applicable ?? 'NO',
                    'invoice_amount' => $this->grand_total,
                ];
                $totalPackingAmount = 0;
                $totalPackingTaxAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->customer->name,
                    'ledger_id' => $this->customer_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'transaction_type' => 'Sale',
                    'ledger_name' => 'Packing Charge',
                    'item_name' => 'Packing Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '998541', // Fix HSN code for packing charge after discussion with Gopal bhai change it here
                    'taxable_value' => $data['packing_amount'],
                    // 'hsn_code' => ! empty($this->saleItems->first()->items->model->hsn_sac_code) ? $this->saleItems->first()->items->model->hsn_sac_code : '',                    'taxable_value' => $data['packing_amount'],
                    'rate_of_gst' => 18,
                    'cgst' => (! empty($this->cgst) || $this->cgst != 0) ? ($data['packing_tax_amount'] / 2) : '0',
                    'sgst' => (! empty($this->cgst) || $this->sgst != 0) ? ($data['packing_tax_amount'] / 2) : '0',
                    'igst' => (! empty($this->igst) || $this->igst != 0) ? ($data['packing_tax_amount']) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'rcm_yes_or_no' => $this->saleItems->first()->classification_is_rcm_applicable ?? 'NO',
                    'invoice_amount' => $this->grand_total,
                ];
            }
        }

        return $prepareNewData;
    }

    public function getb2bSezdeTransactionsData()
    {
        $prepareData = [];
        $prepareNewData = [];
        $isRcmApplicable = self::REVERSE_CHARGE_APPLICABLE_NO;
        $classificationNatureType = '';
        $totalTaxableAmount = 0;
        if ($this->sales_item_type == self::ITEM_INVOICE && $this->saleItems->count() > 0) {
            $isRcmApplicable = $this->saleItems->first()->classification_is_rcm_applicable ?? $isRcmApplicable;
            $classificationNatureType = $this->saleItems->first()->classificationNatureType->name ?? '';
            $transactionItems = $this->saleItems->where('gst_id', '!=', 12);
            $totalTaxableAmount = $transactionItems->sum('taxable_value') ?? 0;
            foreach ($transactionItems as $saleItem) {
                $prepareData[] = [
                    'gst_tax' => $saleItem->gst_tax_percentage ?? 0,
                    'taxable_amount' => $saleItem->taxable_value,
                ];
            }
        }

        if ($this->sales_item_type == self::ACCOUNTING_INVOICE && $this->saleLedgers()->count() > 0) {
            $isRcmApplicable = $this->saleLedgers->first()->classification_is_rcm_applicable ?? $isRcmApplicable;
            $classificationNatureType = $this->saleLedgers->first()->classificationNatureType->name ?? '';
            $transactionItems = $this->saleLedgers->where('gst_id', '!=', 12);
            $totalTaxableAmount = $transactionItems->sum('taxable_value') ?? 0;
            foreach ($transactionItems as $saleItem) {
                $prepareData[] = [
                    'gst_tax' => $saleItem->gst_tax_percentage ?? 0,
                    'taxable_amount' => $saleItem->taxable_value,
                ];
            }
        }
        $additionalChargesData = $this->additionalCharges->where('gst_percentage', '!=', 0)->where('gst_rate_id', '!=', 12);
        $totalTaxableAmount = $totalTaxableAmount + ($additionalChargesData->sum('total') ?? 0);
        foreach ($additionalChargesData as $data) {
            $prepareData[] = [
                'gst_tax' => (float) $data->gst_percentage,
                'taxable_amount' => $data->total_without_tax,
            ];
        }

        $stateId = $this->addresses->firstWhere('address_type', self::BILLING_ADDRESS)->state_id ?? '';
        $gstTaxArray = [];
        $totalTaxAbleAmount = 0;

        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalTaxAbleAmount += $checkGstTax['taxable_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'gst' => $this->gstin ?? '',
                    'name' => $this->customer->name,
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'invoice_amount' => ($this->grand_total),
                    'cgst' => $this->cgst,
                    'sgst' => $this->sgst,
                    'igst' => $this->igst,
                    'place_of_supplier' => getStateNameWithCode($stateId),
                    'rcm' => $isRcmApplicable,
                    'invoice_type' => getInvoiceType($classificationNatureType),
                    'export_type' => getExportType($classificationNatureType),
                    'e_commerce_gstin' => '',
                    'gst_rate' => $data['gst_tax'],
                    'taxable_value' => $totalTaxAbleAmount,
                    'cess' => ($this->cess),
                    'uniqueKey' => $this->id.'-'.'SaleTransaction',
                    'tr_type' => 'sale',      // Transaction Type
                    'tr_id' => $this->id,   // Transaction Id
                    'items' => $this->saleItems->toArray(),
                    'ledgers' => $this->saleLedgers->toArray(),
                    'additionalCharges' => $additionalChargesData->toArray(),
                    'addLess' => $this->addLess->toArray(),
                ];
                $totalTaxAbleAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'gst' => $this->gstin ?? '',
                    'name' => $this->customer->name,
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'invoice_amount' => ($this->grand_total),
                    'cgst' => $this->cgst,
                    'sgst' => $this->sgst,
                    'igst' => $this->igst,
                    'place_of_supplier' => getStateNameWithCode($stateId),
                    'rcm' => $isRcmApplicable,
                    'invoice_type' => getInvoiceType($classificationNatureType),
                    'export_type' => getExportType($classificationNatureType),
                    'e_commerce_gstin' => '',
                    'gst_rate' => $data['gst_tax'],
                    'taxable_value' => $data['taxable_amount'],
                    'cess' => ($this->cess),
                    'uniqueKey' => $this->id.'-'.'SaleTransaction',
                    'tr_type' => 'sale',       // Transaction Type
                    'tr_id' => $this->id,    // Transaction Id
                    'items' => $this->saleItems->toArray(),
                    'ledgers' => $this->saleLedgers->toArray(),
                    'additionalCharges' => $additionalChargesData->toArray(),
                    'addLess' => $this->addLess->toArray(),
                ];
            }
        }

        return $prepareNewData;
    }

    public function getPrimaryPhone()
    {
        $phone = null;

        if (isset($this->customer->model->phone_1)) {
            $phone = $this->customer->model->region_code_1.$this->customer->model->phone_1;
        } elseif (isset($this->customer->model->phone_2)) {
            $phone = $this->customer->model->region_code_2.$this->customer->model->phone_2;
        }

        return $phone;
    }

    public function prepareAttributes()
    {

        $data = [];
        $data = [
            'party_name' => $this->customer->name,
            'invoice_number' => $this->full_invoice_number,
            'date' => Carbon::parse($this->date)->format('d-m-Y'),
            'grand_total' => $this->grand_total,
            'payment_status' => $this->paymentStatus,
            'taxable_value' => $this->taxable_value,
            'phone_no' => $this->getPrimaryPhone(),
            'cgst' => $this->cgst,
            'sgst' => $this->sgst,
            'igst' => $this->igst,
            'tcs_rate' => $this->tcs_rate,
            'tcs_amount' => $this->tcs_amount,
            'tcs_ledger' => $this->tcsLedger->name ?? '',
            'rounding_amount' => $this->rounding_amount,
            'total' => $this->total,
            'shipping_freight' => $this->shipping_freight,
            'shipping_freight_with_gst' => $this->shipping_freight_with_gst,
            'packing_charge' => $this->packing_charge,
            'packing_charge_with_gst' => $this->packing_charge_with_gst,
        ];

        if ($this->sales_item_type == self::ITEM_INVOICE) {
            $items = $this->saleItems;

            foreach ($items as $item) {
                $data['items'][] = [
                    'id' => $item->id,
                    'item_name' => $item->items->item_name,
                    'ledger_name' => $item->ledger->name,
                    'quantity' => $item->quantity,
                    'rpu_without_gst' => $item->rpu_without_gst,
                    'gst_tax_percentage' => $item->gst_tax_percentage,
                    'total_discount_amount' => $item->total_discount_amount,
                    'rpu_with_gst' => $item->rpu_with_gst,
                    'cgst' => $item->classification_cgst_tax ?? 0,
                    'sgst' => $item->classification_sgst_tax ?? 0,
                    'igst' => $item->classification_igst_tax ?? 0,
                    'total' => $item->total,
                ];
            }
        } else {
            $ledgers = $this->saleLedgers;
            foreach ($ledgers as $ledger) {
                $data['ledgers'][] = [
                    'id' => $ledger->id,
                    'ledger_name' => $ledger->ledgers->name,
                    'rpu_with_gst' => $ledger->rpu_with_gst,
                    'rpu_without_gst' => $ledger->rpu_without_gst,
                    'total_discount_amount' => $ledger->total_discount_amount,
                    'gst_tax_percentage' => $ledger->gst_tax_percentage,
                    'cgst' => $ledger->classification_cgst_tax ?? 0,
                    'sgst' => $ledger->classification_sgst_tax ?? 0,
                    'igst' => $ledger->classification_igst_tax ?? 0,
                ];
            }
        }

        return $data;
    }

    public function prepareData()
    {
        $ewayBill = EwayBill::where('transaction_id', $this->id)->where('transaction_type', SaleTransaction::class)->latest()->first();
        $eInvoice = EInvoice::where('transaction_id', $this->id)->where('transaction_type', SaleTransaction::class)->latest()->first();

        return [
            'transaction_id' => $this->id,
            'party_name' => $this->customer->name,
            'phone_no' => $this->getPrimaryPhone(),
            'party_id' => $this->customer->id,
            'invoice_number' => $this->full_invoice_number,
            'date' => Carbon::parse($this->date)->format('d-m-Y'),
            'invoice_amount' => $this->grand_total,
            'payment_status' => $this->payment_status,
            'sales_item_type' => $this->sales_item_type,
            'transaction_type' => 'Sale',
            'eway_bill_id' => $ewayBill ? $ewayBill->id : null,
            'is_eway_bill_cancel' => $ewayBill ? (bool) $ewayBill->is_canceled : false,
            'einvoice_id' => $eInvoice ? $eInvoice->id : null,
            'is_einvoice_cancel' => $eInvoice ? (bool) $eInvoice->is_canceled : false,
        ];
    }

    public function editPrepareData()
    {
        $billingAddress = $this->billingAddress;
        $shippingsAddress = $this->shippingAddress;
        $closingBalance = GetLedgerClosingBalanceAndType::run($this->customer_ledger_id);

        $data = [
            'id' => $this->id,
            'is_gst_enabled' => $this->is_gst_enabled,
            'payment_mode' => $this->payment_mode,
            'payment_type_ledger_id' => $this->payment_type_ledger_id,
            'payment_ledger_name' => $this->paymentTypeLedger?->name,
            'full_invoice_number' => $this->full_invoice_number,
            'invoice_number' => $this->invoice_number,
            'date' => Carbon::parse($this->date)->format('d-m-Y'),
            'customer_ledger_id' => $this->customer_ledger_id,
            'customer_ledger_name' => $this->customer->name,
            'customer_closing_bal' => $closingBalance['closing_bal'],
            'customer_closing_bal_type' => $closingBalance['closing_bal_type'],
            'gstin' => $this->gstin,
            'pan_card_number' => $this->customer->model->pan_card_number,
            'credit_limit' => $this->customer->model->credit_limit,
            'credit_limit_amount' => $this->customer->model->credit_limit_amount,
            'credit_limit_action' => $this->customer->model->credit_limit_action,
            'estimate_quote_no' => ! empty($this->estimate_quote_no) ? explode(',', $this->estimate_quote_no)[0] : null,
            'delivery_challan_no' => ! empty($this->delivery_challan_no) ? explode(',', $this->delivery_challan_no)[0] : null,
            'billing_address' => [
                'address_1' => $billingAddress['address_1'],
                'address_2' => $billingAddress['address_2'],
                'country_id' => $billingAddress['country_id'],
                'state_id' => $billingAddress['state_id'],
                'city_id' => $billingAddress['city_id'],
                'pin_code' => $billingAddress['pin_code'],
            ],
            'shippings_address' => [
                'address_1' => $shippingsAddress['address_1'],
                'address_2' => $shippingsAddress['address_2'],
                'country_id' => $shippingsAddress['country_id'],
                'state_id' => $shippingsAddress['state_id'],
                'city_id' => $shippingsAddress['city_id'],
                'pin_code' => $shippingsAddress['pin_code'],
            ],
            'broker_detail' => [
                'broker_id' => $this->broker_id ?? null,
                'broker_name' => $this->brokerDetails->broker_name ?? null,
                'brokerage_for_sale' => $this->brokerage_for_sale ?? null,
                'brokerage_on_value_type' => $this->brokerage_on_value_type ?? null,
            ],
            'transport_details' => [
                'transport_id' => $this->transport_id,
                'transport_name' => $this->transport?->transporter_name,
                'transporter_document_number' => $this->transporter_document_number,
                'transporter_document_date' => ! empty($this->transporter_document_date) ? Carbon::parse($this->transporter_document_date)->format('d-m-Y') : null,
                'po_no' => $this->po_no,
                'po_date' => ! empty($this->po_date) ? Carbon::parse($this->po_date)->format('d-m-Y') : null,
            ],
            'credit_period' => $this->credit_period,
            'credit_limit_period' => $this->credit_limit_period,
            'credit_period_type' => $this->credit_period_type,
            'sales_item_type' => $this->sales_item_type,
            'is_gst_na' => $this->is_gst_na,
            'main_classification_nature_type' => $this->saleItems[0]->classificationNatureType->name ?? $this->saleLedgers[0]->classificationNatureType->name ?? null,
            'main_is_rcm_applicable' => $this->saleItems[0]->classification_is_rcm_applicable ?? $this->saleLedgers[0]->classification_is_rcm_applicable ?? null,
            'is_consolidating_items_to_invoice' => $this->is_consolidating_items_to_invoice,
            'shipping_freight' => $this->shipping_freight,
            'packing_charge' => $this->packing_charge,
            'cess' => $this->cess,
            'tcs_tax_id' => $this->tcs_tax_id ?? null,
            'tcs_tax_name' => $this->tcsLedger->name ?? null,
            'tcs_rate' => $this->tcs_rate ?? null,
            'tcs_amount' => $this->tcs_amount ?? null,
            'cgst' => $this->cgst,
            'sgst' => $this->sgst,
            'igst' => $this->igst,
            'shipping_freight_with_gst' => $this->shipping_freight_with_gst,
            'packing_charge_with_gst' => $this->packing_charge_with_gst,
            'shipping_freight_sgst_amount' => $this->shipping_freight_sgst_amount,
            'shipping_freight_cgst_amount' => $this->shipping_freight_cgst_amount,
            'shipping_freight_igst_amount' => $this->shipping_freight_igst_amount,
            'packing_charge_sgst_amount' => $this->packing_charge_sgst_amount,
            'packing_charge_cgst_amount' => $this->packing_charge_cgst_amount,
            'packing_charge_igst_amount' => $this->packing_charge_igst_amount,
            'rounding_amount' => $this->rounding_amount,
            'is_cgst_sgst_igst_calculated' => $this->is_cgst_sgst_igst_calculated,
            'total' => $this->total,
            'grand_total' => $this->grand_total,
            'narration' => $this->narration,
            'term_and_condition' => $this->term_and_condition,
        ];

        $openingStock = GetOpeningStockRepo::run();
        if ($this->sales_item_type == self::ITEM_INVOICE) {
            $items = $this->saleItems;
            foreach ($items as $item) {
                $currentBalance = GetLedgerClosingBalanceAndType::run($item->ledger->id);
                $currentStock = GetItemClosingStock::run($item->items->id, $openingStock);
                $data['items'][] = [
                    'item_id' => $item->items->id,
                    'item_name' => $item->items->item_name,
                    'current_stock' => $currentStock['opening_balance_qty'],
                    'unit_id' => $item->unit->id,
                    'unit_name' => $item->unit->name,
                    'quantity' => $item->quantity,
                    'rpu_without_gst' => $item->rpu_without_gst,
                    'gst_id' => $item->gst_id,
                    'mrp' => $item->mrp,
                    'gst_tax_percentage' => $item->gst_tax_percentage,
                    'gst_total' => $item->total,
                    'discount_type' => $item->discount_type ?? null,
                    'discount_value' => $item->discount_value ?? null,
                    'total_discount_amount' => $item->total_discount_amount,
                    'rpu_with_gst' => $item->rpu_with_gst,
                    'is_additional_item_description' => $item->is_additional_item_description,
                    'additional_description' => $item->additional_description,
                    'classification_nature_type' => $item->classification_nature_type ?? null,
                    'classification_is_rcm_applicable' => $item->classification_is_rcm_applicable ?? false,
                    'classification_igst_tax' => $item->classification_igst_tax ?? null,
                    'classification_cgst_tax' => $item->classification_cgst_tax ?? null,
                    'classification_sgst_tax' => $item->classification_sgst_tax ?? null,
                    'classification_cess_tax' => $item->classification_cess_tax ?? null,
                    'consolidating_items_to_invoice' => $item->consolidating_items_to_invoice ?? null,
                    'cess_rate' => $item->cess_rate ?? null,
                    'cess_amount' => $item->cess_amount ?? null,
                    'taxable_amount' => $item->taxable_amount ?? null,
                    'ledger_id' => $item->ledger->id,
                    'ledger_name' => $item->ledger->name,
                    'closing_bal' => $currentBalance['closing_bal'],
                    'closing_bal_type' => $currentBalance['closing_bal_type'],
                    'total' => $item->total,
                ];
            }
        } else {
            $saleLedgers = $this->saleLedgers;
            foreach ($saleLedgers as $ledger) {
                $currentBalance = GetLedgerClosingBalanceAndType::run($ledger->ledgers->id);
                $data['ledgers'][] = [
                    'id' => $ledger->id,
                    'ledger_name' => $ledger->ledgers->name,
                    'closing_bal' => $currentBalance['closing_bal'],
                    'closing_bal_type' => $currentBalance['closing_bal_type'],
                    'rpu_with_gst' => $ledger->rpu_with_gst,
                    'rpu_without_gst' => $ledger->rpu_without_gst,
                    'discount_type' => $ledger->discount_type ?? null,
                    'discount_value' => $ledger->discount_value ?? null,
                    'total_discount_amount' => $ledger->total_discount_amount,
                    'gst_id' => $ledger->gst_id,
                    'gst_tax_percentage' => $ledger->gst_tax_percentage,
                    'is_additional_ledger_description' => $ledger->is_additional_ledger_description,
                    'additional_description' => $ledger->additional_description,
                    'classification_nature_type' => $ledger->classification_nature_type ?? null,
                    'classification_is_rcm_applicable' => $ledger->classification_is_rcm_applicable ?? false,
                    'classification_igst_tax' => $ledger->classification_igst_tax ?? null,
                    'classification_cgst_tax' => $ledger->classification_cgst_tax ?? null,
                    'classification_sgst_tax' => $ledger->classification_sgst_tax ?? null,
                    'classification_cess_tax' => $ledger->classification_cess_tax ?? null,
                    'total' => $ledger->total,
                ];
            }
        }
        $selectedIds = explode(',', $this->estimate_quote_no) ?? [];
        $estimateQuoteDocumentNumbers = getEstimateQuoteDocumentNumbers($this->customer_ledger_id, $selectedIds);
        $data['estimateQuoteDocumentNumbers'] = convertAssociativeArrayToKeyValueArray($estimateQuoteDocumentNumbers);

        return $data;
    }

    public function scopeFinancialYearDate(Builder $query)
    {
        $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
        $query->whereBetween('date', [$getCurrentFinancialYearDetails['yearStartDate'], $getCurrentFinancialYearDetails['yearEndDate']]);
    }

    public function incomeEstimateQuoteTransaction()
    {
        return $this->belongsTo(IncomeEstimateQuoteTransaction::class, 'estimate_quote_no', 'id');
    }

    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by', 'id');
    }

    public function additionalCharges(): HasMany
    {
        return $this->hasMany(AdditionalChargesForSalesTransaction::class, 'sale_id');
    }

    public function addLess(): HasMany
    {
        return $this->hasMany(AddLessForSalesTransaction::class, 'sale_id');
    }

    public function paymentDetails(): HasMany
    {
        return $this->hasMany(PaymentDetailsForSalesTransaction::class, 'sale_id');
    }

    public function scopeSorting($query, $sorting)
    {
        $sorting = getSortingValues($sorting);
        foreach ($sorting as $key => $value) {
            $query->orderBy($key, $value);
        }

        return $query;
    }

    public function prepareAdditionalCharges($isGstCalculated = false)
    {
        $dataItem = [];
        $additionalCharges = $this->additionalCharges->where('gst_percentage', '!=', 0)->when($isGstCalculated, function ($query) {
            return $query->where('gst_rate_id', '!=', 12);
        });
        $incomeTransactionLockDate = getTransactionsLockDate()[LockTransaction::INCOME] ?? null;

        foreach ($additionalCharges as $transaction) {
            $gstData = GstCalculateAction::run($this, $transaction);
            $dataItem[] = [
                'party_name' => $this->customer->name,
                'gstin' => $this->gstin,
                'ledger_name' => $transaction->ledger->name,
                'item_name' => $transaction->ledger->name,
                'ledger_id' => $this->customer_ledger_id,
                'invoice_number' => $this->full_invoice_number,
                'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                'transaction_type' => 'Sale',
                'hsn_code' => $transaction->ledger->model->hsn_sac_code,
                'rate_per_unit' => $transaction->total_without_tax,
                'item_description' => '', //
                'unit_of_measurement' => '',
                'quantity' => 0,
                'taxable_value' => $transaction->total_without_tax,
                'rate_of_gst' => $transaction->gst_percentage,
                'rcm_applicable_or_not' => '', //
                'cgst' => $gstData['cgst'],
                'sgst' => $gstData['sgst'],
                'igst' => $gstData['igst'],
                'cess_amount' => $this->cess_amount,
                'total_amount' => $transaction->value + $gstData['cgst'] + $gstData['sgst'] + $gstData['igst'] + $this->cess_amount,
                'invoice_amount' => $this->grand_total,
                'is_locked' => ! empty($this->date) ? ! empty($incomeTransactionLockDate) && Carbon::parse($incomeTransactionLockDate)->greaterThanOrEqualTo($this->date) : false,
                'rcm_yes_or_no' => $this->saleItems->first()->classification_is_rcm_applicable ?? 'NO',
            ];
        }

        return $dataItem;
    }

    public function scopeSortByInvoiceNumber($query, $direction = 'asc')
    {
        $dir = strtoupper($direction) === 'DESC' ? 'DESC' : 'ASC';

        return $query->orderByRaw("CAST(REGEXP_REPLACE(full_invoice_number, '[^0-9]', '') AS UNSIGNED) $dir");
    }
}
