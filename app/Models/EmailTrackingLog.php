<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTrackingLog extends Model
{
    use HasFactory;

    public const SUBJECT = 'Mail Delivery Failure Notification';

    public $table = 'email_tracking_logs';

    public $fillable = [
        'message_id',
        'send_to_email',
        'company_id',
        'user_id',
        'meta',
        'invoice_id',
        'email_sent',
    ];

    public $casts = [
        'meta' => 'array',
    ];
}
