<?php

namespace App\Models;

use App\Actions\CommonAction\GstCalculateAction;
use App\Actions\CommonAction\IsNegative;
use App\Models\Master\Broker;
use App\Models\Master\Transport;
use App\Traits\HasCompany;
use App\Traits\HasDeleted;
use App\Traits\HasJsonResourcefulData;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as CollectionAlias;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * App\Models\PurchaseReturnTransaction
 *
 * @property int $id
 * @property int $company_id
 * @property string|null $voucher_number
 * @property int|null $payment_mode
 * @property int|null $payment_type_ledger_id
 * @property Carbon $voucher_date
 * @property int|null $original_inv_no
 * @property Carbon|null $original_inv_date
 * @property string|null $supplier_purchase_return_number
 * @property Carbon|null $supplier_purchase_return_date
 * @property int $supplier_id
 * @property string|null $gstin
 * @property int|null $broker_id
 * @property float|null $brokerage_for_sale
 * @property int|null $brokerage_on_value_type
 * @property int|null $transport_id
 * @property string|null $transporter_document_number
 * @property Carbon|null $transporter_document_date
 * @property int $pr_item_type
 * @property float|null $shipping_freight
 * @property float|null $packing_charge
 * @property int|null $tcs_tax_id
 * @property float|null $tcs_rate
 * @property float|null $tcs_amount
 * @property float|null $cgst
 * @property float|null $sgst
 * @property float|null $igst
 * @property float $shipping_freight_with_gst
 * @property float $packing_charge_with_gst
 * @property float|null $shipping_freight_sgst_amount
 * @property float|null $shipping_freight_cgst_amount
 * @property float|null $shipping_freight_igst_amount
 * @property float|null $packing_charge_sgst_amount
 * @property float|null $packing_charge_cgst_amount
 * @property float|null $packing_charge_igst_amount
 * @property float|null $rounding_amount
 * @property float|null $total
 * @property float $grand_total
 * @property string|null $narration
 * @property string|null $term_and_condition
 * @property int $is_gst_enabled
 * @property int $pass_tds_entry
 * @property string|null $tds_pan
 * @property float|null $tds_taxable_value
 * @property int|null $ledger_of_tds
 * @property string|null $tds_rate
 * @property float|null $tds_amount
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property float $cess
 * @property string|null $sale_number
 * @property Carbon $date_of_invoice
 * @property int $supplier_ledger_id
 * @property float|null $cess_amount
 * @property-read string|null $paymentStatus
 * @property-read CollectionAlias|Address[] $addresses
 * @property-read int|null $addresses_count
 * @property-read CollectionAlias|PurchaseReturnItemTransaction[] $purchaseReturnItems
 * @property-read int|null $purchase_return_items_count
 * @property-read CollectionAlias|PurchaseReturnLedgerTransaction[] $purchaseReturnLedgers
 * @property-read int|null $purchase_return_ledgers_count
 * @property-read CollectionAlias|ReceiptTransactionItem[] $receiptTransactionItem
 * @property-read int|null $receipt_transaction_item_count
 * @property-read Ledger $supplier
 * @property-read Ledger|null $tdsLedger
 * @property-read Transport|null $transport
 * @property-read Address|null $shippingAddress
 * @property-read Address|null $billingAddress
 *
 * @method static Builder|PurchaseReturnTransaction newModelQuery()
 * @method static Builder|PurchaseReturnTransaction newQuery()
 * @method static Builder|PurchaseReturnTransaction query()
 * @method static Builder|PurchaseReturnTransaction whereBrokerId($value)
 * @method static Builder|PurchaseReturnTransaction whereBrokerageForSale($value)
 * @method static Builder|PurchaseReturnTransaction whereBrokerageOnValueType($value)
 * @method static Builder|PurchaseReturnTransaction whereCess($value)
 * @method static Builder|PurchaseReturnTransaction whereCgst($value)
 * @method static Builder|PurchaseReturnTransaction whereCompanyId($value)
 * @method static Builder|PurchaseReturnTransaction whereCreatedAt($value)
 * @method static Builder|PurchaseReturnTransaction whereGrandTotal($value)
 * @method static Builder|PurchaseReturnTransaction whereGstin($value)
 * @method static Builder|PurchaseReturnTransaction whereId($value)
 * @method static Builder|PurchaseReturnTransaction whereIgst($value)
 * @method static Builder|PurchaseReturnTransaction whereIsGstEnabled($value)
 * @method static Builder|PurchaseReturnTransaction whereLedgerOfTds($value)
 * @method static Builder|PurchaseReturnTransaction whereNarration($value)
 * @method static Builder|PurchaseReturnTransaction whereOriginalInvDate($value)
 * @method static Builder|PurchaseReturnTransaction whereOriginalInvNo($value)
 * @method static Builder|PurchaseReturnTransaction wherePackingCharge($value)
 * @method static Builder|PurchaseReturnTransaction wherePackingChargeCgstAmount($value)
 * @method static Builder|PurchaseReturnTransaction wherePackingChargeIgstAmount($value)
 * @method static Builder|PurchaseReturnTransaction wherePackingChargeSgstAmount($value)
 * @method static Builder|PurchaseReturnTransaction wherePackingChargeWithGst($value)
 * @method static Builder|PurchaseReturnTransaction wherePassTdsEntry($value)
 * @method static Builder|PurchaseReturnTransaction wherePaymentMode($value)
 * @method static Builder|PurchaseReturnTransaction wherePaymentTypeLedgerId($value)
 * @method static Builder|PurchaseReturnTransaction wherePrItemType($value)
 * @method static Builder|PurchaseReturnTransaction whereRoundingAmount($value)
 * @method static Builder|PurchaseReturnTransaction whereSgst($value)
 * @method static Builder|PurchaseReturnTransaction whereShippingFreight($value)
 * @method static Builder|PurchaseReturnTransaction whereShippingFreightCgstAmount($value)
 * @method static Builder|PurchaseReturnTransaction whereShippingFreightIgstAmount($value)
 * @method static Builder|PurchaseReturnTransaction whereShippingFreightSgstAmount($value)
 * @method static Builder|PurchaseReturnTransaction whereShippingFreightWithGst($value)
 * @method static Builder|PurchaseReturnTransaction whereSupplierId($value)
 * @method static Builder|PurchaseReturnTransaction whereSupplierPurchaseReturnDate($value)
 * @method static Builder|PurchaseReturnTransaction whereSupplierPurchaseReturnNumber($value)
 * @method static Builder|PurchaseReturnTransaction whereTcsAmount($value)
 * @method static Builder|PurchaseReturnTransaction whereTcsRate($value)
 * @method static Builder|PurchaseReturnTransaction whereTcsTaxId($value)
 * @method static Builder|PurchaseReturnTransaction whereTdsAmount($value)
 * @method static Builder|PurchaseReturnTransaction whereTdsPan($value)
 * @method static Builder|PurchaseReturnTransaction whereTdsRate($value)
 * @method static Builder|PurchaseReturnTransaction whereTdsTaxableValue($value)
 * @method static Builder|PurchaseReturnTransaction whereTermAndCondition($value)
 * @method static Builder|PurchaseReturnTransaction whereTotal($value)
 * @method static Builder|PurchaseReturnTransaction whereTransportId($value)
 * @method static Builder|PurchaseReturnTransaction whereTransporterDocumentDate($value)
 * @method static Builder|PurchaseReturnTransaction whereTransporterDocumentNumber($value)
 * @method static Builder|PurchaseReturnTransaction whereUpdatedAt($value)
 * @method static Builder|PurchaseReturnTransaction whereVoucherDate($value)
 * @method static Builder|PurchaseReturnTransaction whereVoucherNumber($value)
 *
 * @mixin Eloquent
 *
 * @method static Builder|PurchaseReturnTransaction withoutAppends()
 *
 * @property int $is_gst_na
 * @property int $is_cgst_sgst_igst_calculated
 * @property bool $is_rcm_applicable
 * @property-read Broker|null $brokerDetails
 * @property-read array|string $invoice_attachment
 * @property-read CollectionAlias|JournalCreditCustomerTransaction[] $journalTransactionItem
 * @property-read int|null $journal_transaction_item_count
 * @property-read MediaCollection|Media[] $media
 * @property-read int|null $media_count
 * @property-read PurchaseTransaction|null $purchase
 * @property-read float $paid_amount
 *
 * @method static Builder|PurchaseReturnTransaction whereIsCgstSgstIgstCalculated($value)
 * @method static Builder|PurchaseReturnTransaction whereIsGstNa($value)
 * @method static Builder|PurchaseReturnTransaction whereIsRcmApplicable($value)
 */
class PurchaseReturnTransaction extends Model implements Auditable, HasMedia
{
    use HasCompany, HasFactory, SoftDeletes;
    use HasDeleted, HasJsonResourcefulData, InteractsWithMedia;
    use \OwenIt\Auditing\Auditable;

    /**
     * Attributes to exclude from the Audit.
     *
     * @var array
     */
    protected $auditExclude = [
        'taxable_value',
        'payment_status',
        'due_amount',
        'deleted_by',
    ];

    public $table = 'purchase_return_transactions';

    public const TRANSACTION_TYPE = 'purchaseReturnTransaction';

    public const PURCHASE_RETURN_DOCUMENT = 'purchase_return_document';

    public const CASH_MODE = 1;

    public const CREDIT_MODE = 2;

    public const PAYMENT_MODE = [
        self::CASH_MODE => 'Cash',
        self::CREDIT_MODE => 'Credit',
    ];

    public const BROKERAGE_ON_INVOICE_VALUE = 1;

    public const BROKERAGE_ON_TAXABLE_VALUE = 2;

    public const BROKERAGE_ON_GROSS_VALUE = 3;

    public const BROKERAGE_ON_VALUE_TYPE = [
        self::BROKERAGE_ON_INVOICE_VALUE => 'Invoice Value',
        self::BROKERAGE_ON_TAXABLE_VALUE => 'Taxable Value',
        self::BROKERAGE_ON_GROSS_VALUE => 'Gross Value',
    ];

    public const PASS_TDS_ENTRY_YES = 1;

    public const PASS_TDS_ENTRY_NO = 0;

    public const ACCOUNTING_INVOICE = 1;

    public const ITEM_INVOICE = 2;

    public const PURCHASE_RETURN_TRANSACTION_TYPE = [
        self::ACCOUNTING_INVOICE => 'Accounting Invoice',
        self::ITEM_INVOICE => 'Item Invoice',
    ];

    public const BILLING_ADDRESS = 1;

    public const SHIPPING_ADDRESS = 2;

    public const DISCOUNT_TYPE_AMOUNT = 1;

    public const DISCOUNT_TYPE_PERCENTAGE = 2;

    public const DISCOUNT_TYPES = [
        self::DISCOUNT_TYPE_AMOUNT => '₹',
        self::DISCOUNT_TYPE_PERCENTAGE => '%',
    ];

    public const REVERSE_CHARGE_APPLICABLE_YES = 1;

    public const REVERSE_CHARGE_APPLICABLE_NO = 0;

    public const REVERSE_CHARGE_APPLICABLE_TYPE = [
        self::REVERSE_CHARGE_APPLICABLE_YES => 'Yes',
        self::REVERSE_CHARGE_APPLICABLE_NO => 'No',
    ];

    public const SAVE_BUTTON = 1;

    public const SAVE_AND_NEW_BUTTON = 2;

    public const SAVE_AND_PRINT_BUTTON = 3;

    public const PAYMENT_STATUS_PAID = 'Paid';

    public const PAYMENT_STATUS_UNPAID = 'Unpaid';

    public const PAYMENT_STATUS_PARTLY_UNPAID = 'Partially Unpaid';

    public const PAYMENT_STATUS_ARR = [
        self::PAYMENT_STATUS_PAID => self::PAYMENT_STATUS_PAID,
        self::PAYMENT_STATUS_UNPAID => self::PAYMENT_STATUS_UNPAID,
        self::PAYMENT_STATUS_PARTLY_UNPAID => self::PAYMENT_STATUS_PARTLY_UNPAID,
    ];

    public const PAID = 1;

    public const UNPAID = 2;

    public const PARTLY_UNPAID = 3;

    public const PAYMENT_STATUS = [
        self::PAID => 'Paid',
        self::UNPAID => 'Unpaid',
        self::PARTLY_UNPAID => 'Partially Unpaid',
    ];

    public const INVOICE_ATTACHMENT = 'invoice_attachment';

    public $fillable = [
        'company_id',
        'voucher_number',
        'original_inv_no',
        'supplier_purchase_return_date',
        'supplier_purchase_return_number',
        'voucher_date',
        'original_inv_date',
        'supplier_id',
        'gstin',
        'broker_id',
        'brokerage_for_sale',
        'brokerage_on_value_type',
        'transport_id',
        'shipping_name',
        'shipping_gstin',
        'shipping_name',
        'address_name',
        'party_name_same_as_address_name',
        'transporter_document_number',
        'transporter_document_date',
        'transporter_vehicle_number',
        'pr_item_type',
        'shipping_freight',
        'packing_charge',
        'tcs_rate',
        'tcs_amount',
        'cgst',
        'sgst',
        'igst',
        'rounding_amount',
        'total',
        'grand_total',
        'narration',
        'term_and_condition',
        'shipping_address_id',
        'same_as_billing',
        'tcs_tax_id',
        'is_gst_enabled',
        'cess',
        'shipping_freight_with_gst',
        'packing_charge_with_gst',
        'payment_mode',
        'payment_type_ledger_id',
        'pass_tds_entry',
        'tds_pan',
        'tds_taxable_value',
        'ledger_of_tds',
        'tds_rate',
        'tds_amount',
        'shipping_freight_sgst_amount',
        'shipping_freight_cgst_amount',
        'shipping_freight_igst_amount',
        'packing_charge_sgst_amount',
        'packing_charge_cgst_amount',
        'packing_charge_igst_amount',
        'is_cgst_sgst_igst_calculated',
        'is_gst_na',
        'is_rcm_applicable',
        'created_by',
        'taxable_value',
        'gross_value',
        'payment_status',
        'due_amount',
        'via_api',
        'is_import',
        'deleted_by',
        'is_round_off_not_changed',
        'eway_bill_number',
        'eway_bill_date',
        'created_at',
        'updated_at',
        'round_off_method',
    ];

    public $casts = [
        'date' => 'date',
        'voucher_date' => 'date',
        'brokerage_on_value_type' => 'integer',
        'debit_note' => 'string',
        'pr_item_type' => 'integer',
        'brokerage_for_sale' => 'double',
        'shipping_freight' => 'double',
        'packing_charge' => 'double',
        'tcs_rate' => 'double',
        'tcs_amount' => 'double',
        'cgst' => 'double',
        'sgst' => 'double',
        'igst' => 'double',
        'rounding_amount' => 'double',
        'total' => 'double',
        'grand_total' => 'double',
        'original_inv_date' => 'date',
        'gstin' => 'string',
        'transporter_document_number' => 'string',
        'transporter_document_date' => 'date',
        'narration' => 'string',
        'term_and_condition' => 'string',
        'is_gst_enabled' => 'integer',
        'supplier_purchase_return_date' => 'date',
        'supplier_purchase_return_number' => 'string',
        'cess' => 'double',
        'shipping_freight_with_gst' => 'double',
        'packing_charge_with_gst' => 'double',
        'shipping_freight_sgst_amount' => 'double',
        'shipping_freight_cgst_amount' => 'double',
        'shipping_freight_igst_amount' => 'double',
        'packing_charge_sgst_amount' => 'double',
        'packing_charge_cgst_amount' => 'double',
        'packing_charge_igst_amount' => 'double',
        'is_rcm_applicable' => 'boolean',
        'taxable_value' => 'double',
        'gross_value' => 'double',
        'due_amount' => 'double',
        'party_name_same_as_address_name' => 'boolean',
        'same_as_billing' => 'boolean',
        'round_off_method' => 'integer',
    ];

    public $appends = ['paid_amount'];

    public function getPurchaseReturnFileAttribute(): array|string
    {
        $mediaUrl = [];
        /** @var Media $medias */
        $medias = $this->getMedia(self::PURCHASE_RETURN_DOCUMENT);

        foreach ($medias as $media) {
            $mediaUrl[] = $media->getFullUrl();
        }

        if (! empty($mediaUrl)) {
            return $mediaUrl;
        }

        return '';
    }

    public function getInvoiceAttachmentAttribute(): array|string
    {
        $mediaUrl = [];
        /** @var Media $medias */
        $medias = $this->getMedia(self::INVOICE_ATTACHMENT);

        foreach ($medias as $media) {
            $mediaUrl[] = $media->getFullUrl();
        }

        if (! empty($mediaUrl)) {
            return $mediaUrl;
        }

        return '';
    }

    public function transformAudit(array $data): array
    {
        if ($data['event'] == 'created') {
            Arr::set($data, 'title', '<b>Purchase Return</b> Invoice <b>'.$this->getAttribute('voucher_number').'</b> was created.');
        } elseif ($data['event'] == 'updated') {
            Arr::set($data, 'title', '<b>Purchase Return</b> Invoice <b>'.$this->getAttribute('voucher_number').'</b> was edited.');
        } elseif ($data['event'] == 'deleted') {
            if (checkThisDeleted($data) == 'hard-delete' || checkThisDeleted($data) == 'bulk-delete' || checkThisDeleted($data) == 'empty-transaction' || checkThisDeleted($data) == 'empty-whole-bin') {
                Arr::set($data, 'title', '<b>Purchase Return</b> Invoice <b>'.$this->getAttribute('voucher_number').'</b> was deleted from recycle bin.');
            } else {
                Arr::set($data, 'title', '<b>Purchase Return</b> Invoice <b>'.$this->getAttribute('voucher_number').'</b> was moved to recycle bin.');
            }
        } elseif ($data['event'] == 'restored') {
            Arr::set($data, 'title', '<b>Purchase Return</b> Invoice <b>'.$this->getAttribute('voucher_number').'</b> was restored from recycle bin.');
        }

        return $data;
    }

    /** @return BelongsTo<Company> */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function purchaseReturnItems(): HasMany
    {
        return $this->hasMany(PurchaseReturnItemTransaction::class, 'purchase_return_id');
    }

    public function purchaseReturnLedgers(): HasMany
    {
        return $this->hasMany(PurchaseReturnLedgerTransaction::class, 'purchase_return_id');
    }

    public function purchase(): BelongsTo
    {
        return $this->belongsTo(PurchaseTransaction::class, 'original_inv_no', 'id');
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'supplier_id', 'id');
    }

    public function tdsLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'ledger_of_tds', 'id');
    }

    public function tcsLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'tcs_tax_id', 'id');
    }

    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'model');
    }

    public function billingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::BILLING_ADDRESS);
    }

    public function shippingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::SHIPPING_ADDRESS);
    }

    public function dispatchAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::SHIPPING_ADDRESS);
    }

    public function transport(): BelongsTo
    {
        return $this->belongsTo(Transport::class, 'transport_id', 'id');
    }

    public function brokerDetails(): BelongsTo
    {
        return $this->belongsTo(Broker::class, 'broker_id', 'id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by', 'id');
    }

    public function additionalCharges(): HasMany
    {
        return $this->hasMany(AdditionalChargesForPurchaseReturnTransaction::class, 'purchase_return_id');
    }

    public function addLess(): HasMany
    {
        return $this->hasMany(AddLessForPurchaseReturnTransaction::class, 'purchase_return_id');
    }

    public function paymentDetails(): HasMany
    {
        return $this->hasMany(PaymentDetailsForPurchaseReturnTransaction::class, 'purchase_return_id');
    }

    public function paymentTypeLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'payment_type_ledger_id', 'id');
    }

    public function settleAdvancePayment(): HasMany
    {
        return $this->hasMany(SettleAdvancePayment::class, 'transaction_id', 'id')
            ->where('transaction_type', SettleAdvancePayment::PURCHASE_RETURN);
    }

    public function advancePayment(): MorphMany
    {
        return $this->morphMany(SettleAdvancePayment::class, 'model');
    }

    public function customFieldValues(): MorphMany
    {
        return $this->morphMany(TransactionCustomFieldValue::class, 'model');
    }

    public function getDayBookData(): array
    {
        return [
            'date' => $this->voucher_date->format('d-m-Y'),
            'particulars' => $this->supplier->name,
            'voucher_type' => 'Purchase Return',
            'voucher_number' => $this->voucher_number,
            'debit_amount' => 0,
            'credit_amount' => -$this->grand_total,
            'narration' => $this->narration,
            'ledger_id' => $this->supplier_id,
        ];
    }

    protected function getTaxableValue(): Attribute
    {
        $taxableValue = 0;
        if ($this->pr_item_type == self::ACCOUNTING_INVOICE) {
            foreach ($this->purchaseReturnLedgers as $purchaseReturnLedgers) {
                $taxableValue += (float) $purchaseReturnLedgers->rpu_without_gst - (float) $purchaseReturnLedgers->total_discount_amount;
            }
        }

        if ($this->pr_item_type == self::ITEM_INVOICE) {
            foreach ($this->purchaseReturnItems as $purchaseReturnItems) {
                $taxableValue += ((float) $purchaseReturnItems->quantity * (float) $purchaseReturnItems->rpu_without_gst)
                    - (int) $purchaseReturnItems->total_discount_amount;
            }
        }

        if (! empty($this->shipping_freight) && $this->shipping_freight > 0) {
            $taxableValue += $this->shipping_freight;
        }

        if (! empty($this->packing_charge) && $this->packing_charge > 0) {
            $taxableValue += $this->packing_charge;
        }

        return Attribute::make(
            get: fn ($value) => $taxableValue,
        );
    }

    public function getLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-Y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->original_inv_no,
            'debit_amount' => 0,
            'credit_amount' => $this->grand_total,
            'balance' => 0,
        ];
    }

    public function getTdsLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->original_inv_no,
            'debit_amount' => $this->tds_amount,
            'credit_amount' => 0,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getCgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->original_inv_no,
            'debit_amount' => 0,
            'credit_amount' => $this->cgst,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getSgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->original_inv_no,
            'debit_amount' => 0,
            'credit_amount' => $this->sgst,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getIgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->original_inv_no,
            'debit_amount' => 0,
            'credit_amount' => $this->igst,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getPackingChargeReport(): array
    {
        $packingCharge = $this->packing_charge;

        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->original_inv_no,
            'debit_amount' => 0,
            'credit_amount' => $packingCharge,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getFreightChargeReport(): array
    {
        $freightCharge = $this->shipping_freight;

        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->original_inv_no,
            'debit_amount' => 0,
            'credit_amount' => $freightCharge,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getAdditionalChargeReport($value): array
    {
        $debit = 0;
        $credit = 0;
        if (IsNegative::run($value)) {
            $debit = abs($value);
        } else {
            $credit = $value;
        }

        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->original_inv_no,
            'debit_amount' => $debit,
            'credit_amount' => $credit,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getRoundOffReport(): array
    {
        if ($this->rounding_amount < 0) {
            $roundAmount = abs($this->rounding_amount);
            $data = [
                'transaction_id' => $this->id,
                'date' => $this->voucher_date->format('d-m-y'),
                'ledger_name' => $this->supplier->name,
                'transaction_type' => 'Purchase Return',
                'voucher_no' => $this->voucher_number,
                'invoice_no' => $this->original_inv_no,
                'debit_amount' => $roundAmount,
                'narration' => $this->narration ?? null,
                'credit_amount' => 0,
                'balance' => 0,
            ];
        } else {
            $data = [
                'transaction_id' => $this->id,
                'date' => $this->voucher_date->format('d-m-y'),
                'ledger_name' => $this->supplier->name,
                'transaction_type' => 'Purchase Return',
                'voucher_no' => $this->voucher_number,
                'invoice_no' => $this->original_inv_no,
                'debit_amount' => 0,
                'credit_amount' => $this->rounding_amount,
                'balance' => 0,
            ];
        }

        return $data;
    }

    public function getSupplierLedgerReport(): array
    {
        $data = [];
        $ledgerName = '';
        if ($this->pr_item_type == self::ITEM_INVOICE && $this->purchaseReturnItems->count() >= 1) {
            foreach ($this->purchaseReturnItems as $purchaseReturnItem) {
                $purchaseReturnItem->load('ledger');
                if (empty($data)) {
                    // If $data is empty, just add the first saleItem
                    $data[] = [
                        'name' => $purchaseReturnItem->ledger->name ?? '',
                        'amount' => $purchaseReturnItem->total ?? 0,
                    ];
                } else {
                    $found = false; // Flag to check if the ledger name is found in $data
                    foreach ($data as &$item) {
                        if ($item['name'] == $purchaseReturnItem->ledger->name) {
                            // If the ledger name matches, add the amount to existing data
                            $item['amount'] += $purchaseReturnItem->total ?? 0;
                            $found = true;
                            break; // Exit the loop once found
                        }
                    }
                    if (! $found) {
                        // If the ledger name is not found in $data, add it as a new entry
                        $data[] = [
                            'name' => $purchaseReturnItem->ledger->name ?? '',
                            'amount' => $purchaseReturnItem->total ?? 0,
                        ];
                    }
                }
            }
            $ledger = $this->purchaseReturnItems->firstWhere('ledger_id', '!=', null);
            $ledger?->load('ledger');
            $ledgerName = $ledger->ledger->name ?? '';
        }

        if ($this->pr_item_type == self::ACCOUNTING_INVOICE && $this->purchaseReturnLedgers->count() >= 1) {
            foreach ($this->purchaseReturnLedgers as $purchaseReturnLedger) {
                $purchaseReturnLedger->load('ledger');
                if (empty($data)) {
                    // If $data is empty, just add the first saleItem
                    $data[] = [
                        'name' => $purchaseReturnLedger->ledger->name ?? '',
                        'amount' => $purchaseReturnLedger->total ?? 0,
                    ];
                } else {
                    $found = false; // Flag to check if the ledger name is found in $data
                    foreach ($data as &$item) {
                        if ($item['name'] == $purchaseReturnLedger->ledger->name) {
                            // If the ledger name matches, add the amount to existing data
                            $item['amount'] += $purchaseReturnLedger->total ?? 0;
                            $found = true;
                            break; // Exit the loop once found
                        }
                    }
                    if (! $found) {
                        // If the ledger name is not found in $data, add it as a new entry
                        $data[] = [
                            'name' => $purchaseReturnLedger->ledger->name ?? '',
                            'amount' => $purchaseReturnLedger->total ?? 0,
                        ];
                    }
                }
            }
            $ledger = $this->purchaseReturnLedgers->firstWhere('ledger_id', '!=', null);
            $ledger?->load('ledger');
            $ledgerName = $ledger->ledger->name ?? '';
        }

        $data['ledger'] = $data;

        if (isCompanyGstApplicable()) {
            $data[] = [
                'name' => 'CGST',
                'amount' => $this->cgst,
            ];
            $data[] = [
                'name' => 'SGST',
                'amount' => $this->sgst,
            ];
            $data[] = [
                'name' => 'IGST',
                'amount' => $this->igst,
            ];
            $data[] = [
                'name' => 'Round Off',
                'amount' => $this->rounding_amount,
            ];
        }

        $additionalChargeAndAddless = [];
        foreach ($this->additionalCharges as $additionalCharge) {
            $additionalChargeAndAddless[] = [
                'name' => $additionalCharge->ledger->name ?? '',
                'amount' => $additionalCharge->total_without_tax,
            ];
        }

        foreach ($this->addLess as $addLess) {
            $additionalChargeAndAddless[] = [
                'name' => $addLess->ledger->name ?? '',
                'amount' => $addLess->total_without_tax,
            ];
        }

        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $ledgerName != '' ? $ledgerName : $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->purchase->voucher_number ?? '',
            'debit_amount' => $this->grand_total,
            'credit_amount' => 0,
            'balance' => 0,
            'tcs_amount' => $this->tcs_amount ?? 0,
            'cgst' => $this->cgst ?? 0,
            'sgst' => $this->sgst ?? 0,
            'igst' => $this->igst ?? 0,
            'additional_charges_addless' => $additionalChargeAndAddless,
            'cess' => $this->cess ?? 0,
            'rounding_amount' => $this->rounding_amount ?? 0,
            'ledger_list' => $data['ledger'],
            'data' => $data,
            'narration' => $this->narration ?? null,
            'payment_status' => $this->payment_status ?? null,
            'transaction_item_list' => $this->transactionItemListAsString(),
        ];
    }

    public function transactionItemListAsString(): string
    {
        return $this->purchaseReturnItems
            ->map(function ($transactionItem) {
                $itemName = $transactionItem->items?->item_name;
                $quantity = number_format($transactionItem->quantity, 2);
                $uom = $transactionItem->unit?->code ?? '';
                $totalDiscountAmount = number_format($transactionItem->total_discount_amount, 2);
                $rate = number_format($transactionItem->rpu_without_gst, 2);
                $total = number_format($transactionItem->total, 2);
                if ($totalDiscountAmount == 0) {
                    return "{$itemName}: {$quantity} {$uom} * {$rate} = {$total}";
                }

                return "{$itemName}: {$quantity} {$uom} * {$rate} - {$totalDiscountAmount} = {$total}";
            })
            ->filter()
            ->implode('<br>');
    }

    public function receiptTransactionItem(): HasMany
    {
        return $this->hasMany(ReceiptTransactionItem::class, 'purchase_return_id', 'id');
    }

    public function journalTransactionItem(): HasMany
    {
        return $this->hasMany(JournalCreditCustomerTransaction::class, 'purchase_return_id', 'id');
    }

    protected function paidAmount(): Attribute
    {
        if ($this->payment_mode == self::CASH_MODE) {
            $paidAmount = $this->grand_total;

            return Attribute::make(
                get: static fn ($value) => $paidAmount,
            );
        }

        $validReceiptTransactionItems = $this->receiptTransactionItem->filter(function ($item) {
            return $item->receiptTransaction && $item->deleted_at == null;
        });

        $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
            return $item->journalTransaction && $item->deleted_at == null;
        });

        $paidAmount = (
            ($validReceiptTransactionItems->sum('received_amount') ?? 0)
            + ($validReceiptTransactionItems->sum('discount') ?? 0)
            + ($validReceiptTransactionItems->sum('round_off') ?? 0)
            + ($validJournalTransactionItems->sum('received_amount') ?? 0)
            + ($validJournalTransactionItems->sum('discount') ?? 0)
            + ($validJournalTransactionItems->sum('round_off') ?? 0)
            + ($this->tds_amount ?? 0)
        );

        $paidAmount = round($paidAmount, getCompanyFixedDigitNumber());

        return Attribute::make(
            get: static fn ($value) => $paidAmount,
        );
    }

    protected function getDueAmount(): Attribute
    {
        $totalDueAmount = 0;
        // is transaction in the mapping after due amount =0
        if ($this->original_inv_no || $this->payment_mode == self::CASH_MODE) {
            return Attribute::make(
                get: static fn ($value) => $totalDueAmount,
            );
        }

        $validReceiptTransactionItems = $this->receiptTransactionItem->filter(function ($item) {
            return $item->receiptTransaction && ! $item->receiptTransaction->trashed();
        });

        $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
            return $item->journalTransaction && ! $item->journalTransaction->trashed();
        });

        $receivedAmount = (
            ($validReceiptTransactionItems->sum('received_amount') ?? 0)
            + ($validReceiptTransactionItems->sum('discount') ?? 0)
            + ($validReceiptTransactionItems->sum('round_off') ?? 0)
            + ($validJournalTransactionItems->sum('received_amount') ?? 0)
            + ($validJournalTransactionItems->sum('discount') ?? 0)
            + ($validJournalTransactionItems->sum('round_off') ?? 0)
            + ($this->advancePayment->sum('adjusted_amount') ?? 0)
            + ($this->tds_amount ?? 0)
        );

        $receivedAmount = round($receivedAmount, getCompanyFixedDigitNumber());
        $grandTotal = round($this->grand_total, getCompanyFixedDigitNumber());

        return Attribute::make(
            get: static fn ($value) => $grandTotal - $receivedAmount,
        );
    }

    protected function getPaymentStatus(): Attribute
    {
        $paymentStatus = '';
        // is transaction in the mapping after payment status = null
        if ($this->original_inv_no) {
            return Attribute::make(
                get: static fn ($value) => $paymentStatus,
            );
        }

        if ($this->payment_mode == self::CASH_MODE) {
            return Attribute::make(
                get: static fn ($value) => self::PAYMENT_STATUS_PAID,
            );
        }

        $validReceiptTransactionItems = $this->receiptTransactionItem->filter(function ($item) {
            return $item->receiptTransaction && ! $item->receiptTransaction->trashed();
        });

        $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
            return $item->journalTransaction && ! $item->journalTransaction->trashed();
        });

        $receivedAmount = (
            ($validReceiptTransactionItems->sum('received_amount') ?? 0)
            + ($validReceiptTransactionItems->sum('discount') ?? 0)
            + ($validReceiptTransactionItems->sum('round_off') ?? 0)
            + ($validJournalTransactionItems->sum('received_amount') ?? 0)
            + ($validJournalTransactionItems->sum('discount') ?? 0)
            + ($validJournalTransactionItems->sum('round_off') ?? 0)
            + ($this->advancePayment->sum('adjusted_amount') ?? 0)
            + ($this->tds_amount ?? 0)
        );

        $receivedAmount = round($receivedAmount, getCompanyFixedDigitNumber());
        $totalAmount = round($this->grand_total, getCompanyFixedDigitNumber());

        if ($receivedAmount >= $totalAmount) {
            $paymentStatus = self::PAYMENT_STATUS_PAID;
        } elseif ($receivedAmount == 0) {
            $paymentStatus = self::PAYMENT_STATUS_UNPAID;
        } elseif ($receivedAmount <= $totalAmount) {
            $paymentStatus = self::PAYMENT_STATUS_PARTLY_UNPAID;
        }

        return Attribute::make(
            get: static fn ($value) => $paymentStatus,
        );
    }

    public static bool $withoutAppends = true;

    public function scopeWithoutAppends($query)
    {
        self::$withoutAppends = false;

        return $query;
    }

    protected function getArrayableAppends()
    {
        if (self::$withoutAppends) {
            return $this->appends;
        }

        return parent::getArrayableAppends();
    }

    public function getCessReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->cess,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function prepareShippingAmount()
    {
        $prepareData = [];
        $prepareNewData = [];

        if ($this->shipping_freight != 0 && empty($this->shipping_freight)) {
            return $prepareNewData;
        }

        if ($this->pr_item_type == self::ITEM_INVOICE) {
            $transactionItems = $this->purchaseReturnItems;
            $totalTaxableAmount = $this->purchaseReturnItems->sum('taxable_value') ?? 0;

            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }

            if ($transactionItems->count() >= 1) {
                foreach ($transactionItems as $purchaseReturnItem) {
                    $shippingAmount = ($totalTaxableAmount != 0) ? ($purchaseReturnItem->taxable_value * $this->shipping_freight) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $purchaseReturnItem->gst_tax_percentage ?? 0,
                        'shipping_amount' => $shippingAmount,
                        'shipping_tax_amount' => ($shippingAmount * 18) / 100,
                    ];
                }
            }
        } else {
            $transactionLedgers = $this->purchaseReturnLedgers;
            $totalTaxableAmount = $this->purchaseReturnLedgers->sum('taxable_value') ?? 0;

            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }
            if ($transactionLedgers->count() >= 1) {
                foreach ($transactionLedgers as $purchaseReturnLedger) {

                    $shippingAmount = ($totalTaxableAmount != 0) ? ($purchaseReturnLedger->taxable_value * $this->shipping_freight) / $totalTaxableAmount : 0;

                    $prepareData[] = [
                        'gst_tax' => $purchaseReturnLedger->gst_tax_percentage ?? 0,
                        'shipping_amount' => $shippingAmount,
                        'shipping_tax_amount' => ($shippingAmount * 18) / 100,
                    ];
                }
            }
        }

        $gstTaxArray = [];
        $totalShippingAmount = 0;
        $totalShippingTaxAmount = 0;
        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalShippingAmount += $checkGstTax['shipping_amount'];
                        $totalShippingTaxAmount += $checkGstTax['shipping_tax_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->supplier->name,
                    'ledger_id' => $this->supplier_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->voucher_number,
                    'invoice_number' => $this->supplier_purchase_return_number,
                    'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->supplier_purchase_return_date)->format('d-m-Y'),
                    'transaction_type' => 'Purchase Return',
                    'ledger_name' => 'Shipping Charge',
                    'item_name' => 'Shipping Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '996812', // Fix HSN code for shipping and freight after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->purchaseReturnItems->first()->items->model->hsn_sac_code) ? $this->purchaseReturnItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => -$totalShippingAmount,
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) && $this->cgst != 0) ? -($totalShippingTaxAmount / 2) : '0',
                    'sgst' => (! empty($this->cgst) && $this->sgst != 0) ? -($totalShippingTaxAmount / 2) : '0',
                    'igst' => (! empty($this->igst) && $this->igst != 0) ? -($totalShippingTaxAmount) : '0',
                    'cess_amount' => (! empty($this->cess)) ? -$this->cess : '0',
                    'itc' => $this->purchaseReturnItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->purchaseReturnItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => -$this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES_RETURN,
                ];
                $totalShippingAmount = 0;
                $totalShippingTaxAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->supplier->name,
                    'ledger_id' => $this->supplier_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->voucher_number,
                    'invoice_number' => $this->supplier_purchase_return_number,
                    'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->supplier_purchase_return_date)->format('d-m-Y'),
                    'transaction_type' => 'Purchase Return',
                    'ledger_name' => 'Shipping Charge',
                    'item_name' => 'Shipping Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '996812', // Fix HSN code for shipping and freight after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->purchaseReturnItems->first()->items->model->hsn_sac_code) ? $this->purchaseReturnItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => -$data['shipping_amount'],
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) || $this->cgst != 0) ? -($data['shipping_tax_amount'] / 2) : '0',
                    'sgst' => (! empty($this->cgst) || $this->sgst != 0) ? -($data['shipping_tax_amount'] / 2) : '0',
                    'igst' => (! empty($this->igst) || $this->igst != 0) ? -($data['shipping_tax_amount']) : '0',
                    'cess_amount' => (! empty($this->cess)) ? -$this->cess : '0',
                    'itc' => $this->purchaseReturnItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->purchaseReturnItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => -$this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES_RETURN,
                ];
            }
        }

        return $prepareNewData;
    }

    public function preparePackingAmount()
    {
        $prepareData = [];
        $prepareNewData = [];

        if ($this->packing_charge != 0 && empty($this->packing_charge)) {
            return $prepareNewData;
        }

        if ($this->pr_item_type == self::ITEM_INVOICE) {
            $transactionItems = $this->purchaseReturnItems;
            $totalTaxableAmount = $this->purchaseReturnItems->sum('taxable_value') ?? 0;
            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }
            if ($transactionItems->count() >= 1) {
                foreach ($transactionItems as $purchaseItem) {
                    $PackingAmount = ($totalTaxableAmount != 0) ? ($purchaseItem->taxable_value * $this->packing_charge) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $purchaseItem->gst_tax_percentage ?? 0,
                        'packing_amount' => $PackingAmount,
                        'packing_tax_amount' => ($PackingAmount * 18) / 100,
                    ];
                }
            }
        } else {
            $transactionLedgers = $this->purchaseReturnLedgers;
            $totalTaxableAmount = $this->purchaseReturnLedgers->sum('taxable_value') ?? 0;

            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }
            if ($transactionLedgers->count() >= 1) {
                foreach ($transactionLedgers as $purchaseReturnLedger) {
                    $PackingAmount = ($totalTaxableAmount != 0) ? ($purchaseReturnLedger->taxable_value * $this->packing_charge) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $purchaseReturnLedger->gst_tax_percentage ?? 0,
                        'packing_amount' => $PackingAmount,
                        'packing_tax_amount' => ($PackingAmount * 18) / 100,
                    ];
                }
            }
        }

        $gstTaxArray = [];
        $totalPackingAmount = 0;
        $totalPackingTaxAmount = 0;
        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalPackingAmount += $checkGstTax['packing_amount'];
                        $totalPackingTaxAmount += $checkGstTax['packing_tax_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->supplier->name,
                    'ledger_id' => $this->supplier_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->voucher_number,
                    'invoice_number' => $this->supplier_purchase_return_number,
                    'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->supplier_purchase_return_date)->format('d-m-Y'),
                    'transaction_type' => 'Purchase Return',
                    'ledger_name' => 'Packing Charge',
                    'item_name' => 'Packing Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '998541', // Fix HSN code for packing charge after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->purchaseReturnItems->first()->items->model->hsn_sac_code) ? $this->purchaseReturnItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => -$totalPackingAmount,
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) && $this->cgst != 0) ? -($totalPackingTaxAmount / 2) : '0',
                    'sgst' => (! empty($this->cgst) && $this->sgst != 0) ? -($totalPackingTaxAmount / 2) : '0',
                    'igst' => (! empty($this->igst) && $this->igst != 0) ? -($totalPackingTaxAmount) : '0',
                    'cess_amount' => (! empty($this->cess)) ? -$this->cess : '0',
                    'itc' => $this->purchaseReturnItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->purchaseReturnItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => -$this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES_RETURN,
                ];
                $totalPackingAmount = 0;
                $totalPackingTaxAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->supplier->name,
                    'ledger_id' => $this->supplier_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->voucher_number,
                    'invoice_number' => $this->supplier_purchase_return_number,
                    'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->supplier_purchase_return_date)->format('d-m-Y'),
                    'transaction_type' => 'Purchase Return',
                    'ledger_name' => 'Packing Charge',
                    'item_name' => 'Packing Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '998541', // Fix HSN code for packing charge after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->purchaseReturnItems->first()->items->model->hsn_sac_code) ? $this->purchaseReturnItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => -$data['packing_amount'],
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) || $this->cgst != 0) ? -($data['packing_tax_amount'] / 2) : '0',
                    'sgst' => (! empty($this->cgst) || $this->sgst != 0) ? -($data['packing_tax_amount'] / 2) : '0',
                    'igst' => (! empty($this->igst) || $this->igst != 0) ? -($data['packing_tax_amount']) : '0',
                    'cess_amount' => (! empty($this->cess)) ? -$this->cess : '0',
                    'itc' => $this->purchaseReturnItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->purchaseReturnItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => -$this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES_RETURN,
                ];
            }
        }

        return $prepareNewData;
    }

    public function getTcsReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->tcs_amount,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getSupplierTdsLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->tdsLedger->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->supplier_purchase_return_number ?? '',
            'debit_amount' => 0,
            'credit_amount' => $this->tds_amount,
            'balance' => 0,
        ];
    }

    public function getPrimaryPhone()
    {
        $phone = null;

        if (isset($this->supplier->model->phone_1)) {
            $phone = $this->supplier->model->region_code_1.$this->supplier->model->phone_1;
        } elseif (isset($this->party->model->phone_2)) {
            $phone = $this->supplier->model->region_code_2.$this->supplier->model->phone_2;
        }

        return $phone;
    }

    public function prepareAttributes()
    {

        $data = [
            'party_name' => $this->supplier->name,
            'invoice_number' => $this->voucher_number,
            'date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
            'grand_total' => $this->grand_total,
            'phone_no' => $this->getPrimaryPhone(),
            'payment_status' => $this->paymentStatus,
            'taxable_value' => $this->taxable_value,
            'cgst' => $this->cgst,
            'sgst' => $this->sgst,
            'igst' => $this->igst,
            'tcs_rate' => $this->tcs_rate,
            'tcs_amount' => $this->tcs_amount,
            'tcs_ledger' => $this->tcsLedger->name ?? '',
            'rounding_amount' => $this->rounding_amount,
            'total' => $this->total,
            'shipping_freight' => $this->shipping_freight,
            'shipping_freight_with_gst' => $this->shipping_freight_with_gst,
            'packing_charge' => $this->packing_charge,
            'packing_charge_with_gst' => $this->packing_charge_with_gst,
        ];

        if ($this->pr_item_type == self::ITEM_INVOICE) {
            $items = $this->purchaseReturnItems;
            foreach ($items as $item) {
                $data['items'][] = [
                    'id' => $item->id,
                    'item_name' => $item->items->item_name,
                    'ledger_name' => $item->ledger->name,
                    'quantity' => $item->quantity,
                    'rpu_without_gst' => $item->rpu_without_gst,
                    'gst_tax_percentage' => $item->gst_tax_percentage,
                    'total_discount_amount' => $item->total_discount_amount,
                    'discount_type' => $item->discount_type,
                    'rpu_with_gst' => $item->rpu_with_gst,
                    'cgst' => $item->classification_cgst_tax ?? 0,
                    'sgst' => $item->classification_sgst_tax ?? 0,
                    'igst' => $item->classification_igst_tax ?? 0,
                    'total' => $item->total,
                ];
            }
        } else {
            $ledgers = $this->purchaseReturnLedgers;
            foreach ($ledgers as $ledger) {
                $data['ledgers'][] = [
                    'id' => $ledger->id,
                    'ledger_name' => $ledger->ledgers->name,
                    'rpu_with_gst' => $ledger->rpu_with_gst,
                    'rpu_without_gst' => $ledger->rpu_without_gst,
                    'gst_tax_percentage' => $ledger->gst_tax_percentage,
                    'total_discount_amount' => $ledger->total_discount_amount,
                    'cgst' => $ledger->classification_cgst_tax ?? 0,
                    'sgst' => $ledger->classification_sgst_tax ?? 0,
                    'igst' => $ledger->classification_igst_tax ?? 0,
                ];
            }
        }

        return $data;
    }

    public function prepareData()
    {
        return [
            'transaction_id' => $this->id,
            'party_name' => $this->supplier->name,
            'party_id' => $this->supplier_id,
            'phone_no' => $this->getPrimaryPhone(),
            'invoice_number' => $this->voucher_number ?? '',
            'invoice_amount' => $this->grand_total,
            'payment_status' => $this->payment_status ?? '',
            'transaction_type' => 'Purchase Return',
            'date' => Carbon::parse($this->voucher_date)->format('d-m-Y') ?? '',
        ];
    }

    public function scopeFinancialYearDate(Builder $query)
    {
        $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
        $query->whereBetween('voucher_date', [$getCurrentFinancialYearDetails['yearStartDate'], $getCurrentFinancialYearDetails['yearEndDate']]);
    }

    public function scopeSorting($query, $sorting)
    {
        $sorting = getSortingValues($sorting);
        foreach ($sorting as $key => $value) {
            $query->orderBy($key, $value);
        }

        return $query;
    }

    public function prepareAdditionalCharges()
    {
        $dataItem = [];
        $additionalCharges = $this->additionalCharges->where('gst_percentage', '!=', 0);
        $expenseTransactionLockDate = getTransactionsLockDate()[LockTransaction::EXPENSE] ?? null;
        foreach ($additionalCharges as $transaction) {
            $gstData = GstCalculateAction::run($this, $transaction);
            $dataItem[] = [
                'party_name' => $this->supplier->name,
                'gstin' => $this->gstin,
                'ledger_name' => $transaction->ledger->name,
                'item_name' => $transaction->ledger->name,
                'ledger_id' => $this->supplier_ledger_id,
                'invoice_number' => $this->sale_number,
                'voucher_number' => $this->voucher_number,
                'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                'invoice_date' => Carbon::parse($this->date_of_invoice)->format('d-m-Y'),
                'transaction_type' => 'Purchase Return',
                'quantity' => 1,
                'hsn_code' => $transaction->ledger->model->hsn_sac_code,
                'rate_per_unit' => $transaction->total_without_tax,
                'item_description' => '', //
                'unit_of_measurement' => '',
                'rate_of_gst' => $transaction->gst_percentage,
                'gst_rate' => $transaction->gst_percentage,
                'rcm_applicable_or_not' => '', //
                'taxable_value' => -$transaction->total_without_tax,
                'cgst' => -$gstData['cgst'],
                'sgst' => -$gstData['sgst'],
                'igst' => -$gstData['igst'],
                'cess_amount' => -$this->cess_amount,
                'itc' => $this->purchaseReturnItems->first()->classification_is_itc_applicable ?? 0,
                'total_amount' => -($transaction->value + $gstData['cgst'] + $gstData['sgst'] + $gstData['igst'] + $this->cess_amount),
                'invoice_amount' => -$this->grand_total,
                'is_locked' => ! empty($this->voucher_date) ? ! empty($expenseTransactionLockDate) && Carbon::parse($expenseTransactionLockDate)->greaterThanOrEqualTo($this->voucher_date) : false,
                'rcm_yes_or_no' => $this->purchaseReturnItems->first()->classification_is_rcm_applicable ?? 0,
            ];
        }

        return $dataItem;
    }
}
