<?php

namespace App\Models;

use App\Actions\CommonAction\GstCalculateAction;
use App\Actions\CommonAction\IsNegative;
use App\Models\Master\Broker;
use App\Models\Master\Transport;
use App\Traits\HasCompany;
use App\Traits\HasDeleted;
use App\Traits\HasJsonResourcefulData;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * App\Models\IncomeDebitNoteTransaction
 *
 * @property int $id
 * @property int $company_id
 * @property int|null $payment_mode
 * @property int|null $payment_type_ledger_id
 * @property string|null $debit_note_number
 * @property string|null $full_invoice_number
 * @property Carbon $date
 * @property int|null $original_inv_no
 * @property Carbon|null $original_inv_date
 * @property int $customer_ledger_id
 * @property string|null $gstin
 * @property int|null $broker_id
 * @property float|null $brokerage_for_sale
 * @property int|null $brokerage_on_value_type
 * @property int|null $credit_period
 * @property int|null $credit_period_type
 * @property int|null $transport_id
 * @property string|null $transporter_document_number
 * @property Carbon|null $transporter_document_date
 * @property string|null $po_no
 * @property Carbon|null $po_date
 * @property int $dn_item_type
 * @property float|null $shipping_freight
 * @property float|null $packing_charge
 * @property int|null $tcs_tax_id
 * @property int $billing_state_id
 * @property float|null $tcs_rate
 * @property float|null $tcs_amount
 * @property float|null $cgst
 * @property float|null $sgst
 * @property float|null $igst
 * @property float $shipping_freight_with_gst
 * @property float $packing_charge_with_gst
 * @property float|null $shipping_freight_sgst_amount
 * @property float|null $shipping_freight_cgst_amount
 * @property float|null $shipping_freight_igst_amount
 * @property float|null $packing_charge_sgst_amount
 * @property float|null $packing_charge_cgst_amount
 * @property float|null $packing_charge_igst_amount
 * @property float|null $rounding_amount
 * @property float|null $total
 * @property float $grand_total
 * @property string|null $narration
 * @property string|null $term_and_condition
 * @property int $is_gst_enabled
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property float $cess
 * @property float|null $cess_amount
 * @property-read string|null $paymentStatus
 * @property-read Collection|Address[] $addresses
 * @property-read int|null $addresses_count
 * @property-read Ledger $customer
 * @property-read Collection|IncomeDebitNoteItemTransaction[] $incomeDebitNoteItems
 * @property-read int|null $income_debit_note_items_count
 * @property-read Collection|IncomeDebitNoteLedgerTransaction[] $incomeDebitNoteLedgers
 * @property-read int|null $income_debit_note_ledgers_count
 * @property-read Collection|PaymentTransactionItem[] $paymentTransactionItem
 * @property-read int|null $payment_transaction_item_count
 * @property-read Collection|ReceiptTransactionItem[] $receiptTransactionItem
 * @property-read int|null $receipt_transaction_item_count
 * @property-read Ledger|null $tcsLedger
 * @property-read Transport|null $transport
 *
 * @method static Builder|IncomeDebitNoteTransaction newModelQuery()
 * @method static Builder|IncomeDebitNoteTransaction newQuery()
 * @method static Builder|IncomeDebitNoteTransaction query()
 * @method static Builder|IncomeDebitNoteTransaction whereBrokerId($value)
 * @method static Builder|IncomeDebitNoteTransaction whereBrokerageForSale($value)
 * @method static Builder|IncomeDebitNoteTransaction whereBrokerageOnValueType($value)
 * @method static Builder|IncomeDebitNoteTransaction whereCess($value)
 * @method static Builder|IncomeDebitNoteTransaction whereCgst($value)
 * @method static Builder|IncomeDebitNoteTransaction whereCompanyId($value)
 * @method static Builder|IncomeDebitNoteTransaction whereCreatedAt($value)
 * @method static Builder|IncomeDebitNoteTransaction whereCreditPeriod($value)
 * @method static Builder|IncomeDebitNoteTransaction whereCreditPeriodType($value)
 * @method static Builder|IncomeDebitNoteTransaction whereCustomerLedgerId($value)
 * @method static Builder|IncomeDebitNoteTransaction whereDate($value)
 * @method static Builder|IncomeDebitNoteTransaction whereDebitNoteNumber($value)
 * @method static Builder|IncomeDebitNoteTransaction whereDnItemType($value)
 * @method static Builder|IncomeDebitNoteTransaction whereFullInvoiceNumber($value)
 * @method static Builder|IncomeDebitNoteTransaction whereGrandTotal($value)
 * @method static Builder|IncomeDebitNoteTransaction whereGstin($value)
 * @method static Builder|IncomeDebitNoteTransaction whereId($value)
 * @method static Builder|IncomeDebitNoteTransaction whereIgst($value)
 * @method static Builder|IncomeDebitNoteTransaction whereIsGstEnabled($value)
 * @method static Builder|IncomeDebitNoteTransaction whereNarration($value)
 * @method static Builder|IncomeDebitNoteTransaction whereOriginalInvDate($value)
 * @method static Builder|IncomeDebitNoteTransaction whereOriginalInvNo($value)
 * @method static Builder|IncomeDebitNoteTransaction wherePackingCharge($value)
 * @method static Builder|IncomeDebitNoteTransaction wherePackingChargeCgstAmount($value)
 * @method static Builder|IncomeDebitNoteTransaction wherePackingChargeIgstAmount($value)
 * @method static Builder|IncomeDebitNoteTransaction wherePackingChargeSgstAmount($value)
 * @method static Builder|IncomeDebitNoteTransaction wherePackingChargeWithGst($value)
 * @method static Builder|IncomeDebitNoteTransaction wherePaymentMode($value)
 * @method static Builder|IncomeDebitNoteTransaction wherePaymentTypeLedgerId($value)
 * @method static Builder|IncomeDebitNoteTransaction wherePoDate($value)
 * @method static Builder|IncomeDebitNoteTransaction wherePoNo($value)
 * @method static Builder|IncomeDebitNoteTransaction whereRoundingAmount($value)
 * @method static Builder|IncomeDebitNoteTransaction whereSgst($value)
 * @method static Builder|IncomeDebitNoteTransaction whereShippingFreight($value)
 * @method static Builder|IncomeDebitNoteTransaction whereShippingFreightCgstAmount($value)
 * @method static Builder|IncomeDebitNoteTransaction whereShippingFreightIgstAmount($value)
 * @method static Builder|IncomeDebitNoteTransaction whereShippingFreightSgstAmount($value)
 * @method static Builder|IncomeDebitNoteTransaction whereShippingFreightWithGst($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTcsAmount($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTcsRate($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTcsTaxId($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTermAndCondition($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTotal($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTransportId($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTransporterDocumentDate($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTransporterDocumentNumber($value)
 * @method static Builder|IncomeDebitNoteTransaction whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property int $is_cgst_sgst_igst_calculated
 * @property-read Broker|null $brokerDetails
 * @property-read array|string $invoice_attachment
 * @property-read Collection|JournalCreditCustomerTransaction[] $journalTransactionItem
 * @property-read int|null $journal_transaction_item_count
 * @property-read MediaCollection|Media[] $media
 * @property-read int|null $media_count
 * @property-read SaleTransaction|null $sale
 *
 * @method static Builder|IncomeDebitNoteTransaction whereBillingStateId($value)
 * @method static Builder|IncomeDebitNoteTransaction whereIsCgstSgstIgstCalculated($value)
 * @method static Builder|IncomeDebitNoteTransaction withoutAppends()
 *
 * @property int $is_gst_na
 *
 * @method static Builder|IncomeDebitNoteTransaction whereIsGstNa($value)
 *
 * @property int|null $created_by
 * @property string|null $next_sent_at_reminder
 * @property string|null $credit_period_due_date
 * @property string|null $transporter_vehicle_number
 * @property string|null $eway_bill_date
 * @property int|null $tds_tax_id
 * @property float|null $tds_rate
 * @property float|null $tds_amount
 * @property float|null $taxable_value
 * @property float|null $gross_value
 * @property string|null $payment_status
 * @property float|null $due_amount
 * @property int $via_api
 * @property int|null $dispatch_address_id
 * @property int|null $is_import
 * @property string|null $shipping_gstin
 * @property string|null $shipping_name
 * @property int|null $deleted_by
 * @property Carbon|null $deleted_at
 * @property int|null $is_round_off_not_changed
 * @property-read Collection<int, \App\Models\AddLessForIncomeDebitNoteTransaction> $addLess
 * @property-read int|null $add_less_count
 * @property-read Collection<int, \App\Models\AdditionalChargesForIncomeDebitNoteTransaction> $additionalCharges
 * @property-read int|null $additional_charges_count
 * @property-read Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @property-read \App\Models\Address|null $billingAddress
 * @property-read \App\Models\Company $company
 * @property-read \App\Models\User|null $createdBy
 * @property-read \App\Models\User|null $deletedBy
 * @property-read \App\Models\Address|null $dispatchAddress
 * @property-read array|string $income_debit_note_file
 * @property-read Collection<int, \App\Models\PaymentDetailsForIncomeDebitNoteTransaction> $paymentDetails
 * @property-read int|null $payment_details_count
 * @property-read \App\Models\Ledger|null $paymentTypeLedger
 * @property-read \App\Models\Address|null $shippingAddress
 * @property-read \App\Models\Ledger|null $tdsLedger
 * @property-read float $paid_amount
 *
 * @method static \Database\Factories\IncomeDebitNoteTransactionFactory factory($count = null, $state = [])
 * @method static Builder|IncomeDebitNoteTransaction financialYearDate()
 * @method static Builder|IncomeDebitNoteTransaction onlyTrashed()
 * @method static Builder|IncomeDebitNoteTransaction sorting($sorting)
 * @method static Builder|IncomeDebitNoteTransaction whereCreatedBy($value)
 * @method static Builder|IncomeDebitNoteTransaction whereCreditPeriodDueDate($value)
 * @method static Builder|IncomeDebitNoteTransaction whereDeletedAt($value)
 * @method static Builder|IncomeDebitNoteTransaction whereDeletedBy($value)
 * @method static Builder|IncomeDebitNoteTransaction whereDispatchAddressId($value)
 * @method static Builder|IncomeDebitNoteTransaction whereDueAmount($value)
 * @method static Builder|IncomeDebitNoteTransaction whereEwayBillDate($value)
 * @method static Builder|IncomeDebitNoteTransaction whereEwayBillNumber($value)
 * @method static Builder|IncomeDebitNoteTransaction whereGrossValue($value)
 * @method static Builder|IncomeDebitNoteTransaction whereIsImport($value)
 * @method static Builder|IncomeDebitNoteTransaction whereIsRoundOffNotChanged($value)
 * @method static Builder|IncomeDebitNoteTransaction whereNextSentAtReminder($value)
 * @method static Builder|IncomeDebitNoteTransaction wherePaymentStatus($value)
 * @method static Builder|IncomeDebitNoteTransaction whereShippingGstin($value)
 * @method static Builder|IncomeDebitNoteTransaction whereShippingName($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTaxableValue($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTdsAmount($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTdsRate($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTdsTaxId($value)
 * @method static Builder|IncomeDebitNoteTransaction whereTransporterVehicleNumber($value)
 * @method static Builder|IncomeDebitNoteTransaction whereViaApi($value)
 * @method static Builder|IncomeDebitNoteTransaction withTrashed()
 * @method static Builder|IncomeDebitNoteTransaction withoutTrashed()
 *
 * @mixin \Eloquent
 */
class IncomeDebitNoteTransaction extends Model implements Auditable, HasMedia
{
    use HasCompany, HasJsonResourcefulData, InteractsWithMedia;
    use HasDeleted, HasFactory, SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    /**
     * Attributes to exclude from the Audit.
     *
     * @var array
     */
    protected $auditExclude = [
        'taxable_value',
        'payment_status',
        'due_amount',
        'deleted_by',
    ];

    public $table = 'income_debit_note_transactions';

    public const INCOME_DEBIT_NOTE_DOCUMENT = 'income_debit_note_document';

    public const ACCOUNTING_INVOICE = 1;

    public const ITEM_INVOICE = 2;

    public const INCOME_DEBIT_NOTE_TRANSACTION_TYPE = [
        self::ACCOUNTING_INVOICE => 'Accounting Invoice',
        self::ITEM_INVOICE => 'Item Invoice',
    ];

    public const DISCOUNT_TYPE_AMOUNT = 1;

    public const DISCOUNT_TYPE_PERCENTAGE = 2;

    public const DISCOUNT_TYPES = [
        self::DISCOUNT_TYPE_AMOUNT => '₹',
        self::DISCOUNT_TYPE_PERCENTAGE => '%',
    ];

    public const CASH_MODE = 1;

    public const CREDIT_MODE = 2;

    public const PAYMENT_MODE = [
        self::CASH_MODE => 'Cash',
        self::CREDIT_MODE => 'Credit',
    ];

    public const BROKERAGE_ON_INVOICE_VALUE = 1;

    public const BROKERAGE_ON_TAXABLE_VALUE = 2;

    public const BROKERAGE_ON_GROSS_VALUE = 3;

    public const BROKERAGE_ON_VALUE_TYPE = [
        self::BROKERAGE_ON_INVOICE_VALUE => 'Invoice Value',
        self::BROKERAGE_ON_TAXABLE_VALUE => 'Taxable Value',
        self::BROKERAGE_ON_GROSS_VALUE => 'Gross Value',
    ];

    public const BILLING_ADDRESS = 1;

    public const SHIPPING_ADDRESS = 2;

    public const DISPATCH_ADDRESS = 3;

    public const SAVE_BUTTON = 1;

    public const SAVE_AND_NEW_BUTTON = 2;

    public const SAVE_AND_PRINT_BUTTON = 3;

    public const PAYMENT_STATUS_PAID = 'Paid';

    public const PAYMENT_STATUS_UNPAID = 'Unpaid';

    public const PAYMENT_STATUS_PARTLY_UNPAID = 'Partially Unpaid';

    public const PAYMENT_STATUS_ARR = [
        self::PAYMENT_STATUS_PAID => self::PAYMENT_STATUS_PAID,
        self::PAYMENT_STATUS_UNPAID => self::PAYMENT_STATUS_UNPAID,
        self::PAYMENT_STATUS_PARTLY_UNPAID => self::PAYMENT_STATUS_PARTLY_UNPAID,
    ];

    public const TRANSACTION_TYPE = 'incomeDebitNoteTransaction';

    public const INVOICE_ATTACHMENT = 'invoice_attachment';

    public const CREDIT_PERIOD_TYPE_MONTH = 1;

    public const CREDIT_PERIOD_TYPE_DAY = 2;

    public const CREDIT_PERIOD_TYPE_VALUE = [
        'Month' => self::CREDIT_PERIOD_TYPE_MONTH,
        'Day' => self::CREDIT_PERIOD_TYPE_DAY,
    ];

    public const CREDIT_PERIOD_TYPE = [
        self::CREDIT_PERIOD_TYPE_DAY => 'Day',
        self::CREDIT_PERIOD_TYPE_MONTH => 'Month',
    ];

    public const PAID = 1;

    public const UNPAID = 2;

    public const PARTLY_UNPAID = 3;

    public const PAYMENT_STATUS = [
        self::PAID => 'Paid',
        self::UNPAID => 'Unpaid',
        self::PARTLY_UNPAID => 'Partially Unpaid',
    ];

    public $fillable = [
        'company_id',
        'payment_mode',
        'full_invoice_number',
        'debit_note_number',
        'date',
        'customer_ledger_id',
        'party_phone_number',
        'region_iso',
        'region_code',
        'gstin',
        'broker_id',
        'brokerage_for_sale',
        'brokerage_on_value_type',
        'credit_period',
        'credit_period_type',
        'transport_id',
        'shipping_name',
        'shipping_gstin',
        'address_name',
        'party_name_same_as_address_name',
        'transporter_document_number',
        'transporter_document_date',
        'transporter_vehicle_number',
        'po_no',
        'po_date',
        'dn_item_type',
        'shipping_freight',
        'packing_charge',
        'tcs_rate',
        'tcs_amount',
        'cgst',
        'sgst',
        'igst',
        'rounding_amount',
        'total',
        'grand_total',
        'narration',
        'term_and_condition',
        'dispatch_address_id',
        'shipping_address_id',
        'same_as_billing',
        'tcs_tax_id',
        'payment_type_ledger_id',
        'original_inv_date',
        'original_inv_no',
        'is_gst_enabled',
        'cess',
        'shipping_freight_with_gst',
        'packing_charge_with_gst',
        'shipping_freight_sgst_amount',
        'shipping_freight_cgst_amount',
        'shipping_freight_igst_amount',
        'packing_charge_sgst_amount',
        'packing_charge_cgst_amount',
        'packing_charge_igst_amount',
        'billing_state_id',
        'is_cgst_sgst_igst_calculated',
        'is_gst_na',
        'created_by',
        'taxable_value',
        'gross_value',
        'payment_status',
        'due_amount',
        'via_api',
        'is_import',
        'credit_period_due_date',
        'next_sent_at_reminder',
        'deleted_by',
        'tds_tax_id',
        'tds_rate',
        'tds_amount',
        'is_round_off_not_changed',
        'created_at',
        'updated_at',
        'round_off_method',
        'bank_id',
    ];

    public $casts = [
        'payment_mode' => 'integer',
        'date' => 'date',
        'brokerage_on_value_type' => 'integer',
        'credit_period' => 'integer',
        'credit_period_type' => 'integer',
        'dn_item_type' => 'integer',
        'po_date' => 'date',
        'brokerage_for_sale' => 'double',
        'shipping_fright' => 'double',
        'packing_charge' => 'double',
        'tcs_rate' => 'double',
        'tcs_amount' => 'double',
        'cgst' => 'double',
        'sgst' => 'double',
        'igst' => 'double',
        'rounding_amount' => 'double',
        'total' => 'double',
        'grand_total' => 'double',
        'original_inv_no' => 'integer',
        'original_inv_date' => 'date',
        'debit_note_number' => 'string',
        'full_invoice_number' => 'string',
        'gstin' => 'string',
        'transporter_document_number' => 'string',
        'narration' => 'string',
        'term_and_condition' => 'string',
        'payment_type_ledger_id' => 'integer',
        'is_gst_enabled' => 'integer',
        'transporter_document_date' => 'date',
        'cess' => 'double',
        'shipping_freight_with_gst' => 'double',
        'packing_charge_with_gst' => 'double',
        'shipping_freight_sgst_amount' => 'double',
        'shipping_freight_cgst_amount' => 'double',
        'shipping_freight_igst_amount' => 'double',
        'packing_charge_sgst_amount' => 'double',
        'packing_charge_cgst_amount' => 'double',
        'packing_charge_igst_amount' => 'double',
        'taxable_value' => 'double',
        'gross_value' => 'double',
        'due_amount' => 'double',
        'party_name_same_as_address_name' => 'boolean',
        'same_as_billing' => 'boolean',
        'round_off_method' => 'integer',
        'bank_id' => 'integer',
    ];

    // public $appends = ['taxable_value', 'due_amount', 'payment_status'];

    public $appends = ['paid_amount'];

    public static bool $withoutAppends = true;

    public function getIncomeDebitNoteFileAttribute(): array|string
    {
        $mediaUrl = [];
        /** @var Media $medias */
        $medias = $this->getMedia(self::INCOME_DEBIT_NOTE_DOCUMENT);

        foreach ($medias as $media) {
            $mediaUrl[] = $media->getFullUrl();
        }

        if (! empty($mediaUrl)) {
            return $mediaUrl;
        }

        return '';
    }

    public function scopeWithoutAppends($query)
    {
        self::$withoutAppends = false;

        return $query;
    }

    protected function getArrayableAppends()
    {
        if (self::$withoutAppends) {
            return $this->appends;
        }

        return parent::getArrayableAppends();
    }

    public function getInvoiceAttachmentAttribute(): array|string
    {
        $mediaUrl = [];
        /** @var Media $medias */
        $medias = $this->getMedia(self::INVOICE_ATTACHMENT);

        foreach ($medias as $media) {
            $mediaUrl[] = $media->getFullUrl();
        }

        if (! empty($mediaUrl)) {
            return $mediaUrl;
        }

        return '';
    }

    public function transformAudit(array $data): array
    {
        if ($data['event'] == 'created') {
            Arr::set($data, 'title', '<b>Income Debit Note</b> Invoice <b>'.$this->getAttribute('full_invoice_number').'</b> was created.');
        } elseif ($data['event'] == 'updated') {
            Arr::set($data, 'title', '<b>Income Debit Note</b> Invoice <b>'.$this->getAttribute('full_invoice_number').'</b> was edited.');
        } elseif ($data['event'] == 'deleted') {
            if (checkThisDeleted($data) == 'hard-delete' || checkThisDeleted($data) == 'bulk-delete' || checkThisDeleted($data) == 'empty-transaction' || checkThisDeleted($data) == 'empty-whole-bin') {
                Arr::set($data, 'title', '<b>Income Debit Note</b> Invoice <b>'.$this->getAttribute('full_invoice_number').'</b> was deleted from recycle bin.');
            } else {
                Arr::set($data, 'title', '<b>Income Debit Note</b> Invoice <b>'.$this->getAttribute('full_invoice_number').'</b> was moved to recycle bin.');
            }
        } elseif ($data['event'] == 'restored') {
            Arr::set($data, 'title', '<b>Income Debit Note</b> Invoice <b>'.$this->getAttribute('full_invoice_number').'</b> was restored from recycle bin.');
        }

        return $data;
    }

    public function incomeDebitNoteItems(): HasMany
    {
        return $this->hasMany(IncomeDebitNoteItemTransaction::class, 'income_dn_id');
    }

    public function incomeDebitNoteLedgers(): HasMany
    {
        return $this->hasMany(IncomeDebitNoteLedgerTransaction::class, 'income_dn_id');
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'customer_ledger_id', 'id');
    }

    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'model');
    }

    public function billingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::BILLING_ADDRESS);
    }

    public function dispatchAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'dispatch_address_id', 'id');
    }

    /** @return BelongsTo<Company> */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function shippingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::SHIPPING_ADDRESS);
    }

    public function transport(): BelongsTo
    {
        return $this->belongsTo(Transport::class, 'transport_id', 'id');
    }

    public function sale(): BelongsTo
    {
        return $this->belongsTo(SaleTransaction::class, 'original_inv_no', 'id');
    }

    public function brokerDetails(): BelongsTo
    {
        return $this->belongsTo(Broker::class, 'broker_id', 'id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by', 'id');
    }

    public function tcsLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'tcs_tax_id', 'id');
    }

    public function tdsLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'tds_tax_id', 'id');
    }

    public function paymentTypeLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'payment_type_ledger_id', 'id');
    }

    public function settleAdvancePayment(): HasMany
    {
        return $this->hasMany(SettleAdvancePayment::class, 'transaction_id', 'id')
            ->where('transaction_type', SettleAdvancePayment::INCOME_DEBIT_NOTE);
    }

    public function advancePayment(): MorphMany
    {
        return $this->morphMany(SettleAdvancePayment::class, 'model');
    }

    public function customFieldValues(): MorphMany
    {
        return $this->morphMany(TransactionCustomFieldValue::class, 'model');
    }

    public function bankLedgerDetails(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'bank_id', 'id');
    }

    public function getDayBookData(): array
    {
        return [
            'date' => $this->date->format('d-m-Y'),
            'particulars' => $this->customer->name,
            'voucher_type' => 'Income Dr. Note',
            'voucher_number' => $this->full_invoice_number,
            'debit_amount' => $this->grand_total,
            'credit_amount' => 0,
            'narration' => $this->narration,
            'ledger_id' => $this->customer_ledger_id,
        ];
    }

    public function receiptTransactionItem(): HasMany
    {
        return $this->hasMany(ReceiptTransactionItem::class, 'income_debit_id', 'id');
    }

    public function journalTransactionItem(): HasMany
    {
        return $this->hasMany(JournalCreditCustomerTransaction::class, 'income_debit_id', 'id');
    }

    public function paymentTransactionItem(): HasMany
    {
        return $this->hasMany(PaymentTransactionItem::class, 'income_debit_id', 'id');
    }

    protected function getTaxableValue(): Attribute
    {
        $taxableValue = 0;
        if ($this->dn_item_type == self::ACCOUNTING_INVOICE) {
            foreach ($this->incomeDebitNoteLedgers as $incomeDebitNoteLedgers) {
                $taxableValue += (float) $incomeDebitNoteLedgers->rpu_without_gst - (float) $incomeDebitNoteLedgers->total_discount_amount;
            }
        }

        if ($this->dn_item_type == self::ITEM_INVOICE) {
            foreach ($this->incomeDebitNoteItems as $incomeDebitNoteItems) {
                $taxableValue += ((float) $incomeDebitNoteItems->quantity * (float) $incomeDebitNoteItems->rpu_without_gst)
                    - (float) $incomeDebitNoteItems->total_discount_amount;
            }
        }

        if (! empty($this->shipping_freight) && $this->shipping_freight > 0) {
            $taxableValue += $this->shipping_freight;
        }

        if (! empty($this->packing_charge) && $this->packing_charge > 0) {
            $taxableValue += $this->packing_charge;
        }

        return Attribute::make(
            get: fn ($value) => $taxableValue,
        );
    }

    public function getLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-Y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sale Dr. Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => $this->grand_total,
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }

    public function getCgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sale Dr. Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->cgst,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getSgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sale Dr. Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->sgst,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getIgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sale Dr. Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->igst,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getPackingChargeReport(): array
    {
        $packingCharge = $this->packing_charge;

        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sale Dr. Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $packingCharge,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getFreightChargeReport(): array
    {
        $freightCharge = $this->shipping_freight;

        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sale Dr. Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $freightCharge,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getAdditionalChargeReport($value): array
    {
        $debit = 0;
        $credit = 0;
        if (IsNegative::run($value)) {
            $debit = abs($value);
        } else {
            $credit = $value;
        }

        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Sale Dr. Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => $debit,
            'credit_amount' => $credit,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getRoundOffReport(): array
    {
        if ($this->rounding_amount < 0) {
            $roundAmount = abs($this->rounding_amount);
            $data = [
                'transaction_id' => $this->id,
                'date' => $this->date->format('d-m-y'),
                'ledger_name' => $this->customer->name,
                'transaction_type' => 'Sale Dr. Note',
                'voucher_no' => $this->full_invoice_number,
                'invoice_no' => '',
                'debit_amount' => $roundAmount,
                'narration' => $this->narration ?? null,
                'credit_amount' => 0,
                'balance' => 0,
            ];
        } else {
            $data = [
                'transaction_id' => $this->id,
                'date' => $this->date->format('d-m-y'),
                'ledger_name' => $this->customer->name,
                'transaction_type' => 'Sale Dr. Note',
                'voucher_no' => $this->full_invoice_number,
                'invoice_no' => '',
                'debit_amount' => 0,
                'credit_amount' => $this->rounding_amount,
                'balance' => 0,
            ];
        }

        return $data;
    }

    public function getCustomerLedgerReport(): array
    {
        $data = [];
        $ledgerName = '';
        if ($this->dn_item_type == self::ITEM_INVOICE && $this->incomeDebitNoteItems->count() >= 1) {
            foreach ($this->incomeDebitNoteItems as $incomeDebitNoteItem) {
                $incomeDebitNoteItem->load('ledger');
                if (empty($data)) {
                    // If $data is empty, just add the first saleItem
                    $data[] = [
                        'name' => $incomeDebitNoteItem->ledger->name ?? '',
                        'amount' => $incomeDebitNoteItem->total ?? 0,
                    ];
                } else {
                    $found = false; // Flag to check if the ledger name is found in $data
                    foreach ($data as &$item) {
                        if ($item['name'] == $incomeDebitNoteItem->ledger->name) {
                            // If the ledger name matches, add the amount to existing data
                            $item['amount'] += $incomeDebitNoteItem->total ?? 0;
                            $found = true;
                            break; // Exit the loop once found
                        }
                    }
                    if (! $found) {
                        // If the ledger name is not found in $data, add it as a new entry
                        $data[] = [
                            'name' => $incomeDebitNoteItem->ledger->name ?? '',
                            'amount' => $incomeDebitNoteItem->total ?? 0,
                        ];
                    }
                }
            }
            $ledger = $this->incomeDebitNoteItems->firstWhere('ledger_id', '!=', null);
            $ledger?->load('ledger');
            $ledgerName = $ledger->ledger->name ?? '';
        }

        if ($this->dn_item_type == self::ACCOUNTING_INVOICE && $this->incomeDebitNoteLedgers->count() >= 1) {
            foreach ($this->incomeDebitNoteLedgers as $incomeDebitNoteLedger) {
                $incomeDebitNoteLedger->load('ledger');
                if (empty($data)) {
                    // If $data is empty, just add the first saleItem
                    $data[] = [
                        'name' => $incomeDebitNoteLedger->ledger->name ?? '',
                        'amount' => $incomeDebitNoteLedger->total ?? 0,
                    ];
                } else {
                    $found = false; // Flag to check if the ledger name is found in $data
                    foreach ($data as &$item) {
                        if ($item['name'] == $incomeDebitNoteLedger->ledger->name) {
                            // If the ledger name matches, add the amount to existing data
                            $item['amount'] += $incomeDebitNoteLedger->total ?? 0;
                            $found = true;
                            break; // Exit the loop once found
                        }
                    }
                    if (! $found) {
                        // If the ledger name is not found in $data, add it as a new entry
                        $data[] = [
                            'name' => $incomeDebitNoteLedger->ledger->name ?? '',
                            'amount' => $incomeDebitNoteLedger->total ?? 0,
                        ];
                    }
                }
            }
            $ledger = $this->incomeDebitNoteLedgers->firstWhere('ledger_id', '!=', null);
            $ledger?->load('ledger');
            $ledgerName = $ledger->ledger->name ?? '';
        }

        $data['ledger'] = $data;

        if (isCompanyGstApplicable()) {
            $data[] = [
                'name' => 'CGST',
                'amount' => $this->cgst,
            ];
            $data[] = [
                'name' => 'SGST',
                'amount' => $this->sgst,
            ];
            $data[] = [
                'name' => 'IGST',
                'amount' => $this->igst,
            ];
            $data[] = [
                'name' => 'Round Off',
                'amount' => $this->rounding_amount,
            ];
        }

        $additionalChargeAndAddless = [];
        foreach ($this->additionalCharges as $additionalCharge) {
            $additionalChargeAndAddless[] = [
                'name' => $additionalCharge->ledger->name ?? '',
                'amount' => $additionalCharge->total_without_tax,
            ];
        }

        foreach ($this->addLess as $addLess) {
            $additionalChargeAndAddless[] = [
                'name' => $addLess->ledger->name ?? '',
                'amount' => $addLess->total_without_tax,
            ];
        }

        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $ledgerName != '' ? $ledgerName : $this->customer->name,
            'transaction_type' => 'Income Dr. Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => $this->sale->full_invoice_number ?? '',
            'debit_amount' => $this->grand_total,
            'credit_amount' => 0,
            'balance' => 0,
            'tcs_amount' => $this->tcs_amount ?? 0,
            'cgst' => $this->cgst ?? 0,
            'sgst' => $this->sgst ?? 0,
            'igst' => $this->igst ?? 0,
            'additional_charges_addless' => $additionalChargeAndAddless,
            'cess' => $this->cess ?? 0,
            'rounding_amount' => $this->rounding_amount ?? 0,
            'ledger_list' => $data['ledger'],
            'data' => $data,
            'narration' => $this->narration ?? null,
            'payment_status' => $this->payment_status ?? null,
            'transaction_item_list' => $this->transactionItemListAsString(),
        ];
    }

    public function transactionItemListAsString(): string
    {
        return $this->incomeDebitNoteItems
            ->map(function ($transactionItem) {
                $itemName = $transactionItem->items?->item_name;
                $quantity = number_format($transactionItem->quantity, 2);
                $uom = $transactionItem->unit?->code ?? '';
                $totalDiscountAmount = number_format($transactionItem->total_discount_amount, 2);
                $rate = number_format($transactionItem->rpu_without_gst, 2);
                $total = number_format($transactionItem->total, 2);
                if ($totalDiscountAmount == 0) {
                    return "{$itemName}: {$quantity} {$uom} * {$rate} = {$total}";
                }

                return "{$itemName}: {$quantity} {$uom} * {$rate} - {$totalDiscountAmount} = {$total}";
            })
            ->filter()
            ->implode('<br>');
    }

    protected function getDueAmount(): Attribute
    {
        $totalDueAmount = 0;
        if ($this->payment_mode == self::CASH_MODE) {
            return Attribute::make(
                get: static fn ($value) => $totalDueAmount,
            );
        }

        $validReceiptTransactionItems = $this->receiptTransactionItem->filter(function ($item) {
            return $item->receiptTransaction && ! $item->receiptTransaction->trashed();
        });

        $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
            return $item->journalTransaction && ! $item->journalTransaction->trashed();
        });

        $receivedAmount = (
            ($validReceiptTransactionItems->sum('received_amount') ?? 0)
            + ($validReceiptTransactionItems->sum('discount') ?? 0)
            + ($validReceiptTransactionItems->sum('round_off') ?? 0)
            + ($validJournalTransactionItems->sum('received_amount') ?? 0)
            + ($validJournalTransactionItems->sum('discount') ?? 0)
            + ($validJournalTransactionItems->sum('round_off') ?? 0)
            + ($this->tds_amount ?? 0)
        );

        $receivedAmount = round($receivedAmount, getCompanyFixedDigitNumber());
        $grandTotal = round($this->grand_total, getCompanyFixedDigitNumber());

        return Attribute::make(
            get: static fn ($value) => $grandTotal - $receivedAmount,
        );
    }

    protected function paidAmount(): Attribute
    {
        $totalPaidAmount = 0;
        if ($this->payment_mode == self::CASH_MODE) {
            $totalPaidAmount = $this->grand_total;
        } else {
            $validReceiptTransactionItems = $this->receiptTransactionItem->filter(function ($item) {
                return $item->receiptTransaction && $item->deleted_at == null;
            });

            $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
                return $item->journalTransaction && $item->deleted_at == null;
            });

            $totalPaidAmount = (
                ($validReceiptTransactionItems->sum('received_amount') ?? 0)
                + ($validReceiptTransactionItems->sum('discount') ?? 0)
                + ($validReceiptTransactionItems->sum('round_off') ?? 0)
                + ($validJournalTransactionItems->sum('received_amount') ?? 0)
                + ($validJournalTransactionItems->sum('discount') ?? 0)
                + ($validJournalTransactionItems->sum('round_off') ?? 0)
                + ($this->tds_amount ?? 0)
                + ($this->advancePayment->sum('adjusted_amount') ?? 0)
            );
        }
        $totalPaidAmount = round($totalPaidAmount, getCompanyFixedDigitNumber());

        return Attribute::make(
            get: static fn ($value) => $totalPaidAmount,
        );
    }

    protected function getPaymentStatus(): Attribute
    {
        if ($this->payment_mode == self::CASH_MODE) {
            return Attribute::make(
                get: static fn ($value) => self::PAYMENT_STATUS_PAID,
            );
        }

        $paymentStatus = '';
        $validReceiptTransactionItems = $this->receiptTransactionItem->filter(function ($item) {
            return $item->receiptTransaction && ! $item->receiptTransaction->trashed();
        });

        $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
            return $item->journalTransaction && ! $item->journalTransaction->trashed();
        });

        $receivedAmount = (
            ($validReceiptTransactionItems->sum('received_amount') ?? 0)
            + ($validReceiptTransactionItems->sum('discount') ?? 0)
            + ($validReceiptTransactionItems->sum('round_off') ?? 0)
            + ($validJournalTransactionItems->sum('received_amount') ?? 0)
            + ($validJournalTransactionItems->sum('discount') ?? 0)
            + ($validJournalTransactionItems->sum('round_off') ?? 0)
            + ($this->tds_amount ?? 0)
            + ($this->advancePayment->sum('adjusted_amount') ?? 0)
        );

        $receivedAmount = round($receivedAmount, getCompanyFixedDigitNumber());
        $totalAmount = round($this->grand_total, getCompanyFixedDigitNumber());

        if ($receivedAmount >= $totalAmount) {
            $paymentStatus = self::PAYMENT_STATUS_PAID;
        } elseif ($receivedAmount == 0) {
            $paymentStatus = self::PAYMENT_STATUS_UNPAID;
        } elseif ($receivedAmount <= $totalAmount) {
            $paymentStatus = self::PAYMENT_STATUS_PARTLY_UNPAID;
        }

        return Attribute::make(
            get: static fn ($value) => $paymentStatus,
        );
    }

    public function getTcsReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Income Debit Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->tcs_amount,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getTdsLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Income Debit Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => $this->tds_amount,
            'credit_amount' => 0,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getCustomerTdsLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->tdsLedger->name,
            'transaction_type' => 'Income Dr. Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->tds_amount,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getCessReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->date->format('d-m-y'),
            'ledger_name' => $this->customer->name,
            'transaction_type' => 'Income Debit Note',
            'voucher_no' => $this->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'credit_amount' => $this->cess,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function prepareShippingAmount()
    {
        $prepareData = [];
        $prepareNewData = [];
        if ($this->shipping_freight != 0 && empty($this->shipping_freight)) {
            return $prepareNewData;
        }

        if ($this->dn_item_type == self::ITEM_INVOICE) {
            $transactionItems = $this->incomeDebitNoteItems;
            $totalTaxableAmount = $this->incomeDebitNoteItems->sum('taxable_value') ?? 0;
            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }
            if ($transactionItems->count() >= 1) {
                foreach ($transactionItems as $incomeDebitNoteItems) {
                    $packingAmount = ($totalTaxableAmount != 0) ? ($incomeDebitNoteItems->taxable_value * $this->shipping_freight) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $incomeDebitNoteItems->gst_tax_percentage ?? 0,
                        'shipping_amount' => $packingAmount,
                        'shipping_tax_amount' => ($packingAmount * 18) / 100,
                    ];
                }
            }
        } else {
            $transactionLedger = $this->incomeDebitNoteLedgers;
            $totalTaxableAmount = $this->incomeDebitNoteLedgers->sum('taxable_value') ?? 0;
            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }
            if ($transactionLedger->count() >= 1) {
                foreach ($transactionLedger as $incomeDebitNoteLedgers) {
                    $packingAmount = ($totalTaxableAmount != 0) ? ($incomeDebitNoteLedgers->taxable_value * $this->shipping_freight) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $incomeDebitNoteLedgers->gst_tax_percentage ?? 0,
                        'shipping_amount' => $packingAmount,
                        'shipping_tax_amount' => ($packingAmount * 18) / 100,
                    ];
                }
            }
        }

        $gstTaxArray = [];
        $totalShippingAmount = 0;
        $totalShippingTaxAmount = 0;

        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalShippingAmount += $checkGstTax['shipping_amount'];
                        $totalShippingTaxAmount += $checkGstTax['shipping_tax_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->customer->name,
                    'ledger_id' => $this->customer_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'transaction_type' => 'Income Dr. Note',
                    'ledger_name' => 'Shipping Charge',
                    'item_name' => 'Shipping Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '996812', // Fix HSN code for shipping and freight after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->incomeDebitNoteItems->first()->items->model->hsn_sac_code) ? $this->incomeDebitNoteItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $totalShippingAmount,
                    'rate_of_gst' => 18,
                    'cgst' => (! empty($this->cgst) && $this->cgst != 0) ? ($totalShippingTaxAmount / 2) : '0',
                    'sgst' => (! empty($this->cgst) && $this->sgst != 0) ? ($totalShippingTaxAmount / 2) : '0',
                    'igst' => (! empty($this->igst) && $this->igst != 0) ? ($totalShippingTaxAmount) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'rcm_yes_or_no' => $this->incomeDebitNoteItems->first()->classification_is_rcm_applicable ?? 'NO',
                    'invoice_amount' => $this->grand_total,
                ];
                $totalShippingAmount = 0;
                $totalShippingTaxAmount = 0;
            } else {

                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->customer->name,
                    'ledger_id' => $this->customer_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'transaction_type' => 'Income Dr. Note',
                    'ledger_name' => 'Shipping Charge',
                    'item_name' => 'Shipping Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '996812', // Fix HSN code for shipping and freight after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->incomeDebitNoteItems->first()->items->model->hsn_sac_code) ? $this->incomeDebitNoteItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $data['shipping_amount'],
                    'rate_of_gst' => 18,
                    'cgst' => (! empty($this->cgst) || $this->cgst != 0) ? ($data['shipping_tax_amount'] / 2) : '0',
                    'sgst' => (! empty($this->cgst) || $this->sgst != 0) ? ($data['shipping_tax_amount'] / 2) : '0',
                    'igst' => (! empty($this->igst) || $this->igst != 0) ? ($data['shipping_tax_amount']) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'rcm_yes_or_no' => $this->incomeDebitNoteItems->first()->classification_is_rcm_applicable ?? 'NO',
                    'invoice_amount' => $this->grand_total,
                ];
            }
        }

        return $prepareNewData;
    }

    public function preparePackingAmount()
    {
        $prepareData = [];
        $prepareNewData = [];
        if ($this->packing_charge != 0 && empty($this->packing_charge)) {
            return $prepareNewData;
        }

        if ($this->dn_item_type == self::ITEM_INVOICE) {
            $transactionItems = $this->incomeDebitNoteItems;
            $totalTaxableAmount = $this->incomeDebitNoteItems->sum('taxable_value') ?? 0;

            if ($transactionItems->count() >= 1) {
                foreach ($transactionItems as $incomeDebitNoteItems) {
                    $packingAmount = ($totalTaxableAmount != 0) ? ($incomeDebitNoteItems->taxable_value * $this->packing_charge) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $incomeDebitNoteItems->gst_tax_percentage ?? 0,
                        'packing_amount' => $packingAmount,
                        'packing_tax_amount' => ($packingAmount * 18) / 100,
                    ];
                }
            }
        } else {
            $transactionLedger = $this->incomeDebitNoteLedgers;
            $totalTaxableAmount = $this->incomeDebitNoteLedgers->sum('taxable_value') ?? 0;

            if ($transactionLedger->count() >= 1) {
                foreach ($transactionLedger as $incomeDebitNoteLedgers) {
                    $packingAmount = ($totalTaxableAmount != 0) ? ($incomeDebitNoteLedgers->taxable_value * $this->packing_charge) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $incomeDebitNoteLedgers->gst_tax_percentage ?? 0,
                        'packing_amount' => $packingAmount,
                        'packing_tax_amount' => ($packingAmount * 18) / 100,
                    ];
                }
            }
        }

        if ($totalTaxableAmount == 0) {
            return $prepareNewData;
        }

        $gstTaxArray = [];
        $totalPackingAmount = 0;
        $totalPackingTaxAmount = 0;
        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalPackingAmount += $checkGstTax['packing_amount'];
                        $totalPackingTaxAmount += $checkGstTax['packing_tax_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->customer->name,
                    'ledger_id' => $this->customer_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'transaction_type' => 'Income Dr. Note',
                    'ledger_name' => 'Packing Charge',
                    'item_name' => 'Packing Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '998541', // Fix HSN code for packing charge after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->incomeDebitNoteItems->first()->items->model->hsn_sac_code) ? $this->incomeDebitNoteItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $totalPackingAmount,
                    'rate_of_gst' => 18,
                    'cgst' => (! empty($this->cgst) && $this->cgst != 0) ? ($totalPackingTaxAmount / 2) : '0',
                    'sgst' => (! empty($this->cgst) && $this->sgst != 0) ? ($totalPackingTaxAmount / 2) : '0',
                    'igst' => (! empty($this->igst) && $this->igst != 0) ? ($totalPackingTaxAmount) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'rcm_yes_or_no' => $this->incomeDebitNoteItems->first()->classification_is_rcm_applicable ?? 'NO',
                    'invoice_amount' => $this->grand_total,
                ];
                $totalPackingAmount = 0;
                $totalPackingTaxAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->customer->name,
                    'ledger_id' => $this->customer_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'transaction_type' => 'Income Dr. Note',
                    'ledger_name' => 'Packing Charge',
                    'item_name' => 'Packing Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '998541', // Fix HSN code for packing charge after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->incomeDebitNoteItems->first()->items->model->hsn_sac_code) ? $this->incomeDebitNoteItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $data['packing_amount'],
                    'rate_of_gst' => 18,
                    'cgst' => (! empty($this->cgst) || $this->cgst != 0) ? ($data['packing_tax_amount'] / 2) : '0',
                    'sgst' => (! empty($this->cgst) || $this->sgst != 0) ? ($data['packing_tax_amount'] / 2) : '0',
                    'igst' => (! empty($this->igst) || $this->igst != 0) ? ($data['packing_tax_amount']) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'rcm_yes_or_no' => $this->incomeDebitNoteItems->first()->classification_is_rcm_applicable ?? 'NO',
                    'invoice_amount' => $this->grand_total,
                ];
            }
        }

        return $prepareNewData;
    }

    public function getb2bSezdeTransactionsData()
    {
        $prepareData = [];
        $prepareNewData = [];
        $isRcmApplicable = SaleTransaction::REVERSE_CHARGE_APPLICABLE_NO;
        $classificationNatureType = '';
        if ($this->dn_item_type == self::ITEM_INVOICE && $this->incomeDebitNoteItems->count()) {
            $isRcmApplicable = $this->incomeDebitNoteItems->first()->classification_is_rcm_applicable ?? $isRcmApplicable;
            $classificationNatureType = $this->incomeDebitNoteItems->first()->classificationNatureType->name ?? '';
            $transactionItems = $this->incomeDebitNoteItems->where('gst_id', '!=', 12);
            $totalTaxableAmount = $transactionItems->sum('taxable_value') ?? 0;

            foreach ($transactionItems as $debitNoteItem) {
                $prepareData[] = [
                    'gst_tax' => $debitNoteItem->gst_tax_percentage ?? 0,
                    'taxable_amount' => $debitNoteItem->taxable_value,
                ];
            }
        }
        if ($this->dn_item_type == self::ACCOUNTING_INVOICE && $this->incomeDebitNoteLedgers->count()) {
            $isRcmApplicable = $this->incomeDebitNoteLedgers->first()->classification_is_rcm_applicable ?? $isRcmApplicable;
            $classificationNatureType = $this->incomeDebitNoteLedgers->first()->classificationNatureType->name ?? '';
            $transactionItems = $this->incomeDebitNoteLedgers->where('gst_id', '!=', 12);
            $totalTaxableAmount = $transactionItems->sum('taxable_value') ?? 0;

            foreach ($transactionItems as $debitNoteItem) {
                $prepareData[] = [
                    'gst_tax' => $debitNoteItem->gst_tax_percentage ?? 0,
                    'taxable_amount' => $debitNoteItem->taxable_value,
                ];
            }
        }
        $additionalChargesData = $this->additionalCharges->where('gst_percentage', '!=', 0)->where('gst_rate_id', '!=', 12);
        $totalTaxableAmount = $totalTaxableAmount + ($additionalChargesData->sum('total') ?? 0);
        foreach ($additionalChargesData as $data) {
            $prepareData[] = [
                'gst_tax' => (float) $data->gst_percentage,
                'taxable_amount' => $data->total_without_tax,
            ];
        }

        $stateId = $this->addresses()->where('address_type', self::BILLING_ADDRESS)->first()->state_id ?? '';
        $gstTaxArray = [];
        $totalTaxAbleAmount = 0;
        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalTaxAbleAmount += $checkGstTax['taxable_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'gst' => $this->gstin ?? '',
                    'name' => $this->customer->name,
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'invoice_amount' => ($this->grand_total),
                    'cgst' => $this->cgst,
                    'sgst' => $this->sgst,
                    'igst' => $this->igst,
                    'place_of_supplier' => getStateNameWithCode($stateId),
                    'rcm' => $isRcmApplicable,
                    'invoice_type' => getInvoiceType($classificationNatureType),
                    'e_commerce_gstin' => '',
                    'gst_rate' => $data['gst_tax'],
                    'taxable_value' => $totalTaxAbleAmount,
                    'cess' => ($this->cess),
                    'transaction_type' => 'D',
                    'uniqueKey' => $this->id.'-'.'incomeDebitNote',
                    'tr_type' => 'income_dr_note',   // Transaction Type
                    'tr_id' => $this->id,           // Transaction Id
                    'items' => $this->incomeDebitNoteItems->toArray(),
                    'ledgers' => $this->incomeDebitNoteLedgers->toArray(),
                    'additionalCharges' => $additionalChargesData->toArray(),
                    'addLess' => $this->addLess->toArray(),
                ];
                $totalTaxAbleAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'gst' => $this->gstin ?? '',
                    'name' => $this->customer->name,
                    'invoice_number' => $this->full_invoice_number,
                    'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                    'invoice_amount' => ($this->grand_total),
                    'cgst' => $this->cgst,
                    'sgst' => $this->sgst,
                    'igst' => $this->igst,
                    'place_of_supplier' => getStateNameWithCode($stateId),
                    'rcm' => $isRcmApplicable,
                    'invoice_type' => getInvoiceType($classificationNatureType),
                    'e_commerce_gstin' => '',
                    'gst_rate' => $data['gst_tax'],
                    'taxable_value' => $data['taxable_amount'],
                    'cess' => ($this->cess),
                    'transaction_type' => 'D',
                    'uniqueKey' => $this->id.'-'.'incomeDebitNote',
                    'tr_type' => 'income_dr_note',   // Transaction Type
                    'tr_id' => $this->id,           // Transaction Id
                    'items' => $this->incomeDebitNoteItems->toArray(),
                    'ledgers' => $this->incomeDebitNoteLedgers->toArray(),
                    'additionalCharges' => $additionalChargesData->toArray(),
                    'addLess' => $this->addLess->toArray(),
                ];
            }
        }

        return $prepareNewData;
    }

    public function getPrimaryPhone()
    {
        $phone = null;

        if (isset($this->customer->model->phone_1)) {
            $phone = $this->customer->model->region_code_1.$this->customer->model->phone_1;
        } elseif (isset($this->customer->model->phone_2)) {
            $phone = $this->customer->model->region_code_2.$this->customer->model->phone_2;
        }

        return $phone;
    }

    public function prepareAttributes()
    {
        $data = [
            'party_name' => $this->customer->name,
            'invoice_number' => $this->full_invoice_number,
            'date' => Carbon::parse($this->date)->format('d-m-Y'),
            'grand_total' => $this->grand_total,
            'phone_no' => $this->getPrimaryPhone(),
            'payment_status' => $this->paymentStatus,
            'taxable_value' => $this->taxable_value,
            'cgst' => $this->cgst,
            'sgst' => $this->sgst,
            'igst' => $this->igst,
            'tcs_rate' => $this->tcs_rate,
            'tcs_amount' => $this->tcs_amount,
            'tcs_ledger' => $this->tcsLedger->name ?? '',
            'rounding_amount' => $this->rounding_amount,
            'total' => $this->total,
            'shipping_freight' => $this->shipping_freight,
            'shipping_freight_with_gst' => $this->shipping_freight_with_gst,
            'packing_charge' => $this->packing_charge,
            'packing_charge_with_gst' => $this->packing_charge_with_gst,
        ];

        if ($this->dn_item_type == self::ITEM_INVOICE) {
            $items = $this->incomeDebitNoteItems;

            foreach ($items as $item) {
                $data['items'][] = [
                    'id' => $item->id,
                    'item_name' => $item->items->item_name,
                    'ledger_name' => $item->ledger->name,
                    'quantity' => $item->quantity,
                    'rpu_without_gst' => $item->rpu_without_gst,
                    'gst_tax_percentage' => $item->gst_tax_percentage,
                    'total_discount_amount' => $item->total_discount_amount,
                    'rpu_with_gst' => $item->rpu_with_gst,
                    'cgst' => $item->classification_cgst_tax ?? 0,
                    'sgst' => $item->classification_sgst_tax ?? 0,
                    'igst' => $item->classification_igst_tax ?? 0,
                    'total' => $item->total,
                ];
            }
        } else {
            $ledgers = $this->incomeDebitNoteLedgers;
            foreach ($ledgers as $ledger) {
                $data['ledgers'][] = [
                    'id' => $ledger->id,
                    'ledger_name' => $ledger->ledgers->name,
                    'rpu_with_gst' => $ledger->rpu_with_gst,
                    'rpu_without_gst' => $ledger->rpu_without_gst,
                    'gst_tax_percentage' => $ledger->gst_tax_percentage,
                    'total_discount_amount' => $ledger->total_discount_amount,
                    'cgst' => $ledger->classification_cgst_tax ?? 0,
                    'sgst' => $ledger->classification_sgst_tax ?? 0,
                    'igst' => $ledger->classification_igst_tax ?? 0,
                ];
            }
        }

        return $data;
    }

    public function prepareData()
    {
        return [
            'transaction_id' => $this->id,
            'party_name' => $this->customer->name,
            'party_id' => $this->customer->id,
            'phone_no' => $this->getPrimaryPhone(),
            'invoice_number' => $this->full_invoice_number ?? '',
            'invoice_amount' => $this->grand_total,
            'payment_status' => $this->payment_status ?? '',
            'transaction_type' => 'Income Dr. Note',
            'date' => Carbon::parse($this->date)->format('d-m-Y') ?? '',
        ];
    }

    public function scopeFinancialYearDate(Builder $query)
    {
        $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
        $query->whereBetween('date', [$getCurrentFinancialYearDetails['yearStartDate'], $getCurrentFinancialYearDetails['yearEndDate']]);
    }

    public function scopeSorting($query, $sorting)
    {
        $sorting = getSortingValues($sorting);
        foreach ($sorting as $key => $value) {
            $query->orderBy($key, $value);
        }

        return $query;
    }

    public function additionalCharges(): HasMany
    {
        return $this->hasMany(AdditionalChargesForIncomeDebitNoteTransaction::class, 'income_debit_note_id');
    }

    public function addLess(): HasMany
    {
        return $this->hasMany(AddLessForIncomeDebitNoteTransaction::class, 'income_debit_note_id');
    }

    public function paymentDetails(): HasMany
    {
        return $this->hasMany(PaymentDetailsForIncomeDebitNoteTransaction::class, 'income_debit_note_id');
    }

    public function prepareAdditionalCharges($isGstCalculated = false)
    {
        $dataItem = [];
        $additionalCharges = $this->additionalCharges->where('gst_percentage', '!=', 0)->when($isGstCalculated, function ($query) {
            return $query->where('gst_rate_id', '!=', 12);
        });
        foreach ($additionalCharges as $transaction) {
            $gstData = GstCalculateAction::run($this, $transaction);
            $dataItem[] = [
                'party_name' => $this->customer->name,
                'gstin' => $this->gstin,
                'ledger_name' => $transaction->ledger->name,
                'item_name' => $transaction->ledger->name,
                'ledger_id' => $this->customer_ledger_id,
                'invoice_number' => $this->full_invoice_number,
                'invoice_date' => Carbon::parse($this->date)->format('d-m-Y'),
                'transaction_type' => 'Income Dr. Note',
                'hsn_code' => $transaction->ledger->model->hsn_sac_code,
                'rate_per_unit' => $transaction->total_without_tax,
                'item_description' => '', //
                'unit_of_measurement' => '',
                'quantity' => 0,
                'rate_of_gst' => $transaction->gst_percentage,
                'rcm_applicable_or_not' => '', //
                'taxable_value' => $transaction->total_without_tax,
                'cgst' => $gstData['cgst'],
                'sgst' => $gstData['sgst'],
                'igst' => $gstData['igst'],
                'cess_amount' => $this->cess_amount,
                'total_amount' => $transaction->value + $gstData['cgst'] + $gstData['sgst'] + $gstData['igst'] + $this->cess_amount,
                'invoice_amount' => $this->grand_total,
                'is_locked' => ! empty($this->date) ? isLockTransaction(LockTransaction::INCOME, Carbon::parse($this->date)->format('Y-m-d')) : false,
                'rcm_yes_or_no' => $this->incomeDebitNoteItems->first()->classification_is_rcm_applicable ?? 'NO',
            ];
        }

        return $dataItem;
    }
}
