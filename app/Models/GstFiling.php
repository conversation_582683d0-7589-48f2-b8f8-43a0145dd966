<?php

namespace App\Models;

use App\Traits\HasCompany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\GstFiling
 *
 * @property int $id
 * @property int $company_id
 * @property string $return_period
 * @property string $requestId
 * @property int $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|GstFiling newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GstFiling newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GstFiling query()
 * @method static \Illuminate\Database\Eloquent\Builder|GstFiling whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstFiling whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstFiling whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstFiling whereRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstFiling whereReturnPeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstFiling whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstFiling whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class GstFiling extends Model
{
    use HasCompany, HasFactory;

    protected $table = 'gst_filing';

    protected $fillable = [
        'company_id',
        'return_period',
        'requestId',
        'status',
        'meta_data',
    ];

    const P = 'P';

    const ER = 'ER';

    const PE = 'PE';

    const REC = 'REC';

    const IP = 'IP';

    const FILED = 'FILED';

    const STATUS = [
        self::P => 1,
        self::ER => 2,
        self::PE => 3,
        self::REC => 4,
        self::IP => 5,
        self::FILED => 6, // custom
    ];

    const STATUS_LABELS = [
        self::P => 'PROCESSED',
        self::ER => 'ERROR OCCURRED',
        self::PE => 'PROCESSED WITH ERROR',
        self::REC => 'RECEIVED BUT PENDING',
        self::IP => 'IN PROGRESS',
    ];
}
