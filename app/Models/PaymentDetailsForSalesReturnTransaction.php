<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentDetailsForSalesReturnTransaction extends Model
{
    use HasFactory;

    public $table = 'payment_details_for_sales_return_transactions';

    public $fillable = [
        'sale_return_id',
        'ledger_id',
        'date',
        'amount',
        'mode',
        'reference_no',
        'payment_id',
    ];

    /** @return BelongsTo<Ledger> */
    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class);
    }

    /** @return BelongsTo<SaleReturnTransaction> */
    public function saleReturn(): BelongsTo
    {
        return $this->belongsTo(SaleReturnTransaction::class);
    }
}
