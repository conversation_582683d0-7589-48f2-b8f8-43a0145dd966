<?php

namespace App\Models;

use App\Traits\HasCompany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\GstrLogin
 *
 * @property int $id
 * @property int $company_id
 * @property string|null $username
 * @property int|null $otp
 * @property int $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|GstrLogin newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GstrLogin newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GstrLogin query()
 * @method static \Illuminate\Database\Eloquent\Builder|GstrLogin whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstrLogin whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstrLogin whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstrLogin whereOtp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstrLogin whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstrLogin whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GstrLogin whereUsername($value)
 *
 * @mixin \Eloquent
 */
class GstrLogin extends Model
{
    use HasCompany, HasFactory;

    protected $table = 'gstr_logins';

    protected $fillable = [
        'company_id',
        'username',
        'otp',
        'status',
        'synced',
        'pan_number',
        'filing_type',
    ];

    public const DISCONNECTED = 0;

    public const CONNECTED = 1;
}
