<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentDetailsForPurchaseReturnTransaction extends Model
{
    use HasFactory;

    public $table = 'payment_details_for_purchase_return_transactions';

    public $fillable = [
        'purchase_return_id',
        'ledger_id',
        'date',
        'amount',
        'mode',
        'reference_no',
        'receipt_id',
    ];

    /** @return BelongsTo<Ledger> */
    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class);
    }

    /** @return BelongsTo<PurchaseReturnTransaction> */
    public function purchaseReturn(): BelongsTo
    {
        return $this->belongsTo(PurchaseReturnTransaction::class);
    }
}
