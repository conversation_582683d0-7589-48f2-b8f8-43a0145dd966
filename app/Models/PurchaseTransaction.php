<?php

namespace App\Models;

use App\Actions\CommonAction\GstCalculateAction;
use App\Actions\CommonAction\IsNegative;
use App\Models\Master\Broker;
use App\Models\Master\Transport;
use App\Traits\HasCompany;
use App\Traits\HasDeleted;
use App\Traits\HasJsonResourcefulData;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * App\Models\PurchaseTransaction
 *
 * @property int $id
 * @property int $company_id
 * @property Carbon|null $voucher_date
 * @property string|null $voucher_number
 * @property int|null $payment_mode
 * @property int|null $payment_type_ledger_id
 * @property string|null $sale_number
 * @property Carbon|null $date_of_invoice
 * @property int $supplier_ledger_id
 * @property string|null $gstin
 * @property int|null $broker_id
 * @property float|null $brokerage_for_sale
 * @property int|null $brokerage_on_value_type
 * @property int|null $transport_id
 * @property string|null $transporter_document_number
 * @property Carbon|null $transporter_document_date
 * @property int $purchase_item_type
 * @property float|null $shipping_freight
 * @property float|null $packing_charge
 * @property int|null $tcs_tax_id
 * @property float|null $tcs_rate
 * @property float|null $tcs_amount
 * @property float|null $cgst
 * @property float|null $sgst
 * @property float|null $igst
 * @property float $shipping_freight_with_gst
 * @property float $packing_charge_with_gst
 * @property float|null $shipping_freight_sgst_amount
 * @property float|null $shipping_freight_cgst_amount
 * @property float|null $shipping_freight_igst_amount
 * @property float|null $packing_charge_sgst_amount
 * @property float|null $packing_charge_cgst_amount
 * @property float|null $packing_charge_igst_amount
 * @property float|null $rounding_amount
 * @property float|null $total
 * @property float $grand_total
 * @property string|null $narration
 * @property string|null $term_and_condition
 * @property int|null $is_gst_enabled
 * @property int $pass_tds_entry
 * @property string|null $tds_pan
 * @property float|null $tds_taxable_value
 * @property int|null $ledger_of_tds
 * @property string|null $tds_rate
 * @property float|null $tds_amount
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property float $cess
 * @property float|null $cess_amount
 * @property string|null $paymentStatus
 * @property-read Collection|Address[] $addresses
 * @property-read int|null $addresses_count
 * @property-read Collection|ExpenseCreditNoteTransaction[] $creditNote
 * @property-read int|null $credit_note_count
 * @property-read string $purchase_file
 * @property-read MediaCollection|Media[] $media
 * @property-read int|null $media_count
 * @property-read Collection|PaymentTransactionItem[] $paymentTransactionItem
 * @property-read int|null $payment_transaction_item_count
 * @property-read Collection|PurchaseReturnTransaction[] $purchaseReturn
 * @property-read int|null $purchase_return_count
 * @property-read Collection|PurchaseItemTransaction[] $purchaseTransactionItems
 * @property-read int|null $purchase_transaction_items_count
 * @property-read Collection|PurchaseLedgerTransaction[] $purchaseTransactionLedger
 * @property-read int|null $purchase_transaction_ledger_count
 * @property-read SaleTransaction|null $saleTransaction
 * @property-read Ledger $supplier
 * @property-read Ledger|null $tdsLedger
 * @property-read Transport|null $transport
 *
 * @method static Builder|PurchaseTransaction newModelQuery()
 * @method static Builder|PurchaseTransaction newQuery()
 * @method static Builder|PurchaseTransaction query()
 * @method static Builder|PurchaseTransaction whereBrokerId($value)
 * @method static Builder|PurchaseTransaction whereBrokerageForSale($value)
 * @method static Builder|PurchaseTransaction whereBrokerageOnValueType($value)
 * @method static Builder|PurchaseTransaction whereCess($value)
 * @method static Builder|PurchaseTransaction whereCgst($value)
 * @method static Builder|PurchaseTransaction whereCompanyId($value)
 * @method static Builder|PurchaseTransaction whereCreatedAt($value)
 * @method static Builder|PurchaseTransaction whereDateOfInvoice($value)
 * @method static Builder|PurchaseTransaction whereGrandTotal($value)
 * @method static Builder|PurchaseTransaction whereGstin($value)
 * @method static Builder|PurchaseTransaction whereId($value)
 * @method static Builder|PurchaseTransaction whereIgst($value)
 * @method static Builder|PurchaseTransaction whereIsGstEnabled($value)
 * @method static Builder|PurchaseTransaction whereLedgerOfTds($value)
 * @method static Builder|PurchaseTransaction whereNarration($value)
 * @method static Builder|PurchaseTransaction wherePackingCharge($value)
 * @method static Builder|PurchaseTransaction wherePackingChargeCgstAmount($value)
 * @method static Builder|PurchaseTransaction wherePackingChargeIgstAmount($value)
 * @method static Builder|PurchaseTransaction wherePackingChargeSgstAmount($value)
 * @method static Builder|PurchaseTransaction wherePackingChargeWithGst($value)
 * @method static Builder|PurchaseTransaction wherePassTdsEntry($value)
 * @method static Builder|PurchaseTransaction wherePaymentMode($value)
 * @method static Builder|PurchaseTransaction wherePaymentTypeLedgerId($value)
 * @method static Builder|PurchaseTransaction wherePurchaseItemType($value)
 * @method static Builder|PurchaseTransaction whereRoundingAmount($value)
 * @method static Builder|PurchaseTransaction whereSaleNumber($value)
 * @method static Builder|PurchaseTransaction whereSgst($value)
 * @method static Builder|PurchaseTransaction whereShippingFreight($value)
 * @method static Builder|PurchaseTransaction whereShippingFreightCgstAmount($value)
 * @method static Builder|PurchaseTransaction whereShippingFreightIgstAmount($value)
 * @method static Builder|PurchaseTransaction whereShippingFreightSgstAmount($value)
 * @method static Builder|PurchaseTransaction whereShippingFreightWithGst($value)
 * @method static Builder|PurchaseTransaction whereSupplierLedgerId($value)
 * @method static Builder|PurchaseTransaction whereTcsAmount($value)
 * @method static Builder|PurchaseTransaction whereTcsRate($value)
 * @method static Builder|PurchaseTransaction whereTcsTaxId($value)
 * @method static Builder|PurchaseTransaction whereTdsAmount($value)
 * @method static Builder|PurchaseTransaction whereTdsPan($value)
 * @method static Builder|PurchaseTransaction whereTdsRate($value)
 * @method static Builder|PurchaseTransaction whereTdsTaxableValue($value)
 * @method static Builder|PurchaseTransaction whereTermAndCondition($value)
 * @method static Builder|PurchaseTransaction whereTotal($value)
 * @method static Builder|PurchaseTransaction whereTransportId($value)
 * @method static Builder|PurchaseTransaction whereTransporterDocumentDate($value)
 * @method static Builder|PurchaseTransaction whereTransporterDocumentNumber($value)
 * @method static Builder|PurchaseTransaction whereUpdatedAt($value)
 * @method static Builder|PurchaseTransaction whereVoucherDate($value)
 * @method static Builder|PurchaseTransaction whereVoucherNumber($value)
 * @method static Builder|PurchaseTransaction withoutAppends()
 *
 * @mixin Eloquent
 *
 * @property int $is_cgst_sgst_igst_calculated
 * @property-read Broker|null $brokerDetails
 * @property-read Collection|ExpenseDebitNoteTransaction[] $debitNote
 * @property-read int|null $debit_note_count
 * @property-read array|string $invoice_attachment
 * @property-read Collection|JournalDebitSupplierTransaction[] $journalTransactionItem
 * @property-read int|null $journal_transaction_item_count
 * @property-read float $paid_amount
 *
 * @method static Builder|PurchaseTransaction whereIsCgstSgstIgstCalculated($value)
 *
 * @property int $is_gst_na
 * @property bool $is_rcm_applicable
 *
 * @method static Builder|PurchaseTransaction whereIsGstNa($value)
 * @method static Builder|PurchaseTransaction whereIsRcmApplicable($value)
 */
class PurchaseTransaction extends Model implements Auditable, HasMedia
{
    use HasCompany, HasFactory, HasJsonResourcefulData, InteractsWithMedia;
    use HasDeleted, SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    /**
     * Attributes to exclude from the Audit.
     *
     * @var array
     */
    protected $auditExclude = [
        'taxable_value',
        'payment_status',
        'due_amount',
        'deleted_by',
    ];

    public $table = 'purchase_transactions';

    public const PURCHASE_INVOICE = 'purchase_invoice';

    public const TRANSACTION_TYPE = 'purchaseTransaction';

    public const CASH_MODE = 1;

    public const CREDIT_MODE = 2;

    public const PAYMENT_MODE = [
        self::CASH_MODE => 'Cash',
        self::CREDIT_MODE => 'Credit',
    ];

    public const BROKERAGE_ON_INVOICE_VALUE = 1;

    public const BROKERAGE_ON_TAXABLE_VALUE = 2;

    public const BROKERAGE_ON_GROSS_VALUE = 3;

    public const BROKERAGE_ON_VALUE_TYPE = [
        self::BROKERAGE_ON_INVOICE_VALUE => 'Invoice Value',
        self::BROKERAGE_ON_TAXABLE_VALUE => 'Taxable Value',
        self::BROKERAGE_ON_GROSS_VALUE => 'Gross Value',
    ];

    public const ACCOUNTING_INVOICE = 1;

    public const ITEM_INVOICE = 2;

    public const SALE_TRANSACTION_TYPE = [
        self::ACCOUNTING_INVOICE => 'Accounting Invoice',
        self::ITEM_INVOICE => 'Item Invoice',
    ];

    public const BILLING_ADDRESS = 1;

    public const SHIPPING_ADDRESS = 2;

    public const DISCOUNT_TYPE_AMOUNT = 1;

    public const DISCOUNT_TYPE_PERCENTAGE = 2;

    public const DISCOUNT_TYPES = [
        self::DISCOUNT_TYPE_AMOUNT => '₹',
        self::DISCOUNT_TYPE_PERCENTAGE => '%',
    ];

    public const REVERSE_CHARGE_APPLICABLE_YES = 1;

    public const REVERSE_CHARGE_APPLICABLE_NO = 0;

    public const REVERSE_CHARGE_APPLICABLE_TYPE = [
        self::REVERSE_CHARGE_APPLICABLE_YES => 'Yes',
        self::REVERSE_CHARGE_APPLICABLE_NO => 'No',
    ];

    public const PASS_TDS_ENTRY_YES = 1;

    public const PASS_TDS_ENTRY_NO = 0;

    public const ITC_APPLICATION_YES = 1;

    public const ITC_APPLICATION_NO = 0;

    public const ITC_APPLICABLE_TYPE = [
        self::ITC_APPLICATION_YES => 'Yes',
        self::ITC_APPLICATION_NO => 'No',
    ];

    public const REPORT_SUMMARY = 1;

    public const REPORT_DETAIL = 2;

    public const SAVE_BUTTON = 1;

    public const SAVE_AND_NEW_BUTTON = 2;

    public const SAVE_AND_PRINT_BUTTON = 3;

    public const PAYMENT_STATUS_ARR = [
        self::PAYMENT_STATUS_PAID => self::PAYMENT_STATUS_PAID,
        self::PAYMENT_STATUS_UNPAID => self::PAYMENT_STATUS_UNPAID,
        self::PAYMENT_STATUS_PARTLY_UNPAID => self::PAYMENT_STATUS_PARTLY_UNPAID,
    ];

    public const PURCHASE_TRANSACTION = 'Purchase';

    public const PURCHASE_RETURN_TRANSACTION = 'Purchase Return';

    public const EXPENSE_DEBIT_NOTE_TRANSACTION = 'Expense Dr. Note';

    public const EXPENSE_CREDIT_NOTE_TRANSACTION = 'Expense Cr. Note';

    public const PAYMENT_STATUS_PAID = 'Paid';

    public const PAYMENT_STATUS_UNPAID = 'Unpaid';

    public const PAYMENT_STATUS_PARTLY_UNPAID = 'Partially Unpaid';

    public const TYPE = 0;

    public const PURCHASES = 5;

    public const PURCHASES_RETURN = 6;

    public const DEBIT_NOTES = 7;

    public const CREDIT_NOTES = 8;

    public const TRANSACTION_TYPE_NAME = [
        self::TYPE => 'Type',
        self::PURCHASES => 'Purchase',
        self::PURCHASES_RETURN => 'Purchase Return',
        self::DEBIT_NOTES => 'Expense Dr. Note',
        self::CREDIT_NOTES => 'Expense Cr. Note',
    ];

    public const PAID = 1;

    public const UNPAID = 2;

    public const PARTLY_UNPAID = 3;

    public const PAYMENT_STATUS = [
        self::PAID => 'Paid',
        self::UNPAID => 'Unpaid',
        self::PARTLY_UNPAID => 'Partially Unpaid',
    ];

    public const INVOICE_ATTACHMENT = 'invoice_attachment';

    public $fillable = [
        'company_id',
        'voucher_date',
        'voucher_number',
        'payment_mode',
        'payment_type_ledger_id',
        'sale_number',
        'date_of_invoice',
        'supplier_ledger_id',
        'gstin',
        'broker_id',
        'brokerage_for_sale',
        'brokerage_on_value_type',
        'transport_id',
        'shipping_name',
        'shipping_gstin',
        'address_name',
        'party_name_same_as_address_name',
        'transporter_document_number',
        'transporter_document_date',
        'transporter_vehicle_number',
        'purchase_item_type',
        'shipping_freight',
        'packing_charge',
        'tcs_rate',
        'tcs_amount',
        'cgst',
        'sgst',
        'igst',
        'rounding_amount',
        'total',
        'grand_total',
        'narration',
        'term_and_condition',
        'shipping_address_id',
        'same_as_billing',
        'tcs_tax_id',
        'is_gst_enabled',
        'cess',
        'shipping_freight_with_gst',
        'packing_charge_with_gst',
        'pass_tds_entry',
        'tds_pan',
        'tds_taxable_value',
        'ledger_of_tds',
        'tds_rate',
        'tds_amount',
        'shipping_freight_sgst_amount',
        'shipping_freight_cgst_amount',
        'shipping_freight_igst_amount',
        'packing_charge_sgst_amount',
        'packing_charge_cgst_amount',
        'packing_charge_igst_amount',
        'is_cgst_sgst_igst_calculated',
        'is_gst_na',
        'is_rcm_applicable',
        'created_by',
        'taxable_value',
        'gross_value',
        'payment_status',
        'due_amount',
        'via_api',
        'is_import',
        'deleted_by',
        'is_round_off_not_changed',
        'purchase_order_no',
        'ocr_id',
        'created_at',
        'updated_at',
        'round_off_method',
    ];

    public $casts = [
        'payment_mode' => 'integer',
        'date_of_invoice' => 'date',
        'voucher_date' => 'date',
        'brokerage_on_value_type' => 'integer',
        'purchase_item_type' => 'integer',
        'brokerage_for_sale' => 'double',
        'shipping_fright' => 'double',
        'packing_charge' => 'double',
        'tcs_rate' => 'double',
        'tcs_amount' => 'double',
        'cgst' => 'double',
        'sgst' => 'double',
        'igst' => 'double',
        'rounding_amount' => 'double',
        'total' => 'double',
        'grand_total' => 'double',
        'gstin' => 'string',
        'transporter_document_number' => 'string',
        'narration' => 'string',
        'term_and_condition' => 'string',
        'payment_type_ledger_id' => 'integer',
        'is_gst_enabled' => 'integer',
        'transporter_document_date' => 'date',
        'sale_number' => 'string',
        'cess' => 'double',
        'shipping_freight_with_gst' => 'double',
        'packing_charge_with_gst' => 'double',
        'shipping_freight_sgst_amount' => 'double',
        'shipping_freight_cgst_amount' => 'double',
        'shipping_freight_igst_amount' => 'double',
        'packing_charge_sgst_amount' => 'double',
        'packing_charge_cgst_amount' => 'double',
        'packing_charge_igst_amount' => 'double',
        'is_rcm_applicable' => 'boolean',
        'taxable_value' => 'double',
        'gross_value' => 'double',
        'due_amount' => 'double',
        'party_name_same_as_address_name' => 'boolean',
        'same_as_billing' => 'boolean',
        'round_off_method' => 'integer',
    ];

    public $appends = ['paid_amount'];

    public function getInvoiceAttachmentAttribute(): array|string
    {
        $mediaUrl = [];
        /** @var Media $medias */
        $medias = $this->getMedia(self::INVOICE_ATTACHMENT);

        foreach ($medias as $media) {
            $mediaUrl[] = $media->getFullUrl();
        }

        if (! empty($mediaUrl)) {
            return $mediaUrl;
        }

        return '';
    }

    public function transformAudit(array $data): array
    {
        if ($data['event'] == 'created') {
            Arr::set($data, 'title', '<b>Purchase</b> Invoice <b>'.$this->getAttribute('voucher_number').'</b> was created.');
        } elseif ($data['event'] == 'updated') {
            Arr::set($data, 'title', '<b>Purchase</b> Invoice <b>'.$this->getAttribute('voucher_number').'</b> was edited.');
        } elseif ($data['event'] == 'deleted') {
            if (checkThisDeleted($data) == 'hard-delete' || checkThisDeleted($data) == 'bulk-delete' || checkThisDeleted($data) == 'empty-transaction' || checkThisDeleted($data) == 'empty-whole-bin') {
                Arr::set($data, 'title', '<b>Purchase</b> Invoice <b>'.$this->getAttribute('voucher_number').'</b> was deleted from recycle bin.');
            } else {
                Arr::set($data, 'title', '<b>Purchase</b> Invoice <b>'.$this->getAttribute('voucher_number').'</b> was moved to recycle bin.');
            }
        } elseif ($data['event'] == 'restored') {
            Arr::set($data, 'title', '<b>Purchase</b> Invoice <b>'.$this->getAttribute('voucher_number').'</b> was restored from recycle bin.');
        }

        return $data;
    }

    /** @return BelongsTo<Company> */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function purchaseTransactionItems(): HasMany
    {
        return $this->hasMany(PurchaseItemTransaction::class, 'purchase_transaction_id');
    }

    public function saleTransaction(): HasOne
    {
        return $this->hasOne(SaleTransaction::class, 'id', 'sale_transaction_id');
    }

    public function purchaseTransactionLedger(): HasMany
    {
        return $this->hasMany(PurchaseLedgerTransaction::class, 'purchase_transaction_id');
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'supplier_ledger_id', 'id');
    }

    public function tdsLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'ledger_of_tds', 'id');
    }

    public function tcsLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'tcs_tax_id', 'id');
    }

    public function addresses(): MorphMany
    {
        return $this->morphMany(Address::class, 'model');
    }

    public function billingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::BILLING_ADDRESS);
    }

    public function dispatchAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'model')->where('address_type', self::SHIPPING_ADDRESS);
    }

    public function purchaseReturn(): HasMany
    {

        return $this->hasMany(PurchaseReturnTransaction::class, 'original_inv_no', 'id');
    }

    public function debitNote(): HasMany
    {

        return $this->hasMany(ExpenseDebitNoteTransaction::class, 'original_inv_no', 'id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by', 'id');
    }

    public function additionalCharges(): HasMany
    {
        return $this->hasMany(AdditionalChargesForPurchaseTransaction::class, 'purchase_transaction_id');
    }

    public function settleAdvancePayment(): HasMany
    {
        return $this->hasMany(SettleAdvancePayment::class, 'transaction_id', 'id')
            ->where('transaction_type', SettleAdvancePayment::PURCHASE);
    }

    public function advancePayment(): MorphMany
    {
        return $this->morphMany(SettleAdvancePayment::class, 'model');
    }

    public function addLess(): HasMany
    {
        return $this->hasMany(AddLessForPurchaseTransaction::class, 'purchase_transaction_id');
    }

    public function paymentDetails(): HasMany
    {
        return $this->hasMany(PaymentDetailsForPurchaseTransaction::class, 'purchase_transaction_id');
    }

    public function paymentTypeLedger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'payment_type_ledger_id', 'id');
    }

    public function customFieldValues(): MorphMany
    {
        return $this->morphMany(TransactionCustomFieldValue::class, 'model');
    }

    public function getDayBookData(): array
    {
        return [
            'date' => $this->voucher_date->format('d-m-Y'),
            'particulars' => $this->supplier->name,
            'voucher_type' => 'Purchase',
            'voucher_number' => $this->voucher_number,
            'debit_amount' => 0,
            'credit_amount' => $this->grand_total,
            'narration' => $this->narration,
            'ledger_id' => $this->supplier_ledger_id,
        ];
    }

    public function paymentTransactionItem(): HasMany
    {
        return $this->hasMany(PaymentTransactionItem::class, 'purchase_id', 'id');
    }

    public function journalTransactionItem(): HasMany
    {
        return $this->hasMany(JournalDebitSupplierTransaction::class, 'purchase_id', 'id');
    }

    public function transport(): BelongsTo
    {
        return $this->belongsTo(Transport::class, 'transport_id', 'id');
    }

    public function brokerDetails(): BelongsTo
    {
        return $this->belongsTo(Broker::class, 'broker_id', 'id');
    }

    public function getPurchaseFileAttribute(): array|string
    {

        $mediaUrl = [];
        /** @var Media $medias */
        $medias = $this->getMedia(self::PURCHASE_INVOICE);

        foreach ($medias as $media) {
            $mediaUrl[] = $media->getFullUrl();
        }

        if (! empty($mediaUrl)) {
            return $mediaUrl;
        }

        return '';
    }

    protected function getPaymentStatus(): Attribute
    {
        if ($this->payment_mode == self::CASH_MODE) {
            return Attribute::make(
                get: static fn ($value) => self::PAYMENT_STATUS_PAID,
            );
        }

        $paymentStatus = '';

        $validPaymentTransactionItems = $this->paymentTransactionItem->filter(function ($item) {
            return $item->paymentTransaction && ! $item->paymentTransaction->trashed();
        });

        $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
            return $item->journalTransaction && ! $item->journalTransaction->trashed();
        });

        $paidAmount = (
            ($validPaymentTransactionItems->sum('paid_amount') ?? 0)
            + ($validPaymentTransactionItems->sum('discount') ?? 0)
            + ($validPaymentTransactionItems->sum('round_off') ?? 0)
            + ($validJournalTransactionItems->sum('paid_amount') ?? 0)
            + ($validJournalTransactionItems->sum('discount') ?? 0)
            + ($validJournalTransactionItems->sum('round_off') ?? 0)
            + ($this->purchaseReturn->sum('grand_total') ?? 0)
            + ($this->debitNote->sum('grand_total') ?? 0)
            + ($this->advancePayment->sum('adjusted_amount') ?? 0)
            + ($this->tds_amount ?? 0)
        );

        $paidAmount = round($paidAmount, getCompanyFixedDigitNumber());
        $totalAmount = round($this->grand_total, getCompanyFixedDigitNumber());

        if ($paidAmount >= $totalAmount) {
            $paymentStatus = self::PAYMENT_STATUS_PAID;
        } elseif ($paidAmount == 0) {
            $paymentStatus = self::PAYMENT_STATUS_UNPAID;
        } else {
            $paymentStatus = self::PAYMENT_STATUS_PARTLY_UNPAID;
        }

        return Attribute::make(
            get: static fn ($value) => $paymentStatus,
        );
    }

    protected function getDueAmount(): Attribute
    {
        if ($this->payment_mode == self::CASH_MODE) {
            return Attribute::make(
                get: static fn ($value) => 0.00,
            );
        }

        $validPaymentTransactionItems = $this->paymentTransactionItem->filter(function ($item) {
            return $item->paymentTransaction && ! $item->paymentTransaction->trashed();
        });

        $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
            return $item->journalTransaction && ! $item->journalTransaction->trashed();
        });

        $paidAmount = (
            ($validPaymentTransactionItems->sum('paid_amount') ?? 0)
            + ($validPaymentTransactionItems->sum('discount') ?? 0)
            + ($validPaymentTransactionItems->sum('round_off') ?? 0)
            + ($validJournalTransactionItems->sum('paid_amount') ?? 0)
            + ($validJournalTransactionItems->sum('discount') ?? 0)
            + ($validJournalTransactionItems->sum('round_off') ?? 0)
            + ($this->advancePayment->sum('adjusted_amount') ?? 0)
        ) + ($this->tds_amount ?? 0);

        $paidAmount = round($paidAmount, getCompanyFixedDigitNumber());
        $grandTotal = round($this->grand_total, getCompanyFixedDigitNumber());
        $purchaseReturnAmount = round($this->purchaseReturn->sum('grand_total') ?? 0, getCompanyFixedDigitNumber());
        $debitNoteAmount = round($this->debitNote->sum('grand_total') ?? 0, getCompanyFixedDigitNumber());

        return Attribute::make(
            get: static fn ($value) => $grandTotal - ($paidAmount + $purchaseReturnAmount + $debitNoteAmount),
        );
    }

    protected function paidAmount(): Attribute
    {
        if ($this->payment_mode == self::CASH_MODE) {
            $paidAmount = $this->grand_total;

            return Attribute::make(
                get: static fn ($value) => $paidAmount,
            );
        }

        $validPaymentTransactionItems = $this->paymentTransactionItem->filter(function ($item) {
            return $item->paymentTransaction && $item->deleted_at == null;
        });

        $validJournalTransactionItems = $this->journalTransactionItem->filter(function ($item) {
            return $item->journalTransaction && $item->deleted_at == null;
        });

        $paidAmount = (
            ($validPaymentTransactionItems->sum('paid_amount') ?? 0)
            + ($validPaymentTransactionItems->sum('discount') ?? 0)
            + ($validPaymentTransactionItems->sum('round_off') ?? 0)
            + ($validJournalTransactionItems->sum('paid_amount') ?? 0)
            + ($validJournalTransactionItems->sum('discount') ?? 0)
            + ($validJournalTransactionItems->sum('round_off') ?? 0)
            + ($this->advancePayment->sum('adjusted_amount') ?? 0)
            + ($this->tds_amount ?? 0)
        );

        $paidAmount = round($paidAmount, getCompanyFixedDigitNumber());
        $purchaseReturnAmount = round($this->purchaseReturn->sum('grand_total') ?? 0, getCompanyFixedDigitNumber());
        $debitNoteAmount = round($this->debitNote->sum('grand_total') ?? 0, getCompanyFixedDigitNumber());

        return Attribute::make(
            get: static fn ($value) => $paidAmount + $purchaseReturnAmount + $debitNoteAmount,
        );
    }

    protected function getTaxableValue(): Attribute
    {
        $taxableValue = 0;
        if ($this->purchase_item_type == self::ACCOUNTING_INVOICE) {
            foreach ($this->purchaseTransactionLedger as $purchaseTransactionLedger) {
                $taxableValue += (float) $purchaseTransactionLedger->rpu_without_gst - (float) $purchaseTransactionLedger->total_discount_amount;
            }
        }

        if ($this->purchase_item_type == self::ITEM_INVOICE) {
            foreach ($this->purchaseTransactionItems as $purchaseTransactionItem) {
                $taxableValue += ((float) $purchaseTransactionItem->quantity * (float) $purchaseTransactionItem->rpu_without_gst)
                    - (float) $purchaseTransactionItem->total_discount_amount;
            }
        }
        if (! empty($this->shipping_freight) && $this->shipping_freight > 0) {
            $taxableValue += $this->shipping_freight;
        }

        if (! empty($this->packing_charge) && $this->packing_charge > 0) {
            $taxableValue += $this->packing_charge;
        }

        return Attribute::make(
            get: fn ($value) => $taxableValue,
        );
    }

    public function getLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-Y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->sale_number,
            'debit_amount' => 0,
            'credit_amount' => $this->grand_total,
            'balance' => 0,
        ];
    }

    public function getTdsLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->sale_number,
            'debit_amount' => 0,
            'credit_amount' => $this->tds_amount,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getSupplierTdsLedgerReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->tdsLedger->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->sale_number ?? '',
            'debit_amount' => $this->tds_amount,
            'credit_amount' => 0,
            'balance' => 0,
        ];
    }

    public function getCgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->sale_number,
            'debit_amount' => $this->cgst,
            'credit_amount' => 0,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getSgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->sale_number,
            'debit_amount' => $this->sgst,
            'credit_amount' => 0,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getIgstReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->sale_number,
            'debit_amount' => $this->igst,
            'credit_amount' => 0,
            'balance' => 0,
            'narration' => $this->narration,
        ];
    }

    public function getPackingChargeReport(): array
    {
        $packingCharge = $this->packing_charge;

        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->sale_number,
            'debit_amount' => $packingCharge,
            'credit_amount' => 0,
            'narration' => $this->narration ?? null,
            'balance' => 0,
        ];
    }

    public function getFreightChargeReport(): array
    {
        $freightCharge = $this->shipping_freight;

        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->sale_number,
            'debit_amount' => $freightCharge,
            'credit_amount' => 0,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getAdditionalChargeReport($value): array
    {
        $debit = 0;
        $credit = 0;
        if (IsNegative::run($value)) {
            $credit = abs($value);
        } else {
            $debit = $value;
        }

        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->sale_number,
            'debit_amount' => $debit,
            'credit_amount' => $credit,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getRoundOffReport(): array
    {
        if ($this->rounding_amount < 0) {
            $roundAmount = abs($this->rounding_amount);
            $data = [
                'transaction_id' => $this->id,
                'date' => $this->voucher_date->format('d-m-y'),
                'ledger_name' => $this->supplier->name,
                'transaction_type' => 'Purchase',
                'voucher_no' => $this->voucher_number,
                'invoice_no' => $this->sale_number,
                'debit_amount' => 0,
                'credit_amount' => $roundAmount,
                'narration' => $this->narration ?? null,
                'balance' => 0,
            ];
        } else {
            $data = [
                'transaction_id' => $this->id,
                'date' => $this->voucher_date->format('d-m-y'),
                'ledger_name' => $this->supplier->name,
                'transaction_type' => 'Purchase',
                'voucher_no' => $this->voucher_number,
                'invoice_no' => $this->sale_number,
                'debit_amount' => $this->rounding_amount,
                'credit_amount' => 0,
                'balance' => 0,
            ];
        }

        return $data;
    }

    public function getSupplierLedgerReport(): array
    {
        $data = [];
        $ledgerName = '';
        if ($this->purchase_item_type == self::ITEM_INVOICE && $this->purchaseTransactionItems->count() >= 1) {
            foreach ($this->purchaseTransactionItems as $purchaseTransactionItem) {
                $purchaseTransactionItem->load('ledger');
                if (empty($data)) {
                    // If $data is empty, just add the first saleItem
                    $data[] = [
                        'name' => $purchaseTransactionItem->ledger->name ?? '',
                        'amount' => $purchaseTransactionItem->total ?? 0,
                    ];
                } else {
                    $found = false; // Flag to check if the ledger name is found in $data
                    foreach ($data as &$item) {
                        if ($item['name'] == $purchaseTransactionItem->ledger->name) {
                            // If the ledger name matches, add the amount to existing data
                            $item['amount'] += $purchaseTransactionItem->total ?? 0;
                            $found = true;
                            break; // Exit the loop once found
                        }
                    }
                    if (! $found) {
                        // If the ledger name is not found in $data, add it as a new entry
                        $data[] = [
                            'name' => $purchaseTransactionItem->ledger->name ?? '',
                            'amount' => $purchaseTransactionItem->total ?? 0,
                        ];
                    }
                }
            }
            $ledger = $this->purchaseTransactionItems->firstWhere('ledger_id', '!=', null);
            $ledger?->load('ledger');
            $ledgerName = $ledger->ledger->name ?? '';
        }

        if ($this->purchase_item_type == self::ACCOUNTING_INVOICE && $this->purchaseTransactionLedger->count() >= 1) {
            foreach ($this->purchaseTransactionLedger as $purchaseTransactionLedger) {
                $purchaseTransactionLedger->load('ledger');
                if (empty($data)) {
                    // If $data is empty, just add the first saleItem
                    $data[] = [
                        'name' => $purchaseTransactionLedger->ledger->name ?? '',
                        'amount' => $purchaseTransactionLedger->total ?? 0,
                    ];
                } else {
                    $found = false; // Flag to check if the ledger name is found in $data
                    foreach ($data as &$item) {
                        if ($item['name'] == $purchaseTransactionLedger->ledger->name) {
                            // If the ledger name matches, add the amount to existing data
                            $item['amount'] += $purchaseTransactionLedger->total ?? 0;
                            $found = true;
                            break; // Exit the loop once found
                        }
                    }
                    if (! $found) {
                        // If the ledger name is not found in $data, add it as a new entry
                        $data[] = [
                            'name' => $purchaseTransactionLedger->ledger->name ?? '',
                            'amount' => $purchaseTransactionLedger->total ?? 0,
                        ];
                    }
                }
            }
            $ledger = $this->purchaseTransactionLedger->firstWhere('ledger_id', '!=', null);
            $ledger?->load('ledger');
            $ledgerName = $ledger->ledger->name ?? '';
        }

        $data['ledger'] = $data;

        if (isCompanyGstApplicable()) {
            $data[] = [
                'name' => 'CGST',
                'amount' => $this->cgst,
            ];
            $data[] = [
                'name' => 'SGST',
                'amount' => $this->sgst,
            ];
            $data[] = [
                'name' => 'IGST',
                'amount' => $this->igst,
            ];
            $data[] = [
                'name' => 'Round Off',
                'amount' => $this->rounding_amount,
            ];
        }

        $additionalChargeAndAddless = [];
        foreach ($this->additionalCharges as $additionalCharge) {
            $additionalChargeAndAddless[] = [
                'name' => $additionalCharge->ledger->name ?? '',
                'amount' => $additionalCharge->total_without_tax,
            ];
        }

        foreach ($this->addLess as $addLess) {
            $additionalChargeAndAddless[] = [
                'name' => $addLess->ledger->name ?? '',
                'amount' => $addLess->total_without_tax,
            ];
        }

        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $ledgerName != '' ? $ledgerName : $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => $this->sale_number,
            'debit_amount' => 0,
            'credit_amount' => $this->grand_total,
            'balance' => 0,
            'tcs_amount' => $this->tcs_amount ?? 0,
            'cgst' => $this->cgst ?? 0,
            'sgst' => $this->sgst ?? 0,
            'igst' => $this->igst ?? 0,
            'additional_charges_addless' => $additionalChargeAndAddless,
            'cess' => $this->cess ?? 0,
            'rounding_amount' => $this->rounding_amount ?? 0,
            'ledger_list' => $data['ledger'],
            'data' => $data,
            'narration' => $this->narration ?? null,
            'payment_status' => $this->payment_status ?? null,
            'transaction_item_list' => $this->transactionItemListAsString(),
        ];
    }

    public function transactionItemListAsString(): string
    {
        return $this->purchaseTransactionItems
            ->map(function ($transactionItem) {
                $itemName = $transactionItem->items?->item_name;
                $quantity = number_format($transactionItem->quantity, 2);
                $uom = $transactionItem->unit?->code ?? '';
                $totalDiscountAmount = number_format($transactionItem->total_discount_amount, 2);
                $rate = number_format($transactionItem->rpu_without_gst, 2);
                $total = number_format($transactionItem->total, 2);
                if ($totalDiscountAmount == 0) {
                    return "{$itemName}: {$quantity} {$uom} * {$rate} = {$total}";
                }

                return "{$itemName}: {$quantity} {$uom} * {$rate} - {$totalDiscountAmount} = {$total}";
            })
            ->filter()
            ->implode('<br>');
    }

    public static bool $withoutAppends = true;

    public function scopeWithoutAppends($query)
    {
        self::$withoutAppends = false;

        return $query;
    }

    protected function getArrayableAppends()
    {
        if (self::$withoutAppends) {
            return $this->appends;
        }

        return parent::getArrayableAppends();
    }

    public function getCessReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => '',
            'debit_amount' => $this->cess,
            'credit_amount' => 0,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function prepareShippingAmount(): array
    {
        $prepareData = [];
        $prepareNewData = [];

        if ($this->shipping_freight != 0 && empty($this->shipping_freight)) {
            return $prepareNewData;
        }

        if ($this->purchase_item_type == self::ITEM_INVOICE) {
            $transactionItems = $this->purchaseTransactionItems;
            $totalTaxableAmount = $this->purchaseTransactionItems->sum('taxable_value') ?? 0;

            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }

            if ($transactionItems->count() >= 1) {
                foreach ($transactionItems as $purchaseItem) {
                    $shippingAmount = ($totalTaxableAmount != 0) ? ($purchaseItem->taxable_value * $this->shipping_freight) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $purchaseItem->gst_tax_percentage ?? 0,
                        'shipping_amount' => $shippingAmount,
                        'shipping_tax_amount' => ($shippingAmount * 18) / 100,
                    ];
                }
            }
        } else {
            $transactionLedgers = $this->purchaseTransactionLedger;
            $totalTaxableAmount = $this->purchaseTransactionLedger->sum('taxable_value') ?? 0;

            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }
            if ($transactionLedgers->count() >= 1) {
                foreach ($transactionLedgers as $purchaseLedger) {

                    $shippingAmount = ($totalTaxableAmount != 0) ? ($purchaseLedger->taxable_value * $this->shipping_freight) / $totalTaxableAmount : 0;

                    $prepareData[] = [
                        'gst_tax' => $purchaseLedger->gst_tax_percentage ?? 0,
                        'shipping_amount' => $shippingAmount,
                        'shipping_tax_amount' => ($shippingAmount * 18) / 100,
                    ];
                }
            }
        }

        $gstTaxArray = [];
        $totalShippingAmount = 0;
        $totalShippingTaxAmount = 0;
        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalShippingAmount += $checkGstTax['shipping_amount'];
                        $totalShippingTaxAmount += $checkGstTax['shipping_tax_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->supplier->name,
                    'ledger_id' => $this->supplier_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->voucher_number,
                    'invoice_number' => $this->sale_number,
                    'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->date_of_invoice)->format('d-m-Y'),
                    'transaction_type' => 'Purchase',
                    'ledger_name' => 'Shipping Charge',
                    'item_name' => 'Shipping Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '996812', // Fix HSN code for shipping and freight after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->purchaseTransactionItems->first()->items->model->hsn_sac_code) ? $this->purchaseTransactionItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $totalShippingAmount,
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) && $this->cgst != 0) ? ($totalShippingTaxAmount / 2) : '0',
                    'sgst' => (! empty($this->cgst) && $this->sgst != 0) ? ($totalShippingTaxAmount / 2) : '0',
                    'igst' => (! empty($this->igst) && $this->igst != 0) ? ($totalShippingTaxAmount) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'itc' => $this->purchaseTransactionItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->purchaseTransactionItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => $this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES,
                ];
                $totalShippingAmount = 0;
                $totalShippingTaxAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->supplier->name,
                    'ledger_id' => $this->supplier_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->voucher_number,
                    'invoice_number' => $this->sale_number,
                    'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->date_of_invoice)->format('d-m-Y'),
                    'transaction_type' => 'Purchase',
                    'ledger_name' => 'Shipping Charge',
                    'item_name' => 'Shipping Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '996812', // Fix HSN code for shipping and freight after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->purchaseTransactionItems->first()->items->model->hsn_sac_code) ? $this->purchaseTransactionItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $data['shipping_amount'],
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) || $this->cgst != 0) ? ($data['shipping_tax_amount'] / 2) : '0',
                    'sgst' => (! empty($this->cgst) || $this->sgst != 0) ? ($data['shipping_tax_amount'] / 2) : '0',
                    'igst' => (! empty($this->igst) || $this->igst != 0) ? ($data['shipping_tax_amount']) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'itc' => $this->purchaseTransactionItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->purchaseTransactionItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => $this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES,
                ];
            }
        }

        return $prepareNewData;
    }

    public function preparePackingAmount()
    {
        $prepareData = [];
        $prepareNewData = [];
        if ($this->packing_charge != 0 && empty($this->packing_charge)) {
            return $prepareNewData;
        }

        if ($this->purchase_item_type == self::ITEM_INVOICE) {
            $transactionItems = $this->purchaseTransactionItems;
            $totalTaxableAmount = $this->purchaseTransactionItems->sum('taxable_value') ?? 0;
            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }
            if ($transactionItems->count() >= 1) {
                foreach ($transactionItems as $purchaseItem) {
                    $PackingAmount = ($totalTaxableAmount != 0) ? ($purchaseItem->taxable_value * $this->packing_charge) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $purchaseItem->gst_tax_percentage ?? 0,
                        'packing_amount' => $PackingAmount,
                        'packing_tax_amount' => ($PackingAmount * 18) / 100,
                    ];
                }
            }
        } else {
            $transactionLedger = $this->purchaseTransactionLedger;
            $totalTaxableAmount = $this->purchaseTransactionLedger->sum('taxable_value') ?? 0;
            if ($totalTaxableAmount == 0) {
                return $prepareNewData;
            }
            if ($transactionLedger->count() >= 1) {
                foreach ($transactionLedger as $purchaseLedger) {
                    $PackingAmount = ($totalTaxableAmount != 0) ? ($purchaseLedger->taxable_value * $this->packing_charge) / $totalTaxableAmount : 0;
                    $prepareData[] = [
                        'gst_tax' => $purchaseLedger->gst_tax_percentage ?? 0,
                        'packing_amount' => $PackingAmount,
                        'packing_tax_amount' => ($PackingAmount * 18) / 100,
                    ];
                }
            }
        }

        $gstTaxArray = [];
        $totalPackingAmount = 0;
        $totalPackingTaxAmount = 0;
        foreach ($prepareData as $data) {
            if (in_array($data['gst_tax'], $gstTaxArray)) {
                foreach ($prepareData as $checkGstTax) {
                    if ($checkGstTax['gst_tax'] == $data['gst_tax']) {
                        $totalPackingAmount += $checkGstTax['packing_amount'];
                        $totalPackingTaxAmount += $checkGstTax['packing_tax_amount'];
                    }
                }
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->supplier->name,
                    'ledger_id' => $this->supplier_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->voucher_number,
                    'invoice_number' => $this->sale_number,
                    'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->date_of_invoice)->format('d-m-Y'),
                    'transaction_type' => 'Purchase',
                    'ledger_name' => 'Packing Charge',
                    'item_name' => 'Packing Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '998541', // Fix HSN code for packing charge after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->purchaseTransactionItems->first()->items->model->hsn_sac_code) ? $this->purchaseTransactionItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $totalPackingAmount,
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) && $this->cgst != 0) ? ($totalPackingTaxAmount / 2) : '0',
                    'sgst' => (! empty($this->cgst) && $this->sgst != 0) ? ($totalPackingTaxAmount / 2) : '0',
                    'igst' => (! empty($this->igst) && $this->igst != 0) ? ($totalPackingTaxAmount) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'itc' => $this->purchaseTransactionItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->purchaseTransactionItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => $this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES,
                ];
                $totalPackingAmount = 0;
                $totalPackingTaxAmount = 0;
            } else {
                $gstTaxArray[] = $data['gst_tax'];
                $prepareNewData[$data['gst_tax']] = [
                    'party_name' => $this->supplier->name,
                    'ledger_id' => $this->supplier_ledger_id,
                    'gstin' => $this->gstin ?? '',
                    'voucher_number' => $this->voucher_number,
                    'invoice_number' => $this->sale_number,
                    'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                    'invoice_date' => Carbon::parse($this->date_of_invoice)->format('d-m-Y'),
                    'transaction_type' => 'Purchase',
                    'ledger_name' => 'Packing Charge',
                    'item_name' => 'Packing Charge',
                    'quantity' => 0,
                    'unit_of_measurement' => '',
                    'rate_per_unit' => 0,
                    'hsn_code' => '998541', // Fix HSN code for packing charge after discussion with Gopal bhai change it here
                    // 'hsn_code' => ! empty($this->purchaseTransactionItems->first()->items->model->hsn_sac_code) ? $this->purchaseTransactionItems->first()->items->model->hsn_sac_code : '',
                    'taxable_value' => $data['packing_amount'],
                    'rate_of_gst' => 18,
                    'gst_rate' => $data['gst_tax'],
                    'cgst' => (! empty($this->cgst) || $this->cgst != 0) ? ($data['packing_tax_amount'] / 2) : '0',
                    'sgst' => (! empty($this->cgst) || $this->sgst != 0) ? ($data['packing_tax_amount'] / 2) : '0',
                    'igst' => (! empty($this->igst) || $this->igst != 0) ? ($data['packing_tax_amount']) : '0',
                    'cess_amount' => ! empty($this->cess_amount) ? $this->cess_amount : '0',
                    'itc' => $this->purchaseTransactionItems->first()->classification_is_itc_applicable ?? 0,
                    'rcm_yes_or_no' => $this->purchaseTransactionItems->first()->classification_is_rcm_applicable ?? 0,
                    'invoice_amount' => $this->grand_total,
                    'invoice_type' => PurchaseTransaction::PURCHASES,
                ];
            }
        }

        return $prepareNewData;
    }

    public function getTcsReport(): array
    {
        return [
            'transaction_id' => $this->id,
            'date' => $this->voucher_date->format('d-m-y'),
            'ledger_name' => $this->supplier->name,
            'transaction_type' => 'Purchase',
            'voucher_no' => $this->voucher_number,
            'invoice_no' => '',
            'debit_amount' => $this->tcs_amount,
            'credit_amount' => 0,
            'balance' => 0,
            'narration' => $this->narration ?? null,
        ];
    }

    public function getPrimaryPhone()
    {
        $phone = null;

        if (isset($this->supplier->model->phone_1)) {
            $phone = $this->supplier->model->region_code_1.$this->supplier->model->phone_1;
        } elseif (isset($this->party->model->phone_2)) {
            $phone = $this->supplier->model->region_code_2.$this->supplier->model->phone_2;
        }

        return $phone;
    }

    public function prepareAttributes()
    {

        $data = [
            'party_name' => $this->supplier->name,
            'invoice_number' => $this->voucher_number,
            'date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
            'grand_total' => $this->grand_total,
            'phone_no' => $this->getPrimaryPhone(),
            'payment_status' => $this->paymentStatus,
            'taxable_value' => $this->taxable_value,
            'cgst' => $this->cgst,
            'sgst' => $this->sgst,
            'igst' => $this->igst,
            'tcs_rate' => $this->tcs_rate,
            'tcs_amount' => $this->tcs_amount,
            'tcs_ledger' => $this->tcsLedger->name ?? '',
            'rounding_amount' => $this->rounding_amount,
            'total' => $this->total,
            'shipping_freight' => $this->shipping_freight,
            'shipping_freight_with_gst' => $this->shipping_freight_with_gst,
            'packing_charge' => $this->packing_charge,
            'packing_charge_with_gst' => $this->packing_charge_with_gst,
            'purchase_order_no' => $this->purchase_order_no,
        ];

        if ($this->purchase_item_type == self::ITEM_INVOICE) {
            $items = $this->purchaseTransactionItems;
            foreach ($items as $item) {
                $data['items'][] = [
                    'id' => $item->id,
                    'item_name' => $item->items->item_name,
                    'ledger_name' => $item->ledger->name,
                    'quantity' => $item->quantity,
                    'rpu_without_gst' => $item->rpu_without_gst,
                    'gst_tax_percentage' => $item->gst_tax_percentage,
                    'total_discount_amount' => $item->total_discount_amount,
                    'rpu_with_gst' => $item->rpu_with_gst,
                    'cgst' => $item->classification_cgst_tax ?? 0,
                    'sgst' => $item->classification_sgst_tax ?? 0,
                    'igst' => $item->classification_igst_tax ?? 0,
                    'total' => $item->total,
                ];
            }
        } else {
            $ledgers = $this->purchaseTransactionLedger;
            foreach ($ledgers as $ledger) {
                $data['ledgers'][] = [
                    'id' => $ledger->id,
                    'ledger_name' => $ledger->ledgers->name,
                    'rpu_with_gst' => $ledger->rpu_with_gst,
                    'rpu_without_gst' => $ledger->rpu_without_gst,
                    'total_discount_amount' => $ledger->total_discount_amount,
                    'gst_tax_percentage' => $ledger->gst_tax_percentage,
                    'cgst' => $ledger->classification_cgst_tax ?? 0,
                    'sgst' => $ledger->classification_sgst_tax ?? 0,
                    'igst' => $ledger->classification_igst_tax ?? 0,
                ];
            }
        }

        return $data;
    }

    public function prepareData()
    {
        return [
            'transaction_id' => $this->id,
            'party_name' => $this->supplier->name,
            'party_id' => $this->supplier_ledger_id,
            'phone_no' => $this->getPrimaryPhone(),
            'invoice_number' => $this->voucher_number ?? '',
            'invoice_amount' => $this->grand_total,
            'payment_status' => $this->payment_status ?? '',
            'transaction_type' => 'Purchase',
            'date' => Carbon::parse($this->voucher_date)->format('d-m-Y') ?? '',
        ];
    }

    public function scopeFinancialYearDate(Builder $query)
    {
        $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
        $query->whereBetween('voucher_date', [$getCurrentFinancialYearDetails['yearStartDate'], $getCurrentFinancialYearDetails['yearEndDate']]);
    }

    public function scopeSorting($query, $sorting)
    {
        $sorting = getSortingValues($sorting);
        foreach ($sorting as $key => $value) {
            $query->orderBy($key, $value);
        }

        return $query;
    }

    public function prepareAdditionalCharges()
    {
        $dataItem = [];
        $additionalCharges = $this->additionalCharges->where('gst_percentage', '!=', 0);
        foreach ($additionalCharges as $transaction) {
            $gstData = GstCalculateAction::run($this, $transaction);
            $dataItem[] = [
                'party_name' => $this->supplier->name,
                'gstin' => $this->gstin,
                'ledger_id' => $this->supplier_ledger_id,
                'ledger_name' => $transaction->ledger->name,
                'item_name' => $transaction->ledger->name,
                'invoice_number' => $this->sale_number,
                'voucher_number' => $this->voucher_number,
                'voucher_date' => Carbon::parse($this->voucher_date)->format('d-m-Y'),
                'invoice_date' => Carbon::parse($this->date_of_invoice)->format('d-m-Y'),
                'transaction_type' => 'Purchase',
                'quantity' => 1,
                'hsn_code' => $transaction->ledger->model->hsn_sac_code,
                'rate_per_unit' => $transaction->total_without_tax,
                'item_description' => '', //
                'unit_of_measurement' => '',
                'rate_of_gst' => $transaction->gst_percentage,
                'gst_rate' => $transaction->gst_percentage,
                'rcm_applicable_or_not' => '', //
                'taxable_value' => $transaction->total_without_tax,
                'cgst' => $gstData['cgst'],
                'sgst' => $gstData['sgst'],
                'igst' => $gstData['igst'],
                'itc' => $this->purchaseTransactionItems->first()->classification_is_itc_applicable ?? 0,
                'cess_amount' => $this->cess_amount,
                'total_amount' => $transaction->value + $gstData['cgst'] + $gstData['sgst'] + $gstData['igst'] + $this->cess_amount,
                'invoice_amount' => $this->grand_total,
                'is_locked' => ! empty($this->date) ? isLockTransaction(LockTransaction::EXPENSE, Carbon::parse($this->date)->format('Y-m-d')) : false,
                'rcm_yes_or_no' => $this->purchaseTransactionItems->first()->classification_is_rcm_applicable ?? 0,
            ];
        }

        return $dataItem;
    }

    public function scopeSortByVoucherNumber($query, $direction = 'asc')
    {
        $dir = strtoupper($direction) === 'DESC' ? 'DESC' : 'ASC';

        return $query->orderByRaw("CAST(REGEXP_REPLACE(voucher_number, '[^0-9]', '') AS UNSIGNED) $dir");
    }
}
