<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateFranchiseRequest;
use App\Models\Bank;
use App\Models\City;
use App\Models\Company;
use App\Models\EntityType;
use App\Models\Franchise;
use App\Models\Role;
use App\Models\State;
use App\Models\User;
use App\Repositories\FranchiseRepository;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Laracasts\Flash\Flash;

/**
 * Class FranchisesController
 */
class FranchisesController extends AppBaseController
{
    public FranchiseRepository $franchiseRepository;

    public function __construct(FranchiseRepository $franchiseRepository)
    {
        $this->franchiseRepository = $franchiseRepository;
    }

    /**
     * @return View
     */
    public function index(): \Illuminate\View\View
    {

        $franchisesCount = Franchise::count();

        return view('admin.franchises.index', compact('franchisesCount'));
    }

    /**
     * @return View
     */
    public function create(): \Illuminate\View\View
    {
        $countries = getCountries();

        return view('admin.franchises.create', compact('countries'));
    }

    public function store(CreateFranchiseRequest $request): JsonResponse
    {
        $input = $request->all();
        $franchise = $this->franchiseRepository->store($input);

        return $this->sendResponse($franchise->id, 'Franchise created successfully');
    }

    /**
     * @return View
     */
    public function edit(Franchise $franchise): \Illuminate\View\View
    {
        $franchise->load(['user', 'addresses', 'franchiseTax.entityType', 'contactPerson.media']);
        $countries = getCountries();
        $getEntityTypes = null;

        $states = [];
        $cities = [];
        if (! empty($franchise->addresses)) {
            $states = State::whereCountryId($franchise->addresses->country_id)->orderBy('name', 'ASC')
                ->toBase()->pluck('name', 'id')->toArray();
            $cities = City::whereStateId($franchise->addresses->state_id)->orderBy('name', 'ASC')
                ->toBase()->pluck('name', 'id')->toArray();
        }

        if (! empty($franchise->franchiseTax->entity_type) && $franchise->franchiseTax->entity_type) {
            $companyPanEntityCharacter = EntityType::whereId($franchise->franchiseTax->entity_type)->first()->character;
            $getEntityTypes = EntityType::whereCharacter($companyPanEntityCharacter)->get();
        }

        return view('admin.franchises.edit', compact('franchise', 'countries', 'getEntityTypes', 'states', 'cities'));
    }

    public function update(CreateFranchiseRequest $request, Franchise $franchise): JsonResponse
    {
        if ($request->get('part') === 'franchise-tax-details') {
            $this->franchiseRepository->franchiseTaxDetails($request->all(), $franchise->id);
        }
        if ($request->get('part') === 'franchise-basic-details') {

            $this->franchiseRepository->franchiseBasicDetail($request->all(), $franchise);
        }
        if ($request->get('part') === 'franchise-contact-person-details') {

            $this->franchiseRepository->contactPersonDetails($request->all(), $franchise->id);
        }

        return $this->sendSuccess('Franchise updated successfully');
    }

    public function destroy(Franchise $franchise): JsonResponse
    {
        $companyExists = Company::whereFranchiseId($franchise->id)->exists();
        if ($companyExists) {
            return $this->sendError("You can't delete this franchise cause of this franchise have company.");
        }
        $franchise->delete();
        User::whereId($franchise->user->id)->delete();

        return $this->sendSuccess('Franchise deleted successfully.');
    }

    public function defaultStatus(User $franchise): JsonResponse
    {
        $status = ! $franchise->status;
        $franchise->update(['status' => $status]);

        return $this->sendSuccess('Status updated successfully.');
    }

    public function addFranchiseBank(CreateFranchiseRequest $request, $franchiseId): JsonResponse
    {
        $companyBank = $this->franchiseRepository->createCompanyBank($request->all(), $franchiseId);

        return $this->sendResponse($companyBank, 'Bank created successfully');
    }

    public function editFranchiseBank($bankId): JsonResponse
    {
        $bank = Bank::whereId($bankId)->firstOrfail();

        return $this->sendResponse($bank, 'Bank retrieved successfully');
    }

    public function updateFranchiseBank(CreateFranchiseRequest $request, $bankId): JsonResponse
    {
        $input = $request->all();
        $bankDetails = [
            'bank_name' => $input['bank_name'],
            'bank_acc_number' => $input['bank_account_number'],
            'bank_acc_type' => $input['bank_account_type'],
            'bank_branch' => $input['bank_branch'],
            'ifsc_code' => $input['ifsc_code'],
        ];
        $bank = Bank::whereId($bankId)->whereModelType(Franchise::class)->firstOrfail();
        $bank->update($bankDetails);

        return $this->sendResponse($bank, 'Bank updated successfully');
    }

    public function deleteFranchiseBank($bankId): JsonResponse
    {
        $bank = Bank::whereId($bankId)->firstOrFail();
        $bank->delete();

        return $this->sendSuccess('Bank deleted successfully');
    }

    /**
     * @return RedirectResponse
     */
    public function impersonate(User $user): RedirectResponse
    {
        Auth::user()->impersonate($user);

        return redirect(route('franchise.dashboard'));
    }

    /**
     * @return RedirectResponse
     */
    public function userImpersonateLogout(): RedirectResponse
    {
        $user = Auth::user();

        if (empty($user)) {
            Auth::logout();

            return redirect(route('login'));
        }

        $user->leaveImpersonation();

        return redirect(url('admin/dashboard'));
    }

    public function setFranchiseForSAdmin(User $sadmin, Franchise $franchise): RedirectResponse
    {
        $user = getLoginUser();

        $query = Franchise::where('id', $franchise->id)->with(['user', 'addresses', 'franchiseTax', 'contactPerson', 'clients']);

        /* Add super admin related other roles */
        if ($user->hasRole(Role::HO_ADMIN)) {
            $franchise = $query->first();
        }

        if (! empty($franchise)) {
            Session::put('current_franchise', $franchise);

            return redirect(route('franchise.dashboard'));
        }

        Flash::error('You don\'t have to access this page.');

        return redirect()->back();
    }

    public function backToSAdmin(): RedirectResponse
    {
        Session::forget('current_franchise');
        Session::forget('current_ho_admin');

        /* Add super admin related other roles */
        if (getLoginUser()->hasRole([Role::HO_ADMIN])) {

            return redirect(route('admin.dashboard'));
        }

        return redirect(route('franchise.clients.index'));
    }
}
