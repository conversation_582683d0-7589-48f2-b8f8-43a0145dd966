<?php

namespace App\Http\Controllers;

use App\Actions\TransactionMaster\Expense\UpdateCreditNoteTransactionMaster;
use App\Actions\TransactionMaster\Expense\UpdateDebitNoteTransactionMaster;
use App\Actions\TransactionMaster\Expense\UpdatePurchaseOrderTransactionMaster;
use App\Actions\TransactionMaster\Expense\UpdatePurchaseReturnTransactionMaster;
use App\Actions\TransactionMaster\Expense\UpdatePurchaseTransactionMaster;
use App\Models\Master\ExpenseCreditNoteTransactionMaster;
use App\Models\Master\ExpenseDebitNoteTransactionMaster;
use App\Models\Master\ExpenseReturnTransactionMaster;
use App\Models\Master\ExpenseTransactionMaster;
use App\Models\Master\PurchaseOrderTransactionMaster;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Laracasts\Flash\Flash;

/**
 * Class ExpenseTransactionController
 */
class ExpenseTransactionController extends AppBaseController
{
    /**
     * Display a listing of the resource.
     *
     * @return View
     */
    public function index(): \Illuminate\View\View
    {
        $data['expense'] = ExpenseTransactionMaster::whereCompanyId(getCurrentCompany()->id)->first();
        $data['expenseReturn'] = ExpenseReturnTransactionMaster::whereCompanyId(getCurrentCompany()->id)->first();
        $data['expenseDebitNote'] = ExpenseDebitNoteTransactionMaster::whereCompanyId(getCurrentCompany()->id)->first();
        $data['expenseCreditNote'] = ExpenseCreditNoteTransactionMaster::whereCompanyId(getCurrentCompany()->id)->first();
        $data['purchaseOrder'] = PurchaseOrderTransactionMaster::whereCompanyId(getCurrentCompany()->id)->first();

        return view('company.transaction.expense.index')->with($data);
    }

    public function purchaseUpdate(Request $request)
    {
        $data = UpdatePurchaseTransactionMaster::run($request->all());

        Flash::success('Purchase Transaction updated successfully.');

        return redirect()->route('company.expense-transaction.index');
    }

    public function purchaseReturnUpdate(Request $request): RedirectResponse
    {
        $data = UpdatePurchaseReturnTransactionMaster::run($request->all());

        Flash::success('Purchase Return Transaction updated successfully.');

        return redirect()->route('company.expense-transaction.index');
    }

    public function purchaseDebitNoteUpdate(Request $request): RedirectResponse
    {
        $data = UpdateDebitNoteTransactionMaster::run($request->all());

        Flash::success('Debit Note Transaction updated successfully.');

        return redirect()->route('company.expense-transaction.index');
    }

    public function purchaseCreditNoteUpdate(Request $request): RedirectResponse
    {
        $data = UpdateCreditNoteTransactionMaster::run($request->all());

        Flash::success('Credit Note Transaction updated successfully.');

        return redirect()->route('company.expense-transaction.index');
    }

    public function purchaseOrderUpdate(Request $request)
    {
        UpdatePurchaseOrderTransactionMaster::run($request->all());

        Flash::success('Purchase Order Transaction updated successfully.');

        return redirect()->route('company.expense-transaction.index');
    }
}
