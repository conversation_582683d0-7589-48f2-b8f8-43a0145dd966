<?php

namespace App\Http\Controllers;

use App\Actions\CommonAction\GetGstInformationAction;
use App\Exports\Gst1ReportExport;
use App\Exports\Gstr3BReportExport;
use App\Exports\GstrDashboardReportExport;
use App\Exports\GstReportExport;
use App\Jobs\GetGstDashboardData;
use App\Models\Company;
use App\Models\GstDashboardData;
use App\Models\GstrLogin;
use App\Services\FileGstService;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Maatwebsite\Excel\Facades\Excel;

class GstDashboardController extends AppBaseController
{
    /** @var FileGstService */
    public mixed $fileGstService;

    public function __construct()
    {
        $this->fileGstService = new FileGstService();
    }

    public function index()
    {

        return view('company.gst-dashboard.index');
    }

    public function downloadFiledGstr1Report(Request $request)
    {
        $input = $request->all();
        $company = getCurrentCompany();
        $gstr1Data = GstDashboardData::whereId($input['id'])->with('saleGstR1Data')->first();
        $gstr1File = json_decode($gstr1Data->saleGstR1Data->sales_gstr_1);
        $gstInformation = GetGstInformationAction::run($company->companyTax->gstin);
        $gstInformation = json_decode($gstInformation);
        $gstrData = [];

        $month = substr($gstr1Data->month, 0, 2);
        $year = substr($gstr1Data->month, 2, 4);
        $year = Carbon::createFromDate((int) $year, (int) $month, 1);
        $monthYear = $year->format('My');
        if (isset($gstr1File->sec_sum)) {
            foreach ($gstr1File->sec_sum as $key => $value) {
                $gstrData[$value->sec_nm] = $value;
            }
        }

        $month = substr($gstr1File->ret_period, 0, 2); // "05"
        $year = substr($gstr1File->ret_period, 2, 4);  // "2024"

        if ($month >= '04') {
            $startYear = $year;
            $endYear = $year + 1;
        } else {
            $startYear = $year - 1;
            $endYear = $year;
        }
        $financialYear = $startYear.'-'.substr($endYear, -2);

        $dateObj = Carbon::createFromFormat('!m', $month);
        $monthName = $dateObj->format('F'); // Full month name (e.g., "May")
        $gstrData['year'] = $financialYear;
        $gstrData['month'] = $monthName;
        $fileName = 'GSTR1_'.$company->legal_name.'_'.$monthYear;
        if ($input['type'] == 'pdf') {

            $pdf = Pdf::loadView('pdf.final-gst-report', ['gstrData' => $gstrData, 'company' => $company, 'gstr1Data' => $gstr1Data, 'gstInformation' => $gstInformation])->setPaper('a4', 'landscape');

            return $pdf->download($fileName.'.pdf');
        }
        $response = Excel::download(new Gst1ReportExport($gstrData, $company, $gstr1Data, $gstInformation), $fileName.'.xlsx');
        ob_end_clean();

        return $response;

    }

    public function downloadFiledGstr3bReport(Request $request)
    {
        $input = $request->all();

        $gstr3BData = GstDashboardData::whereId($input['id'])->with('saleGstR3BData')->first();
        $gstr3bFile = json_decode($gstr3BData->saleGstR3BData->sales_gstr_3b);
        $gstrData = [];
        $company = getCurrentCompany();
        $gstInformation = GetGstInformationAction::run($company->companyTax->gstin);
        $gstInformation = json_decode($gstInformation);
        $month = substr($gstr3bFile->ret_period, 0, 2); // "05"
        $year = substr($gstr3bFile->ret_period, 2, 4);  // "2024"
        $monthYear = Carbon::createFromDate((int) $year, (int) $month, 1);
        $monthYear = $monthYear->format('My');
        if ($month >= '04') {
            $startYear = $year;
            $endYear = $year + 1;
        } else {
            $startYear = $year - 1;
            $endYear = $year;
        }
        $financialYear = $startYear.'-'.substr($endYear, -2);

        $dateObj = Carbon::createFromFormat('!m', $month);
        $monthName = $dateObj->format('F'); // Full month name (e.g., "May")
        $gstrData['year'] = $financialYear;
        $gstrData['month'] = $monthName;
        $gstrData['gstr3bFile'] = $gstr3bFile;
        $fileName = 'GSTR3B_'.$company->legal_name.'_'.$monthYear;
        if ($input['type'] == 'pdf') {

            $pdf = Pdf::loadView('pdf.final-gstr3b-report', ['gstrData' => $gstrData, 'company' => $company, 'gstr3BData' => $gstr3BData, 'gstInformation' => $gstInformation])->setPaper('a4', 'landscape');

            return $pdf->download($fileName.'.pdf');
        }
        $response = Excel::download(new Gstr3BReportExport($gstrData, $company, $gstr3BData, $gstInformation), $fileName.'.xlsx');
        ob_end_clean();

        return $response;
    }

    public function export(Request $request)
    {
        $itemDetailsReportCacheKey = generateCacheKey('gst_dashboard');
        $data = Cache::get($itemDetailsReportCacheKey);
        $input = $request->all();
        $company = getCurrentCompany();
        $data['company'] = $company;
        $data['companyAddress'] = $company?->addresses->where('address_type', Company::BILLING_ADDRESS)->first();

        if ($input['type'] == 'pdf') {
            $pdf = Pdf::loadView('pdf.gst-report', $data)->setPaper('a4', 'landscape');
        } else {
            $pdf = Excel::download(new GstReportExport($data), 'gst-report.xlsx');
            ob_end_clean();

            return $pdf;
        }

        return $pdf->stream('gst-report.pdf');
    }

    public function exportDetail(Request $request)
    {
        $input = $request->all();
        $booksVsGstr1DashboardCacheKey = generateCacheKey('books_vs_gstr1_view_details');
        $booksVsGstr3bDashboardCacheKey = generateCacheKey('books_vs_gstr3b_view_details');
        $gstr1vsGstr3bDashboardCacheKey = generateCacheKey('gstr1_vs_gstr3b_view_details');
        $itcBooksVsGstr2aDashboardCacheKey = generateCacheKey('itc_books_vs_gstr2a_view_details');
        $itcBooksVsGstr2bDashboardCacheKey = generateCacheKey('itc_books_vs_gstr2b_view_details');
        $itcBooksVsGstr3bDashboardCacheKey = generateCacheKey('itc_books_vs_gstr3b_view_details');
        $itcGstr2bVsGstr2aDashboardCacheKey = generateCacheKey('itc_gstr2b_vs_gstr2a_view_details');
        $itcGstr3bVsGstr2aDashboardCacheKey = generateCacheKey('itc_gstr3b_vs_gstr2a_view_details');
        $itcGstr3bVsGstr2bDashboardCacheKey = generateCacheKey('itc_gstr3b_vs_gstr2b_view_details');

        $data['booksVsGstr1Dashboard'] = Cache::get($booksVsGstr1DashboardCacheKey);
        $data['booksVsGstr3bDashboard'] = Cache::get($booksVsGstr3bDashboardCacheKey);
        $data['gstr1vsGstr3bDashboard'] = Cache::get($gstr1vsGstr3bDashboardCacheKey);
        $data['itcBooksVsGstr2aDashboard'] = Cache::get($itcBooksVsGstr2aDashboardCacheKey);
        $data['itcBooksVsGstr2bDashboard'] = Cache::get($itcBooksVsGstr2bDashboardCacheKey);
        $data['itcBooksVsGstr3bDashboard'] = Cache::get($itcBooksVsGstr3bDashboardCacheKey);
        $data['itcGstr2bVsGstr2aDashboard'] = Cache::get($itcGstr2bVsGstr2aDashboardCacheKey);
        $data['itcGstr3bVsGstr2aDashboard'] = Cache::get($itcGstr3bVsGstr2aDashboardCacheKey);
        $data['itcGstr3bVsGstr2bDashboard'] = Cache::get($itcGstr3bVsGstr2bDashboardCacheKey);
        $company = getCurrentCompany();
        $data['company'] = $company;
        $data['companyAddress'] = $company?->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        if ($input['type'] == 'pdf') {
            $pdf = Pdf::loadView('pdf.gst-report-dashboard-details', $data)->setPaper('a4', 'landscape');

            return $pdf->download('gst-report.pdf');
        } else {
            $pdf = Excel::download(new GstrDashboardReportExport($data), 'gst-report.xlsx');
            ob_end_clean();

            return $pdf;
        }

    }

    public function getGstData()
    {
        $company = getCurrentCompany();
        $gstin = $company->companyTax->gstin;

        $dashboardData = [
            'company_id' => $company->id,
            'gstin' => $gstin,
        ];

        $gstrLogin = GstrLogin::whereCompanyId($company->id)->first();
        $filingPeriod = Carbon::now()->endOfMonth()->format('mY');
        $statecd = substr($gstin, 0, 2);
        $headers = [
            'username' => $gstrLogin->username ?? null,
            'otp' => $gstrLogin->otp ?? '000000',
            'gstin' => $gstin,
            'action' => 'B2B',
            'statecd' => $statecd,
            'retperiod' => $filingPeriod,
        ];

        $fileGst = $this->fileGstService;
        $data['api'] = $fileGst->getGstR1Data($headers);

        if (isset($data['api']['errorCode']) && $data['api']['errorCode'] == 'RETOTPREQUEST') {
            $massage = 'OTP sent to your registered mobile/email with GST Portal';

            return $this->sendError($massage);
        }

        // $gstrLogin->update([
        //     'synced' => true,
        // ]);

        GetGstDashboardData::dispatch($dashboardData);

        return $this->sendSuccess('GSTR1 data syncing started successfully. Please check the dashboard after some time.');
    }
}
