<?php

namespace App\Http\Controllers;

use App\Actions\DownloadPdf\DownloadBulkPdf;
use App\Actions\DownloadPdf\DownloadPdfStatus;
use App\Actions\Expense\DebitNote\CheckExistDebitNoteTransactionData;
use App\Actions\Expense\DebitNote\DeleteDebitNoteTransaction;
use App\Actions\Expense\DebitNote\ExpenseDebitNoteStoreTransaction;
use App\Actions\Expense\DebitNote\ExpenseDebitNoteUpdateTransaction;
use App\Actions\Expense\DebitNote\GetExpenseDebitNoteInvoiceNumber;
use App\Actions\Expense\DebitNote\GetInvoicePDFDataForExpenseDebitNote;
use App\Actions\Income\GstCalculationForPdf;
use App\Exports\ExpenseDebitNoteTransactionExport;
use App\Http\Requests\ExpenseDebitNoteTransactionRequest;
use App\Imports\ExpenseDrAccountingInvoiceImport;
use App\Imports\ExpenseDrItemInvoiceImport;
use App\Jobs\BulkPdfExportJob;
use App\Jobs\DownloadInvoiceJob;
use App\Jobs\InvoiceMailJob;
use App\Models\Company;
use App\Models\CompanyGroup;
use App\Models\CompanySetting;
use App\Models\Configuration\ExpenseDebitNote;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\Ledger;
use App\Models\Location;
use App\Models\Master\Broker;
use App\Models\Master\Customer;
use App\Models\Master\ExpenseDebitNoteTransactionMaster;
use App\Models\Master\Supplier;
use App\Models\Notification;
use App\Models\NotificationTemplate;
use App\Models\PdfDownloadingStatus;
use App\Models\PurchaseTransaction;
use App\Models\ReceiptTransaction;
use App\Models\SaleTransaction;
use App\Repositories\ExpenseDebitNoteTransactionRepository;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Laracasts\Flash\Flash;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use ZipArchive;

/**
 * Class ExpenseDebitNoteTransactionController
 */
class ExpenseDebitNoteTransactionController extends AppBaseController
{
    public ExpenseDebitNoteTransactionRepository $expenseDebitNoteTransactionRepository;

    public function __construct(ExpenseDebitNoteTransactionRepository $expenseDebitNoteTransactionRepository)
    {
        $this->expenseDebitNoteTransactionRepository = $expenseDebitNoteTransactionRepository;
    }

    /**
     * @return View
     */
    public function index(): \Illuminate\View\View
    {
        return view('company.expense-debit-note.index');
    }

    /**
     * @return View
     */
    public function create(): \Illuminate\View\View
    {
        $data = [];
        $getCurrentCompany = getCurrentCompany();
        $ledgerCustomer = CompanyGroup::whereCompanyId($getCurrentCompany?->id);
        $data['groupLists'] = getGroupLists();
        $data['groupListsId'] = $ledgerCustomer->where('name', Ledger::SUPPLIER)->first()->id;
        $data['countries'] = getCountries();
        $data['indiaStates'] = getStates(Company::INDIA_COUNTRY_ID);
        $data['updateLedger'] = false;
        $TdsGroups = getParentGroupsValue(Ledger::TAXES_TDS);
        $groupIds = array_merge(array_keys($TdsGroups));
        $data['ledgerOfTds'] = Ledger::whereIn('group_id', $groupIds)->pluck('name', 'id')->toArray();
        $data['locationOfAssets'] = Location::pluck('name', 'id')->toArray();
        $data['brokerMasters'] = Broker::whereCompanyId($getCurrentCompany?->id)->pluck('broker_name', 'id');
        $data['debitNoteConfiguration'] = ExpenseDebitNote::first();
        $data['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $data['defaultGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)->whereName('Default Group')
            ->whereCompanyId(getCurrentCompany()->id)
            ->value('id');
        $data['unitOfMeasurement'] = getItemMasterUnit();
        $data['expenseDebitNoteTransaction'] = ExpenseDebitNoteTransactionMaster::whereCompanyId($getCurrentCompany?->id)->first();
        $data['purchaseInvoiceNumbers'] = PurchaseTransaction::pluck('voucher_number', 'id')->toArray();
        $data['lastVoucherNumber'] = GetExpenseDebitNoteInvoiceNumber::run();
        $data['methodVoucherType'] = $data['expenseDebitNoteTransaction']->voucher_number ?? ExpenseDebitNoteTransactionMaster::AUTOMATIC;
        $data['paymentMode'] = $data['expenseDebitNoteTransaction']->payment_mode ?? ExpenseDebitNoteTransaction::CREDIT_MODE;
        $data['previousBillId'] = ExpenseDebitNoteTransaction::orderBy('created_at', 'DESC')->first()?->id;

        $lastTransactionId = request()->query('lastTransactionId');
        if (! empty($lastTransactionId)) {
            $data['lastTransaction'] = ExpenseDebitNoteTransaction::where('id', $lastTransactionId)->first();
        }

        return view('company.expense-debit-note.create')->with($data);
    }

    public function checkUniqueVoucherNumber($voucherNumber, $configuration)
    {
        /* Whenever the code is used, add a fiscal year date condition to the query. */
        /* if (is_numeric($voucherNumber)) {
            $data['lastVoucherNumber'] = sprintf('%0'.strlen($voucherNumber).'d', ++$voucherNumber);
        } else {
            $data['lastVoucherNumber'] = $voucherNumber !== null ? ++$voucherNumber : 1;
        }
        $expenseDebitNoteTransaction = ExpenseDebitNoteTransaction::whereVoucherNumber($data['lastVoucherNumber'])->first();
        if (! empty($expenseDebitNoteTransaction)) {
            return $this->checkUniqueVoucherNumber($expenseDebitNoteTransaction->voucher_number, $configuration);
        } */

        $data['lastVoucherNumber'] = genNextInvNo($voucherNumber);

        return $data['lastVoucherNumber'] ?? 1;
    }

    public function store(ExpenseDebitNoteTransactionRequest $request): JsonResponse
    {
        $input = $request->all();

        if ($input['is_cgst_sgst_igst_calculated'] && $input['is_gst_na'] && getCurrentCompany()->is_gst_applicable) {
            Log::error([
                'is_cgst_sgst_igst_calculated and is_gst_na both are' => true,
                'companyId' => getCurrentCompany()->id,
                'input' => $input,
            ]);
        }

        $data = ExpenseDebitNoteStoreTransaction::run($input);

        if ($input['submit_button_value'] == ExpenseDebitNoteTransaction::SAVE_AND_NEW_BUTTON) {
            $data['lastExpenseDrTransactionDate'] = $input['voucher_date'];
            $data['lastTransactionId'] = $data['debitNote']['id'];
            $data['actionName'] = ExpenseDebitNoteTransaction::SAVE_AND_NEW_BUTTON;
        } elseif ($input['submit_button_value'] == ExpenseDebitNoteTransaction::SAVE_AND_PRINT_BUTTON) {
            $data['actionName'] = ExpenseDebitNoteTransaction::SAVE_AND_PRINT_BUTTON;
            $data['expense_debit_note_id'] = $data['debitNote']['id'];
            // $pdf = $this->getExpenseDNPdfPreview($data['debitNote']['id']);
            // $content = json_decode($pdf->getContent(), true);
            // $data['pdf_status'] = $content['data']['status'];
            // $data['pdf_view_route'] = $data['pdf_status'] == 'completed' ? $content['data']['viewRoute'] : null;
        } else {
            $data['actionName'] = ExpenseDebitNoteTransaction::SAVE_BUTTON;
        }

        return $this->sendResponse($data, 'Debit Note transaction created successfully');
    }

    public function show(ExpenseDebitNoteTransaction $expenseDebitNote): JsonResponse
    {
        $data = $expenseDebitNote->load('debitNoteItems', 'supplier', 'tdsLedger');
        $expenseDebitTransactions = ReceiptTransaction::whereHas(
            'receiptTransactionItem',
            function ($q) use ($expenseDebitNote) {
                $q->where('expense_debit_id', $expenseDebitNote->id)->where('received_amount', '>', 0);
            }
        )->get();
        $transactionsData['receiptTransactions'] = [];
        $transactionsData['purchaseTransaction'] = [];
        $expenseDebitTransactionsCount = $expenseDebitTransactions->count();
        if ($expenseDebitTransactionsCount) {
            $transactionsData['receiptTransactions'] = $expenseDebitTransactions;
        }
        $purchaseTransaction = $expenseDebitNote->purchase;
        $purchaseTransactionCount = false;
        if (! empty($purchaseTransaction)) {
            $purchaseTransactionCount = true;
            $transactionsData['purchaseTransaction'] = $purchaseTransaction;
        }
        $data['checkTransactionExists'] = $expenseDebitTransactionsCount || $purchaseTransactionCount;
        $data['html'] = view('company.sale.view_sale_associated_transaction_list')->with($transactionsData)->render();

        return $this->sendResponse($data, 'Debit Note transaction retrieved successfully');
    }

    /**
     * @return Application|Factory|View
     */
    public function edit(ExpenseDebitNoteTransaction $expenseDebitNote)
    {
        $lockDate = getTransactionsLockDate()['expense'] ?? null;
        $isLocked = ! empty($lockDate) && Carbon::parse($lockDate)->greaterThanOrEqualTo($expenseDebitNote->voucher_date);
        if ($isLocked) {
            Flash::error('These Transactions Is locked');

            return redirect()->back();
        }
        $data = [];
        $data['debitNote'] = $expenseDebitNote->load([
            'debitNoteItems.items',
            'debitNoteItems.gst',
            'debitNoteLedgers.gst',
            'addresses',
            'supplier.model',
            'tcsLedger.model',
        ]);
        $getCurrentCompany = getCurrentCompany();
        $ledgerSupplier = CompanyGroup::whereCompanyId($getCurrentCompany?->id);
        $data['groupLists'] = getGroupLists();
        $data['groupListsId'] = $ledgerSupplier->where('name', Ledger::SUPPLIER)->first()->id;
        $data['countries'] = getCountries();
        $data['indiaStates'] = getStates(Company::INDIA_COUNTRY_ID);
        $data['updateLedger'] = false;
        $ledger = Ledger::whereId($expenseDebitNote->supplier_id)->whereIn('model_type', [Supplier::class, Customer::class])->first();
        $supplier = Supplier::whereId($ledger->model_id)->first();
        $data['isTdsApplicable'] = $supplier?->is_tds_applicable;
        $TdsGroups = getParentGroupsValue(Ledger::TAXES_TDS);
        $groupIds = array_merge(array_keys($TdsGroups));
        $data['ledgerOfTds'] = Ledger::whereIn('group_id', $groupIds)->pluck('name', 'id')->toArray();
        $data['locationOfAssets'] = Location::pluck('name', 'id')->toArray();
        $data['brokerMasters'] = Broker::whereCompanyId($getCurrentCompany?->id)->pluck('broker_name', 'id');
        $data['purchaseNumbers'] = getSupplierPurchaseInvoiceNumber($expenseDebitNote->supplier_id);
        $data['debitNoteConfiguration'] = ExpenseDebitNote::first();
        $data['expenseDebitNoteTransaction'] = ExpenseDebitNoteTransactionMaster::whereCompanyId($getCurrentCompany?->id)->first();
        $data['debitNoteConfiguration'] = ExpenseDebitNote::first();
        $data['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $data['defaultGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)->whereName('Default Group')
            ->whereCompanyId(getCurrentCompany()->id)
            ->value('id');
        $data['unitOfMeasurement'] = getItemMasterUnit();
        $data['billingAddress'] = $data['debitNote']->addresses->firstWhere(
            'address_type',
            ExpenseDebitNoteTransaction::BILLING_ADDRESS
        );
        $data['billingStates'] = getStates($data['billingAddress']->country_id);
        $data['billingCities'] = getCities($data['billingAddress']->state_id);
        $data['shippingAddress'] = $data['debitNote']->addresses->firstWhere(
            'address_type',
            ExpenseDebitNoteTransaction::SHIPPING_ADDRESS
        );
        if ($data['shippingAddress']) {
            $data['shippingStates'] = getStates($data['shippingAddress']->country_id);
            $data['shippingCities'] = getCities($data['shippingAddress']->state_id);
        } else {
            $data['shippingStates'] = null;
            $data['shippingCities'] = null;
        }
        $data['previousBillId'] = ExpenseDebitNoteTransaction::where('id', '<', $expenseDebitNote->id)->orderByDesc('id')->value('id');
        $data['nextBillId'] = ExpenseDebitNoteTransaction::where('id', '>', $expenseDebitNote->id)->orderBy('id')->value('id');

        return view('company.expense-debit-note.edit')->with($data);
    }

    public function update(ExpenseDebitNoteTransactionRequest $request, ExpenseDebitNoteTransaction $expenseDebitNote): JsonResponse
    {
        $input = $request->all();

        if ($input['is_cgst_sgst_igst_calculated'] && $input['is_gst_na'] && getCurrentCompany()->is_gst_applicable) {
            Log::error([
                'is_cgst_sgst_igst_calculated and is_gst_na both are' => true,
                'transactionId' => $expenseDebitNote->id,
                'companyId' => $expenseDebitNote->company_id,
                'input' => $input,
                'old_input' => $expenseDebitNote,
            ]);
        }

        $data = ExpenseDebitNoteUpdateTransaction::run($input, $expenseDebitNote);

        if ($input['submit_button_value'] == ExpenseDebitNoteTransaction::SAVE_AND_PRINT_BUTTON) {
            $data['actionName'] = ExpenseDebitNoteTransaction::SAVE_AND_PRINT_BUTTON;
            $data['expense_debit_note_id'] = $expenseDebitNote->id;
            // $pdf = $this->getExpenseDNPdfPreview($expenseDebitNote->id);
            // $content = json_decode($pdf->getContent(), true);
            // $data['pdf_status'] = $content['data']['status'];
            // $data['pdf_view_route'] = $data['pdf_status'] == 'completed' ? $content['data']['viewRoute'] : null;
        }

        return $this->sendResponse($data, 'Debit Note updated successfully');
    }

    public function destroy(ExpenseDebitNoteTransaction $expenseDebitNote): JsonResponse
    {
        $data = DeleteDebitNoteTransaction::run($expenseDebitNote);

        return $this->sendSuccess('Debit Note deleted successfully');
    }

    public function getSaleTransactionItems($purchaseTransactionId): JsonResponse
    {
        $purchaseTransaction = PurchaseTransaction::whereId($purchaseTransactionId)->with(
            'purchaseTransactionItems',
            'purchaseTransactionLedger'
        )
            ->firstOrFail();

        $isCompanyGstApplicable = isCompanyGstApplicable();
        $expenseDebitNoteConfiguration = ExpenseDebitNote::toBase()->first();
        $html = view(
            'company.expense-debit-note.append.purchase-items-fields',
            compact('expenseDebitNoteConfiguration', 'isCompanyGstApplicable', 'purchaseTransaction')
        )->render();

        return $this->sendResponse($html, 'Purchase Item Type retrieved successfully');
    }

    public function getExpenseDebitNotePdfPreview($expenseDnPreviewId): JsonResponse
    {
        enableDeletedScope();
        $data = GetInvoicePDFDataForExpenseDebitNote::run($expenseDnPreviewId);
        $data = GstCalculationForPdf::run($data, ExpenseDebitNoteTransaction::class);
        $data['preview_enabled'] = true;
        $data['isA5Pdf'] = false;
        if (getCompanyExpensePDFFormat() == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
            $data['isA5Pdf'] = true;
        }

        $html = view('company.pdf.purchase_pdf.debit_note', $data)->render();
        $fileName = 'Expense_debit_note_'.$data['transaction']->voucher_number.'_'.$data['transaction']['supplier']['name'];
        $data = [];
        // $data['emailRoute'] = route('company.expense-debit-notes-email', ['expense_debit_note' => $expenseDnPreviewId]);
        $data['downloadRoute'] = route('company.expense-dn-preview-pdf-download', ['expenseDnDownloadPdfId' => $expenseDnPreviewId]);
        if (getCompanyExpensePDFFormat() == CompanySetting::PDF_FORMAT[CompanySetting::A5]) {
            $data['isA5Pdf'] = true;
        }
        disableDeletedScope();

        return $this->sendResponse(['html' => $html, 'fileName' => $fileName, 'data' => $data], 'Expense debit note pdf generated successfully');
    }

    public function getExpenseDNPdfPreview($expenseDnPreviewId): JsonResponse
    {
        $pdfStatus = DownloadPdfStatus::run(PdfDownloadingStatus::EXPENSE_DEBIT_NOTE);
        $currentCompany = getCurrentCompany();
        $data = [];

        DownloadInvoiceJob::dispatch($expenseDnPreviewId, $pdfStatus, $currentCompany, ExpenseDebitNoteTransaction::class);

        while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
            $pdfStatus->refresh();
            usleep(50000);
        }

        if ($pdfStatus->status == PdfDownloadingStatus::COMPLETED) {
            $data['status'] = 'completed';
            $data['downloadRoute'] = route('company.expense-dn-pdf-download', [
                'expenseDnDownloadPdfId' => $pdfStatus->id,
                'isView' => 0,
            ]);
            $data['viewRoute'] = $pdfStatus->link;
            $data['emailRoute'] = route(
                'company.expense-debit-notes-email',
                ['expense_debit_note' => $expenseDnPreviewId]
            );
            $data['html'] = view('company.pdf.purchase_pdf.preview')
                ->with($data)
                ->render();
        }

        if ($pdfStatus->status == PdfDownloadingStatus::FAILED) {
            $data['status'] = 'failed';
            $data['message'] = 'Something went wrong. Please try again later.';
        }

        return $this->sendResponse($data, 'Expense Debit Note PDF Preview screen retrieved successfully');
    }

    public function getExpenseDebitNotePdfDownload($expenseDnDownloadId, $isView = false)
    {

        if ($isView) {
            $transaction = ExpenseDebitNoteTransaction::whereId($expenseDnDownloadId)->withTrashed()->firstOrFail();
            $currentCompany = Company::with('billingAddress', 'companyTax', 'user', 'mailConfiguration')->findOrFail($transaction->company_id);
            session(['current_company' => $currentCompany]);
            $pdfStatus = DownloadPdfStatus::run(PdfDownloadingStatus::EXPENSE_DEBIT_NOTE);

            if (dockerEnabled()) {
                $job = new DownloadInvoiceJob($expenseDnDownloadId, $pdfStatus, $currentCompany, ExpenseDebitNoteTransaction::class);
                $job->dispatchSync($expenseDnDownloadId, $pdfStatus, $currentCompany, ExpenseDebitNoteTransaction::class);
                $fileName = $job->getFileName();

                $pdfStatus->refresh();

                if ($pdfStatus->status == PdfDownloadingStatus::COMPLETED && $pdfStatus->link == null) {
                    return response($job->getPdfContents(), 200, [
                        'Content-Type' => 'application/pdf',
                        'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
                    ]);
                }
            } else {
                DownloadInvoiceJob::dispatch($expenseDnDownloadId, $pdfStatus, $currentCompany, ExpenseDebitNoteTransaction::class);
            }
            $pdfStatus->refresh();

            while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
                $currentTime = Carbon::now();
                while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
                    $pdfStatus->refresh();
                    usleep(50000);
                    $runningTime = Carbon::now();
                    if ($runningTime->diffInSeconds($currentTime) > 60) {
                        $pdfStatus->update([
                            'status' => PdfDownloadingStatus::FAILED,
                        ]);
                        Log::error('Pdf download failed after 60 seconds '.$pdfStatus->id);
                    }
                }
            }

            if ($pdfStatus->status == PdfDownloadingStatus::FAILED) {
                $data['status'] = 'failed';
                $data['message'] = 'Something went wrong. Please try again later.';

                Flash::error('Something went wrong. Please try again later.');

                return redirect()->back()->with('error', $data['message']);
            }

            $client = new Client();
            $response = $client->get($pdfStatus->link, ['stream' => true]);
            $fileContent = $response->getBody()->getContents();

            $headers = [
                'Content-Type' => $response->getHeaderLine('Content-Type'),
                'Content-Disposition' => 'attachment; filename="'.$pdfStatus->name.'"',
            ];

            return response($fileContent, 200, $headers);
        }

        $data = PdfDownloadingStatus::find($expenseDnDownloadId);
        $client = new Client();
        $response = $client->get($data->link, ['stream' => true]);
        $fileContent = $response->getBody()->getContents();
        $headers = [
            'Content-Type' => $response->getHeaderLine('Content-Type'),
            'Content-Disposition' => 'attachment; filename="'.$data->name.'"',
        ];

        return response($fileContent, 200, $headers);
    }

    public function getExpenseDebitNotePreviewPdfDownload($expenseDnDownloadPdfId)
    {
        enableDeletedScope();
        $transaction = ExpenseDebitNoteTransaction::whereId($expenseDnDownloadPdfId)->firstOrFail();
        $currentCompany = Company::with('billingAddress', 'companyTax', 'user', 'mailConfiguration')->findOrFail($transaction->company_id);
        session(['current_company' => $currentCompany]);
        $pdfStatus = DownloadPdfStatus::run(PdfDownloadingStatus::EXPENSE_DEBIT_NOTE);

        if (dockerEnabled()) {
            $job = new DownloadInvoiceJob($expenseDnDownloadPdfId, $pdfStatus, $currentCompany, ExpenseDebitNoteTransaction::class);
            $job->dispatchSync($expenseDnDownloadPdfId, $pdfStatus, $currentCompany, ExpenseDebitNoteTransaction::class);
            $fileName = $job->getFileName();

            $pdfStatus->refresh();

            if ($pdfStatus->status == PdfDownloadingStatus::COMPLETED && $pdfStatus->link == null) {
                return response($job->getPdfContents(), 200, [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
                ]);
            }
        } else {
            DownloadInvoiceJob::dispatch($expenseDnDownloadPdfId, $pdfStatus, $currentCompany, ExpenseDebitNoteTransaction::class);
        }
        $pdfStatus->refresh();

        while ($pdfStatus->status != PdfDownloadingStatus::COMPLETED && $pdfStatus->status != PdfDownloadingStatus::FAILED) {
            $pdfStatus->refresh();
            usleep(50000);
        }

        $client = new Client();
        $response = $client->get($pdfStatus->link, ['stream' => true]);
        $fileContent = $response->getBody()->getContents();

        $headers = [
            'Content-Type' => $response->getHeaderLine('Content-Type'),
            'Content-Disposition' => 'attachment; filename="'.$pdfStatus->name.'"',
        ];
        disableDeletedScope();

        return response($fileContent, 200, $headers);
    }

    /**
     * @return View
     */
    public function expenseDebitNoteCreateDuplicate(ExpenseDebitNoteTransaction $expenseDebitNote): \Illuminate\View\View
    {
        $data = [];
        $data['debitNote'] = $expenseDebitNote->load([
            'debitNoteItems.items',
            'debitNoteItems.gst',
            'debitNoteLedgers.gst',
            'addresses',
            'supplier.model',
            'tcsLedger.model',
        ]);
        $getCurrentCompany = getCurrentCompany();
        $ledgerSupplier = CompanyGroup::whereCompanyId($getCurrentCompany?->id);
        $data['groupLists'] = getGroupLists();
        $data['groupListsId'] = $ledgerSupplier->where('name', Ledger::SUPPLIER)->first()->id;
        $data['countries'] = getCountries();
        $data['indiaStates'] = getStates(Company::INDIA_COUNTRY_ID);
        $data['updateLedger'] = false;
        $data['locationOfAssets'] = Location::pluck('name', 'id')->toArray();
        $data['brokerMasters'] = Broker::whereCompanyId($getCurrentCompany?->id)->pluck('broker_name', 'id');
        $data['purchaseNumbers'] = getSupplierPurchaseInvoiceNumber($expenseDebitNote->supplier_id);
        $data['debitNoteConfiguration'] = ExpenseDebitNote::first();
        $data['expenseDebitNoteTransaction'] = ExpenseDebitNoteTransactionMaster::whereCompanyId($getCurrentCompany?->id)->first();
        $data['debitNoteConfiguration'] = ExpenseDebitNote::first();
        $TdsGroups = getParentGroupsValue(Ledger::TAXES_TDS);
        $groupIds = array_merge(array_keys($TdsGroups));
        $data['ledgerOfTds'] = Ledger::whereIn('group_id', $groupIds)->pluck('name', 'id')->toArray();
        $voucherNumber = ExpenseDebitNoteTransaction::orderBy('created_at', 'desc')->first();
        $data['lastVoucherNumber'] = GetExpenseDebitNoteInvoiceNumber::run();
        $data['methodVoucherType'] = $data['expenseDebitNoteTransaction']->voucher_number ?? ExpenseDebitNoteTransactionMaster::AUTOMATIC;
        $data['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $data['defaultGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)->whereName('Default Group')
            ->whereCompanyId(getCurrentCompany()->id)
            ->value('id');
        $data['unitOfMeasurement'] = getItemMasterUnit();
        $data['billingAddress'] = $data['debitNote']->addresses->firstWhere(
            'address_type',
            ExpenseDebitNoteTransaction::BILLING_ADDRESS
        );
        $data['billingStates'] = getStates($data['billingAddress']->country_id);
        $data['billingCities'] = getCities($data['billingAddress']->state_id);
        $data['shippingAddress'] = $data['debitNote']->addresses->firstWhere(
            'address_type',
            ExpenseDebitNoteTransaction::SHIPPING_ADDRESS
        );
        if ($data['shippingAddress']) {
            $data['shippingStates'] = getStates($data['shippingAddress']->country_id);
            $data['shippingCities'] = getCities($data['shippingAddress']->state_id);
        } else {
            $data['shippingStates'] = null;
            $data['shippingCities'] = null;
        }
        $data['previousBillId'] = ExpenseDebitNoteTransaction::where('id', '<', $expenseDebitNote->id)->orderByDesc('id')->value('id');
        $data['nextBillId'] = ExpenseDebitNoteTransaction::where('id', '>', $expenseDebitNote->id)->orderBy('id')->value('id');

        return view('company.expense-debit-note.duplicate')->with($data);
    }

    /**
     * @return View
     */
    public function expenseDebitNoteReturnEmail($expenseDebitNote): \Illuminate\View\View
    {
        enableDeletedScope();
        $expenseDebitNote = ExpenseDebitNoteTransaction::findOrFail($expenseDebitNote);
        $data['debitNote'] = $expenseDebitNote->load([
            'debitNoteItems',
            'debitNoteLedgers',
            'addresses',
            'supplier'
        ]);
        disableDeletedScope();

        return view('company.expense-debit-note.send-email')->with($data);
    }

    /**
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    public function sendEmail(Request $request, $expenseDebitNote): RedirectResponse
    {
        enableDeletedScope();
        $expenseDebitNote = ExpenseDebitNoteTransaction::findOrFail($expenseDebitNote);
        $data = $this->getInvoicePDFData($expenseDebitNote->id);
        $input = $request->all();
        //   $data['mailSetting'] = $data['currentCompany']->mailConfiguration;
        //   if (empty($data['mailSetting'])) {

        //       Flash::error('Please enter mail credentials first.');

        //       return redirect()->route('company.expense-debit-notes.index');
        //   }

        if (isset($input['removed_attachment']) && ! empty($input['removed_attachment'][0])) {
            $cleanedFileArray = explode(',', $input['removed_attachment'][0]);
            $input['attachments'] = array_filter($input['attachments'], function ($file) use ($cleanedFileArray) {
                return ! in_array($file->getClientOriginalName(), $cleanedFileArray);
            });
        }

        if (isset($input['attachments']) && ! empty($input['attachments'])) {
            foreach ($input['attachments'] as $file) {
                $expenseDebitNote->addMedia($file)->toMediaCollection(
                    ExpenseDebitNoteTransaction::INVOICE_ATTACHMENT,
                    config('app.media_disc')
                );
            }
        }

        $notificationTemplate = NotificationTemplate::whereTemplateName(NotificationTemplate::EXPENSE_DEBIT_NOTE)->first();

        $data['to'] = $input['to'] ?? null;
        $data['cc'] = $input['cc'] ?? null;
        $data['body'] = $input['body'] ?? null;
        $data['subject'] = $notificationTemplate->subject ?? null;
        $data['regards'] = $input['regards'] ?? null;
        $data['is_attachment'] = $input['is_attachment'] ?? 1;
        $data['customPaperSize'] = [0, 0, 700, 900];
        $data['fromName'] = getCompanySettings()['from_name'];
        $data['replayToEmail'] = getCompanySettings()['replay_to_email'];

        InvoiceMailJob::dispatch($data, ExpenseDebitNoteTransaction::class);

        Flash::success('Mail Sent Successfully.');
        disableDeletedScope();

        return redirect()->route('company.expense-debit-notes.index');
    }

    public function getInvoicePDFData($debitNoteId): array
    {
        $data = [];
        $data['taxInvoice'] = 'Voucher No';
        $data['configuration'] = ExpenseDebitNote::first();
        $data['currentCompany'] = getCurrentCompany()?->load('addresses', 'companyTax', 'user', 'mailConfiguration', 'media');
        $data['companyBillingAddress'] = $data['currentCompany']->addresses
            ->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['transaction'] = ExpenseDebitNoteTransaction::with(
            'addresses',
            'supplier',
            'transport',
            'debitNoteItems',
            'debitNoteLedgers'
        )->whereId($debitNoteId)->firstOrFail();
        $data['transactionItems'] = $data['transaction']->debitNoteItems->load('items');
        $data['transactionLedgers'] = $data['transaction']->debitNoteLedgers->load('ledgers');
        $data['customerDetail'] = $data['transaction']->supplier->load('model');
        $data['fromNameExists'] = CompanySetting::where('key', 'from_name')->exists();
        $data['replyToEmailExists'] = CompanySetting::where('key', 'replay_to_email')->exists();
        $data['ledgerShippingAddress'] = $data['customerDetail']->model->shippingAddress;
        $addresses = $data['transaction']->addresses;
        $data['billingAddress'] = $addresses->firstWhere('address_type', ExpenseDebitNoteTransaction::BILLING_ADDRESS);
        $data['shippingAddress'] = $addresses->firstWhere(
            'address_type',
            ExpenseDebitNoteTransaction::SHIPPING_ADDRESS
        );
        $data['invoiceDate'] = Carbon::Parse($data['transaction']->original_inv_date)->format('d-m-Y');
        $data['voucherDate'] = Carbon::Parse($data['transaction']->voucher_date)->format('d-m-Y');
        $data['invoiceNo'] = $data['transaction']->supplier_purchase_return_number;
        $data['itemType'] = $data['transaction']->dn_item_type;
        $data['dueDate'] = null;
        $data['creditPeriod'] = null;
        if (! empty($data['transaction']->credit_period)) {

            $data['creditPeriod'] = $data['transaction']->credit_period.' '.SaleTransaction::CREDIT_PERIOD_TYPE[$data['transaction']->credit_period_type];

            if ($data['transaction']->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addMonths($data['transaction']->credit_period)->format('d-m-Y');
            } elseif ($data['transaction']->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addDays($data['transaction']->credit_period)->format('d-m-Y');
            }
        } elseif (! empty($data['customerDetail']->model->credit_limit_period)) {

            $data['creditPeriod'] = $data['customerDetail']->model->credit_limit_period.' '.SaleTransaction::CREDIT_PERIOD_TYPE[$data['customerDetail']->model->credit_period_type];

            if ($data['customerDetail']->model->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addMonths($data['customerDetail']->model->credit_limit_period)->format('d-m-Y');
            } elseif ($data['customerDetail']->model->credit_period_type == SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
                $data['dueDate'] = Carbon::parse($data['transaction']->voucher_date)->addDays($data['customerDetail']->model->credit_limit_period)->format('d-m-Y');
            }
        }

        return $data;
    }

    public function storeExpenseDebitNoteTdsEntry(Request $request): JsonResponse
    {
        $input = $request->all();
        $transactionId = $request->expenseDebitNoteTransactionId;
        $passTdsEntry = (int) ($input['pass_tds_entry'] ?? 0);

        $tdsEntry = [
            'pass_tds_entry' => $passTdsEntry,
            'tds_pan' => $passTdsEntry != 0 ? $input['tds_pan'] : null,
            'tds_taxable_value' => $passTdsEntry != 0 ? $input['tds_taxable_value'] : null,
            'ledger_of_tds' => $passTdsEntry != 0 ? $input['ledger_of_tds'] : null,
            'tds_rate' => $passTdsEntry != 0 ? $input['tds_rate'] : null,
            'tds_amount' => $passTdsEntry != 0 ? $input['tds_amount'] : null,
        ];
        $data = ExpenseDebitNoteTransaction::whereId($transactionId)->first();
        $data->update($tdsEntry);

        updateFieldsValue($data);

        return $this->sendResponse($data, 'TDS Entry store Successfully');
    }

    public function checkExpenseDebitNoteExists(ExpenseDebitNoteTransaction $expenseDebitNote)
    {
        $transactionsData = CheckExistDebitNoteTransactionData::run($expenseDebitNote);

        if (isset($transactionsData['transactionsIsCashMode']) && $transactionsData['transactionsIsCashMode']) {
            return $transactionsData;
        }

        $data['checkTransactionExists'] = $transactionsData['checkTransactionExists'];
        $data['transactionName'] = $transactionsData['transactionName'];
        $data['html'] = view('company.sale.delete_sale_associated_transaction_list')->with($transactionsData)->render();

        return $data;
    }

    /**
     * @return Response|BinaryFileResponse
     */
    public function export(Request $request)
    {
        $input = $request->all();
        $type = $input['type'];
        // $expenseDebitNoteTranCacheKey = generateCacheKey('expense_debit_note_transactions');
        $expenseDebitNoteTranFilterCacheKey = generateCacheKey('expense_debit_note_transactions_filter');
        $company = getCurrentCompany();
        $data = [];
        // $data['data'] = Cache::get($expenseDebitNoteTranCacheKey);
        $filters = Cache::get($expenseDebitNoteTranFilterCacheKey);
        $data = $filters;

        $queryData = ExpenseDebitNoteTransaction::select('expense_debit_note_transactions.*')
            ->whereBetween('voucher_date', [$data['data']['startDate'], $data['data']['endDate']])
            ->when(! empty($data['data']['supplierId']), function ($q) use ($data) {
                $q->where('supplier_id', $data['data']['supplierId']);
            })
            ->when(! empty($data['data']['paymentStatus']), function ($q) use ($data) {
                $q->where('payment_status', $data['data']['paymentStatus']);
            });

        $queryData = $queryData->sorting($data['data']['defaultSorting'])->get();

        /** @var ExpenseDebitNoteTransactionRepository $expenseDebitNoteTranRepo */
        $expenseDebitNoteTranRepo = App::make(ExpenseDebitNoteTransactionRepository::class);
        $data['data']['data'] = $expenseDebitNoteTranRepo->prepareDataForTable($queryData);

        $data['company'] = $company;
        $data['companyAddress'] = $company?->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['durationDate'] = Carbon::parse($input['start_date'])->format('d-m-Y').' to '.Carbon::parse($input['end_date'])->format('d-m-Y');

        if ($type == 'excel') {
            $fileName = 'expenseDebitNoteTransaction'.Carbon::now()->format('d-m-Y').'.xlsx';

            $response = Excel::download(new ExpenseDebitNoteTransactionExport($data), $fileName);
            ob_end_clean();

            return $response;
            // return (new ExpenseDebitNoteTransactionExport($data))->download($fileName);
        }

        $fileName = 'expense-debit-note-transaction.pdf';

        return Pdf::loadView('pdf.expense-debit-note-transaction', $data)
            ->setPaper('a4', 'landscape')
            ->download($fileName);
    }

    public function importDebitNote(Request $request)
    {
        ini_set('max_execution_time', 3600000000);
        ini_set('memory_limit', '500M'); // or you could use nM

        $request->validate([
            'expense_dr_excel_file' => 'required|mimes:xlsx,xls,csv',
        ]);

        $type = 'expense_debit_note';
        $transactionErrorCacheKey = generateCacheKey($type.'_transaction_import_error');
        Cache::forget($transactionErrorCacheKey);

        $input = $request->all();
        if ($input['transaction_type'] == ExpenseDebitNoteTransaction::ITEM_INVOICE) {
            $import = new ExpenseDrItemInvoiceImport();
        } else {
            $import = new ExpenseDrAccountingInvoiceImport();
        }
        $import->import($input['expense_dr_excel_file']);
        $data = $import;

        if (! empty($import->importErrors['missing_voucher_number'])) {
            return $this->sendError($import->importErrors['missing_voucher_number']);
        }
        if (! empty($import->importErrors['field_missing'])) {
            return $this->sendError($import->importErrors['field_missing']);
        }
        if (! empty($import->importErrors['empty_excel'])) {
            return $this->sendError($import->importErrors['empty_excel']);
        }
        if (! empty($import->importErrors['wrong_excel'])) {
            return $this->sendError($import->importErrors['wrong_excel']);
        }
        if (! empty($import->importErrors['unsupported_formula'])) {
            return $this->sendError($import->importErrors['unsupported_formula']);
        }
        $modalIsOpen = false;
        if ($import->importErrors) {
            $modalIsOpen = true;
        }
        Cache::put($transactionErrorCacheKey, $data);
        $html = view('company.import-transaction-error-modal.import_error_list', compact('data', 'type'))->render();

        $importedInvoice = $data->totalInvoice - $data->notImportedInvoice;
        $message = '';
        if ($importedInvoice != 0) {
            $message = 'Expense debit note transaction imported successfully.';
        }

        return $this->sendResponse(['modalIsOpen' => $modalIsOpen, 'html' => $html], $message);
    }

    public function bulkDelete(Request $request)
    {
        $input = $request->all();

        $expenseDebitNotes = ExpenseDebitNoteTransaction::whereIn('id', $input['ids'])->get();
        $showMode = true;
        $data = [];
        $data['alreadyUsed'] = [];
        $data['lockedTransaction'] = [];
        $lockDate = getTransactionsLockDate()['expense'] ?? null;

        foreach ($expenseDebitNotes as $expenseDebitNote) {
            $isLocked = ! empty($lockDate) && Carbon::parse($lockDate)->greaterThanOrEqualTo($expenseDebitNote->voucher_date);
            $transactionsData = CheckExistDebitNoteTransactionData::run($expenseDebitNote);
            if ($isLocked) {
                $data['lockedTransaction'][] = $expenseDebitNote->voucher_number;
            } elseif ($transactionsData['checkTransactionExists']) {
                $data['alreadyUsed'][] = $transactionsData['expenseDebitNote']->voucher_number;
            } else {
                DeleteDebitNoteTransaction::run($expenseDebitNote);
            }
        }

        if (empty($data['alreadyUsed']) && empty($data['lockedTransaction'])) {
            $showMode = false;
        }

        $html = '';
        if (! empty($data)) {
            $html = view('company.item-master.list-view', compact('data'))->render();
        }

        return $this->sendResponse(['html' => $html, 'showMode' => $showMode], 'Expense Debit Note deleted successfully');
    }

    public function expenseDebitNoteBulkDownload(Request $request)
    {
        $input = $request->all();

        $fileName = 'expense-debit-transaction-'.strtotime('now').'.pdf';
        $notification = DownloadBulkPdf::run(Notification::DOWNLOAD, $fileName);
        $currentCompany = getCurrentCompany();

        BulkPdfExportJob::dispatch($input['ids'], $notification, $currentCompany, ExpenseDebitNoteTransaction::class);

        return true;

    }

    public function downloadAttachment(ExpenseDebitNoteTransaction $expenseDebitNote): Response|BinaryFileResponse
    {
        $mediaCollection = $expenseDebitNote->media;

        if ($mediaCollection->isEmpty()) {
            abort(404, 'No documents found.');
        }

        $tempDir = storage_path('app/temp');
        if (! File::exists($tempDir)) {
            File::makeDirectory($tempDir, 0755, true);
        }

        if ($mediaCollection->count() === 1) {
            $mediaItem = $mediaCollection->first();
            $mediaPath = $mediaItem->getPath();

            if (config('app.media_disc') === 'public') {
                $mediaPath = Str::after($mediaItem->getUrl(), '/uploads');
            }

            $file = Storage::disk(config('app.media_disc'))->get($mediaPath);

            return response($file, 200, [
                'Content-Type' => $mediaItem->mime_type,
                'Content-Description' => 'File Transfer',
                'Content-Disposition' => "attachment; filename={$mediaItem->file_name}",
                'filename' => $mediaItem->file_name,
            ]);
        }

        $zipFileName = 'documents_'.time().'.zip';
        $zipPath = storage_path("app/temp/{$zipFileName}");

        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            abort(500, 'Could not create zip file.');
        }

        foreach ($mediaCollection as $mediaItem) {
            $mediaPath = $mediaItem->getPath();
            if (config('app.media_disc') === 'public') {
                $mediaPath = Str::after($mediaItem->getUrl(), '/uploads');
            }

            $fileContent = Storage::disk(config('app.media_disc'))->get($mediaPath);
            $fileName = pathinfo($mediaItem->file_name, PATHINFO_FILENAME)
                .'_'.uniqid()
                .'.'.pathinfo($mediaItem->file_name, PATHINFO_EXTENSION);
            $zip->addFromString($fileName, $fileContent);
        }

        $zip->close();

        return response()->download($zipPath)->deleteFileAfterSend(true);
    }

    public function getExpenseDebitNoteTransactionItemType($itemType): JsonResponse
    {
        $html = null;
        $debitNoteConfiguration = ExpenseDebitNote::first();
        if ($itemType == ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE) {
            $html = view('company.expense-debit-note.append.ledgers_fields', compact('debitNoteConfiguration'))
                ->render();
        }
        if ($itemType == ExpenseDebitNoteTransaction::ITEM_INVOICE) {
            $html = view('company.expense-debit-note.append.items_fields', compact('debitNoteConfiguration'))
                ->render();
        }

        return $this->sendResponse(
            ['html' => $html, 'isGstApplicable' => isCompanyGstApplicable()],
            'Expense Debit Note Transaction screen retrieved successfully'
        );
    }

    public function getExpenseDebitNoteItemList(Request $request)
    {
        $input = $request->all();
        $input['debitNoteConfiguration'] = ExpenseDebitNote::toBase()->first();
        $html = view('company.expense-debit-note.item-invoice-list')->with($input)->render();

        return $this->sendResponse(['html' => $html], 'Item list generated successfully.');
    }

    public function getExpenseDebitNoteAccountingLedgerList(Request $request)
    {
        $input = $request->all();

        $html = view('company.expense-debit-note.item-accounting-ledger-list')->with($input)->render();

        return $this->sendResponse(['html' => $html], 'Ledger list generated successfully.');
    }
}
