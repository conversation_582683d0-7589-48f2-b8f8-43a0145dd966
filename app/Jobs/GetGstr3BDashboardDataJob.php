<?php

namespace App\Jobs;

use App\Actions\Report\GstDashboard\API\GetApiForGSTR2BData;
use App\Actions\Report\GstDashboard\API\GetApiForGSTR3BData;
use App\Models\Company;
use App\Models\GstrLogin;
use App\Services\FileGstService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GetGstr3BDashboardDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 3600000;

    public $dashboardData;

    public $gst3BData;

    public $currentCompany;

    /** @var FileGstService */
    public mixed $fileGstService;

    /**
     * Create a new job instance.
     */
    public function __construct($dashboardData, $gstr3BData)
    {
        $this->dashboardData = $dashboardData;
        $this->gst3BData = $gstr3BData;
        $this->currentCompany = Company::find($this->dashboardData['company_id']);
        $this->fileGstService = new FileGstService();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            session(['current_company' => $this->currentCompany]);
            $gstin = $this->dashboardData['gstin'];
            $companyId = $this->dashboardData['company_id'];

            $gstLogin = GstrLogin::whereCompanyId($companyId)->first()->toArray();

            try {
                foreach ($this->gst3BData as $month) {
                    $data = GetApiForGSTR3BData::run($this->dashboardData, $gstLogin, $month);
                    if (! $data) {
                        GstrLogin::whereCompanyId($companyId)->update(['status' => false, 'synced' => false]);
                        Log::warning('Failed to fetch GSTR3B data', [
                            'gstin' => $gstin,
                            'company_id' => $companyId,
                            'month' => $month['ret_prd'],
                        ]);
                    }

                    $data = GetApiForGSTR2BData::run($this->dashboardData, $gstLogin, $month);
                    if (! $data) {
                        GstrLogin::whereCompanyId($companyId)->update(['status' => false, 'synced' => false]);
                        Log::warning('Failed to fetch GSTR2B data', [
                            'gstin' => $gstin,
                            'company_id' => $companyId,
                            'month' => $month['ret_prd'],
                        ]);
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error in GSTR3B data processing: '.$e->getMessage(), [
                    'gstin' => $gstin,
                    'company_id' => $companyId,
                    'trace' => $e,
                ]);
                GstrLogin::whereCompanyId($companyId)->update(['status' => false, 'synced' => false]);
            }

            GstrLogin::whereCompanyId($companyId)->update(['synced' => false]);
        } catch (\Exception $e) {
            Log::error('GSTR3B Dashboard Data Job failed: '.$e->getMessage(), [
                'gstin' => $this->dashboardData['gstin'] ?? null,
                'company_id' => $this->dashboardData['company_id'] ?? null,
                'trace' => $e,
            ]);

            return;
        }

    }
}
