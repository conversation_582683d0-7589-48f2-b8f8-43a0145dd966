<?php

use App\Actions\v1\Transactions\IncomeCreditNote\StoreIncomeCNTransactionAction;
use App\Models\GstTax;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\Master\ItemMasterGoods;
use Carbon\Carbon;
use Database\Factories\Api\IncomeCreditNoteTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Income Credit Note Transaction Item Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate income credit note transaction item invoice will return gst na incorrect', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make(['is_gst_na' => true, 'is_cgst_sgst_igst_calculated' => true])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return gross value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return addition charge total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Addition charge total is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return taxable value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return cgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = $gst->id;
        $creditNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return sgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = $gst->id;
        $creditNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return igst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = $gst->id;
        $creditNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return classification nature type is empty', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate income credit note transaction item invoice will return add less total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['add_less'][0]['al_total'] = 100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return round off amount is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'is_round_off_not_changed' => 1,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate income credit note transaction item invoice will return grand total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Income Credit Note Transaction Account Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate income credit note transaction account invoice will return gst na incorrect', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make(['cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['with_tax'] = true;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return gross value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return addition charge total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Addition charge total is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = 2;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return taxable value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return cgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $creditNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return sgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $creditNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return igst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $creditNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return classification nature type is empty', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate income credit note transaction account invoice will return add less total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['add_less'][0]['al_total'] = 100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return round off amount is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_round_off_not_changed' => 1,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate income credit note transaction account invoice will return grand total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Income Credit Note Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.2, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.22, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.220, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.2197, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.21970, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(128.2, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(128.16, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(128.155, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(128.1552, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(128.15515, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['rpu_without_gst'] = -10000;
    $creditNoteInput['items'][0]['rpu_with_gst'] = -10000;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertNotEquals(100, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['total'] = -10000;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(28, intval($creditNoteTransaction->incomeCreditNoteItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = $gst->id;
        $creditNoteInput['items'][$key]['discount_type'] = 2;
        $creditNoteInput['items'][$key]['discount_value'] = 10;
        $creditNoteInput['items'][$key]['discount_type_2'] = 2;
        $creditNoteInput['items'][$key]['discount_value_2'] = 10;
        $creditNoteInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    foreach ($creditNoteTransaction->incomeCreditNoteItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['items'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(100, intval($creditNoteTransaction->incomeCreditNoteItems->first()->total));
});

test('validation with update negative and wrong total for income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make(['total' => -620])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make(['gross_value' => -100])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $creditNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('additionalCharges')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertNotEquals(10.00, $creditNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('additionalCharges')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($creditNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(67.04, $creditNoteTransaction->cgst);
    $this->assertEquals(67.04, $creditNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(134.08, $creditNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make(['taxable_value' => -100])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('addLess')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($creditNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make(['grand_total' => -100])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Income Credit Note Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['decimal'] = 1;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.2, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['decimal'] = 2;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.22, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['decimal'] = 3;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.220, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['decimal'] = 4;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.2197, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['decimal'] = 5;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.21970, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['decimal'] = 1;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(128.2, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['decimal'] = 2;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(128.16, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['decimal'] = 3;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(128.155, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['decimal'] = 4;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(128.1552, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['decimal'] = 5;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(128.15515, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertNotEquals(100, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['total'] = -10000;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(28, intval($creditNoteTransaction->incomeCreditNoteLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $creditNoteInput['ledgers'][$key]['discount_type'] = 2;
        $creditNoteInput['ledgers'][$key]['discount_value'] = 10;
        $creditNoteInput['ledgers'][$key]['discount_type_2'] = 2;
        $creditNoteInput['ledgers'][$key]['discount_value_2'] = 10;
        $creditNoteInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.income_credit_note_transaction.credit_note_number'),
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    foreach ($creditNoteTransaction->incomeCreditNoteLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['total'] = -10000;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(78.13, $creditNoteTransaction->incomeCreditNoteLedgers->first()->total);
});

test('validation with update negative and wrong sub total for income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make(['cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['ledgers'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(100, intval($creditNoteTransaction->incomeCreditNoteLedgers->first()->total));
});

test('validation with update negative and wrong total for income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'total' => -620,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'gross_value' => -100,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $creditNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.income_credit_note_transaction.credit_note_number'),
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('additionalCharges')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertNotEquals(10.00, $creditNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make(['cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('additionalCharges')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($creditNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(67.04, $creditNoteTransaction->cgst);
    $this->assertEquals(67.04, $creditNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(134.08, $creditNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'taxable_value' => -100,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('addLess')->whereId($response->json('data.income_credit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($creditNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'grand_total' => -100,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes', $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $response->json('data.income_credit_note_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Income Credit Note Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.2, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.22, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.220, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.2197, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.21970, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(128.2, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(128.16, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(128.155, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(128.1552, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu'] = 100.********;
    $creditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(128.15515, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['rpu_without_gst'] = -10000;
    $creditNoteInput['items'][0]['rpu_with_gst'] = -10000;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertNotEquals(100, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $creditNoteTransaction->incomeCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['total'] = -10000;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(28, intval($creditNoteTransaction->incomeCreditNoteItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = $gst->id;
        $creditNoteInput['items'][$key]['discount_type'] = 2;
        $creditNoteInput['items'][$key]['discount_value'] = 10;
        $creditNoteInput['items'][$key]['discount_type_2'] = 2;
        $creditNoteInput['items'][$key]['discount_value_2'] = 10;
        $creditNoteInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    foreach ($creditNoteTransaction->incomeCreditNoteItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['total'] = -10000;
    $creditNoteInput['items'][0]['gst_id'] = $gst->id;
    $creditNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.13, $creditNoteTransaction->incomeCreditNoteItems->first()->total);
});

test('validation with update negative and wrong sub total for update income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['items'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteItems')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(100, intval($creditNoteTransaction->incomeCreditNoteItems->first()->total));
});

test('validation with update negative and wrong total for update income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['total'] = -620;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['gross_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $creditNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('additionalCharges')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $creditNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('additionalCharges')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($creditNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(67.04, $creditNoteTransaction->cgst);
    $this->assertEquals(67.04, $creditNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update income credit note transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($creditNoteInput['items'] as $key => $item) {
        $creditNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(134.08, $creditNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for update income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['taxable_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('addLess')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($creditNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update income credit note transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make()->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['grand_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Income Credit Note Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['decimal'] = 1;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.2, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['decimal'] = 2;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.22, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['decimal'] = 3;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.220, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['decimal'] = 4;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.2197, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['decimal'] = 5;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.21970, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['decimal'] = 1;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(128.2, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['decimal'] = 2;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(128.16, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['decimal'] = 3;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(128.155, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['decimal'] = 4;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(128.1552, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['decimal'] = 5;
    $creditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $creditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(128.15515, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $creditNoteInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertNotEquals(100, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $creditNoteTransaction->incomeCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_cgst_sgst_igst_calculated' => 1,
        'is_gst_enabled' => true,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['total'] = -10000;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(28, intval($creditNoteTransaction->incomeCreditNoteLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $creditNoteInput['ledgers'][$key]['discount_type'] = 2;
        $creditNoteInput['ledgers'][$key]['discount_value'] = 10;
        $creditNoteInput['ledgers'][$key]['discount_type_2'] = 2;
        $creditNoteInput['ledgers'][$key]['discount_value_2'] = 10;
        $creditNoteInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    foreach ($creditNoteTransaction->incomeCreditNoteLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['total'] = -10000;
    $creditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $creditNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(78.13, $creditNoteTransaction->incomeCreditNoteLedgers->first()->total);
});

test('validation with update negative and wrong sub total for update income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make(['cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['ledgers'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('incomeCreditNoteLedgers')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(100, intval($creditNoteTransaction->incomeCreditNoteLedgers->first()->total));
});

test('validation with update negative and wrong total for update income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['total'] = 620;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['gross_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $creditNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('additionalCharges')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $creditNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make(['cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::with('additionalCharges')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($creditNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(67.04, $creditNoteTransaction->cgst);
    $this->assertEquals(67.04, $creditNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update income credit note transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($creditNoteInput['ledgers'] as $key => $ledger) {
        $creditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = IncomeCreditNoteTransaction::whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(134.08, $creditNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for update income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['taxable_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteInput = IncomeCreditNoteTransaction::with('addLess')->whereId($creditNoteData['transaction_id'])->first();
    $this->assertEquals(10, intval($creditNoteInput->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update income credit note transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $creditNoteInput = IncomeCreditNoteTransactionFactory::new()->make([
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $creditNoteInput['date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreIncomeCNTransactionAction::run($creditNoteInput);
    $creditNoteInput['grand_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/income-credit-notes/'. $creditNoteData['transaction_id'], $creditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Income Credit Note transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('income_credit_note_transactions', [
        'id' => $creditNoteData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $creditNoteInput['invoice_number'],
        'cn_item_type' => IncomeCreditNoteTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});
