<?php

use App\Actions\v1\Transactions\Purchase\StorePurchaseTransactionAction;
use App\Models\GstTax;
use App\Models\Master\ItemMasterGoods;
use App\Models\PurchaseTransaction;
use Carbon\Carbon;
use Database\Factories\Api\PurchaseTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Purchase Transaction Item Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate purchase transaction item invoice will return gst na incorrect', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['is_gst_na' => true, 'is_cgst_sgst_igst_calculated' => true])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return gross value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return addition charge total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Addition charge total is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return taxable value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return cgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return sgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return igst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return classification nature type is empty', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate purchase transaction item invoice will return add less total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['add_less'][0]['al_total'] = 100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return round off amount is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_na' => true,
        'is_round_off_not_changed' => 1,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate purchase transaction item invoice will return grand total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Purchase Transaction Account Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate purchase transaction account invoice will return gst na incorrect', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['total'] = 10;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['with_tax'] = true;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return gross value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return addition charge total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Addition charge total is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseInput['ledgers'] as $key => $item) {
        $purchaseInput['ledgers'][$key]['gst_id'] = 2;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return taxable value is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return cgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseInput['ledgers'] as $key => $item) {
        $purchaseInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return sgst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseInput['ledgers'] as $key => $item) {
        $purchaseInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return igst is not correct in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseInput['ledgers'] as $key => $item) {
        $purchaseInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return classification nature type is empty', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseInput['ledgers'] as $key => $item) {
        $purchaseInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate purchase transaction account invoice will return add less total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['add_less'][0]['al_total'] = 100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return round off amount is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_round_off_not_changed' => 1,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate purchase transaction account invoice will return grand total is not correct', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Purchase Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.2, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.22, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.220, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.2197, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.21970, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.2, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.16, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.155, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.1552, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.15515, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['rpu_without_gst'] = -10000;
    $purchaseInput['items'][0]['rpu_with_gst'] = -10000;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertNotEquals(100, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['total'] = -10000;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(28, intval($purchaseTransaction->purchaseTransactionItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseInput['items'][$key]['discount_type'] = 2;
        $purchaseInput['items'][$key]['discount_value'] = 10;
        $purchaseInput['items'][$key]['discount_type_2'] = 2;
        $purchaseInput['items'][$key]['discount_value_2'] = 10;
        $purchaseInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    foreach ($purchaseTransaction->purchaseTransactionItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['total'] = -10000;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.13, $purchaseTransaction->purchaseTransactionItems->first()->total);
});

test('validation with update negative and wrong sub total for purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['items'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(100, intval($purchaseTransaction->purchaseTransactionItems->first()->total));
});

test('validation with update negative and wrong total for purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['total' => -620])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['gross_value' => -100])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertNotEquals(10.00, $purchaseTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(67.04, $purchaseTransaction->cgst);
    $this->assertEquals(67.04, $purchaseTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(134.08, $purchaseTransaction->igst);
});

test('validation with update negative and wrong taxable value for purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['taxable_value' => -100])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['grand_total' => -100])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Purchase Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['decimal'] = 1;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.2, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['decimal'] = 2;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.22, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['decimal'] = 3;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.220, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['decimal'] = 4;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.2197, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['decimal'] = 5;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.21970, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['decimal'] = 1;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.2, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['decimal'] = 2;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.16, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['decimal'] = 3;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.155, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['decimal'] = 4;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.1552, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['decimal'] = 5;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.15515, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertNotEquals(100, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['total'] = -10000;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(28, intval($purchaseTransaction->purchaseTransactionLedger->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    foreach ($purchaseInput['ledgers'] as $key => $ledger) {
        $purchaseInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseInput['ledgers'][$key]['discount_type'] = 2;
        $purchaseInput['ledgers'][$key]['discount_value'] = 10;
        $purchaseInput['ledgers'][$key]['discount_type_2'] = 2;
        $purchaseInput['ledgers'][$key]['discount_value_2'] = 10;
        $purchaseInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    foreach ($purchaseTransaction->purchaseTransactionLedger as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['total'] = -10000;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.13, $purchaseTransaction->purchaseTransactionLedger->first()->total);
});

test('validation with update negative and wrong sub total for purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['ledgers'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(100, intval($purchaseTransaction->purchaseTransactionLedger->first()->total));
});

test('validation with update negative and wrong total for purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'total' => -620,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'gross_value' => -100,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertNotEquals(10.00, $purchaseTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseInput['ledgers'] as $key => $ledger) {
        $purchaseInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(67.04, $purchaseTransaction->cgst);
    $this->assertEquals(67.04, $purchaseTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseInput['ledgers'] as $key => $ledger) {
        $purchaseInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(134.08, $purchaseTransaction->igst);
});

test('validation with update negative and wrong taxable value for purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'taxable_value' => -100,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'grand_total' => -100,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase', $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $response->json('data.purchase_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Purchase Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.2, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.22, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.220, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.2197, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.21970, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.2, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.16, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.155, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.1552, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu'] = 100.********;
    $purchaseInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['items'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.15515, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['rpu_without_gst'] = -10000;
    $purchaseInput['items'][0]['rpu_with_gst'] = -10000;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertNotEquals(100, $purchaseTransaction->purchaseTransactionItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseTransaction->purchaseTransactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['total'] = -10000;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(28, intval($purchaseTransaction->purchaseTransactionItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseInput['items'][$key]['discount_type'] = 2;
        $purchaseInput['items'][$key]['discount_value'] = 10;
        $purchaseInput['items'][$key]['discount_type_2'] = 2;
        $purchaseInput['items'][$key]['discount_value_2'] = 10;
        $purchaseInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    foreach ($purchaseTransaction->purchaseTransactionItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['total'] = -10000;
    $purchaseInput['items'][0]['gst_id'] = $gst->id;
    $purchaseInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.13, $purchaseTransaction->purchaseTransactionItems->first()->total);
});

test('validation with update negative and wrong sub total for update purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['items'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionItems')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(100, intval($purchaseTransaction->purchaseTransactionItems->first()->total));
});

test('validation with update negative and wrong total for update purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['total'] = -620;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['gross_value' => -100])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertNotEquals(10.00, $purchaseTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(67.04, $purchaseTransaction->cgst);
    $this->assertEquals(67.04, $purchaseTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for update purchase transaction item invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseInput['items'] as $key => $item) {
        $purchaseInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(134.08, $purchaseTransaction->igst);
});

test('validation with update negative and wrong taxable value for update purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['taxable_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update purchase transaction item invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make()->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['grand_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Purchase Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['decimal'] = 1;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.2, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['decimal'] = 2;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.22, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['decimal'] = 3;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.220, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['decimal'] = 4;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.2197, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['decimal'] = 5;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.21970, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['decimal'] = 1;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.2, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['decimal'] = 2;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.16, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for purchase purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['decimal'] = 3;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.155, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for purchase purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['decimal'] = 4;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.1552, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for purchase purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['decimal'] = 5;
    $purchaseInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(128.15515, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $purchaseInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertNotEquals(100, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseTransaction->purchaseTransactionLedger->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['total'] = -10000;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(28, intval($purchaseTransaction->purchaseTransactionLedger->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    foreach ($purchaseInput['ledgers'] as $key => $ledger) {
        $purchaseInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseInput['ledgers'][$key]['discount_type'] = 2;
        $purchaseInput['ledgers'][$key]['discount_value'] = 10;
        $purchaseInput['ledgers'][$key]['discount_type_2'] = 2;
        $purchaseInput['ledgers'][$key]['discount_value_2'] = 10;
        $purchaseInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    foreach ($purchaseTransaction->purchaseTransactionLedger as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['total'] = -10000;
    $purchaseInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(78.13, $purchaseTransaction->purchaseTransactionLedger->first()->total);
});

test('validation with update negative and wrong sub total for update purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['ledgers'][0]['total'] = -10000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('purchaseTransactionLedger')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(100, intval($purchaseTransaction->purchaseTransactionLedger->first()->total));
});

test('validation with update negative and wrong total for update purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['total'] = -620;
    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['gross_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertNotEquals(10.00, $purchaseTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make(['purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseInput['ledgers'] as $key => $ledger) {
        $purchaseInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(67.04, $purchaseTransaction->cgst);
    $this->assertEquals(67.04, $purchaseTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for update purchase transaction account invoice in gst company', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1, true);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseInput['ledgers'] as $key => $ledger) {
        $purchaseInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseTransaction = PurchaseTransaction::whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(134.08, $purchaseTransaction->igst);
});

test('validation with update negative and wrong taxable value for update purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['taxable_value'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['add_less'][0]['al_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseTransaction = PurchaseTransaction::with('additionalCharges')->whereId($response->json('data.purchase_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update purchase transaction account invoice', function () {
    //arrange
    $user = setCompanyAndGenerateToken(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseInput = PurchaseTransactionFactory::new()->make([
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $purchaseInput['date_of_invoice'] = Carbon::now()->format('d-m-Y');
    $purchaseData = StorePurchaseTransactionAction::run($purchaseInput);
    $purchaseInput['grand_total'] = -100;

    //act
    $response = postAPI($user->token, $user->company->id, 'api/v2/purchase/'.$purchaseData['purchase_transaction']['id'], $purchaseInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Transaction Update Successfully',
        ]);
    $this->assertDatabaseHas('purchase_transactions', [
        'id' => $purchaseData['purchase_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $response->json('data.purchase_transaction.voucher_number'),
        'purchase_item_type' => PurchaseTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});
