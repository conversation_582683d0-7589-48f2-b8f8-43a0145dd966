<?php

use App\Actions\v1\Transactions\SaleReturn\StoreSaleReturnTransactionAction;
use App\Models\GstTax;
use App\Models\Master\ItemMasterGoods;
use App\Models\SaleReturnTransaction;
use Carbon\Carbon;
use Database\Factories\Api\SaleReturnTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Sale Return Transaction Item Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate sale return transaction item invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['is_gst_na' => true, 'is_cgst_sgst_igst_calculated' => 1])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['items'][$key]['gst_tax_percentage'] = $gst->tax_rate;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Addition charge total is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate sale return transaction item invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'taxable_value' => 610,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
    }
    $saleReturnInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate sale return transaction item invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Sale Transaction Account Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate sale return transaction account invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['with_tax'] = true;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Addition charge total is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate sale return transaction account invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate sale return transaction account invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Sale Return Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.2, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.22, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.220, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.2197, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.21970, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(128.2, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(128.16, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(128.155, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(128.1552, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(128.15515, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['rpu_without_gst'] = -10000;
    $saleReturnInput['items'][0]['rpu_with_gst'] = -10000;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);

    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertNotEquals(100.0, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['total'] = -10000;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(28, intval($saleReturnTransaction->saleReturnItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['items'][$key]['discount_type'] = 2;
        $saleReturnInput['items'][$key]['discount_value'] = 10;
        $saleReturnInput['items'][$key]['discount_type_2'] = 2;
        $saleReturnInput['items'][$key]['discount_value_2'] = 10;
        $saleReturnInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    foreach ($saleReturnTransaction->saleReturnItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['total'] = -10000;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.13, $saleReturnTransaction->saleReturnItems->first()->total);
});

test('validation with update negative and wrong sub total for sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(100, intval($saleReturnTransaction->saleReturnItems->first()->total));
});

test('validation with update negative and wrong total for sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['total' => -620])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['gross_value' => -100])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $saleReturnInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertNotEquals(10.00, $saleReturnTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(10, intval($saleReturnTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(67.04, $saleReturnTransaction->cgst);
    $this->assertEquals(67.04, $saleReturnTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(134.08, $saleReturnTransaction->igst);
});

test('validation with update negative and wrong cgst when deemed export - intrastate classification for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Intrastate',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(67.04, $saleReturnTransaction->cgst);
});

test('validation with update negative and wrong igst when deemed export - interstate classification for sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Interstate',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(134.08, $saleReturnTransaction->igst);
});

test('validation with update negative and wrong taxable value for sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['taxable_value' => -100])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(10, intval($saleReturnTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['grand_total' => -100])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Sale Return Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['decimal'] = 1;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.2, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['decimal'] = 2;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.22, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['decimal'] = 3;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.220, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['decimal'] = 4;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.2197, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['decimal'] = 5;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.21970, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['decimal'] = 1;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(128.2, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['decimal'] = 2;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(128.16, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['decimal'] = 3;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(128.155, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['decimal'] = 4;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(128.1552, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['decimal'] = 5;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(128.15515, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertNotEquals(100, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['total'] = -10000;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(28, intval($saleReturnTransaction->saleReturnLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['ledgers'][$key]['discount_type'] = 2;
        $saleReturnInput['ledgers'][$key]['discount_value'] = 10;
        $saleReturnInput['ledgers'][$key]['discount_type_2'] = 2;
        $saleReturnInput['ledgers'][$key]['discount_value_2'] = 10;
        $saleReturnInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    foreach ($saleReturnTransaction->saleReturnLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['total'] = -10000;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(78.13, $saleReturnTransaction->saleReturnLedgers->first()->total);
});

test('validation with update negative and wrong sub total for sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(100, intval($saleReturnTransaction->saleReturnLedgers->first()->total));
});

test('validation with update negative and wrong total for sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE, 'total' => -620])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE, 'gross_value' => -100])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $saleReturnInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertNotEquals(10.00, $saleReturnTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional chanrges total for sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(10, intval($saleReturnTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(67.04, $saleReturnTransaction->cgst);
    $this->assertEquals(67.04, $saleReturnTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(134.08, $saleReturnTransaction->igst);
});

test('validation with update negative and wrong cgst when deemed export - intrastate classification for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Intrastate',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(67.04, $saleTransaction->cgst);
});

test('validation with update negative and wrong igst when deemed export - interstate classification for sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Interstate',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(134.08, $saleTransaction->igst);
});

test('validation with update negative and wrong taxable value for sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE, 'taxable_value' => -100])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(10, intval($saleReturnTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE, 'grand_total' => -100])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('sale-return-transaction'), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Created Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $response->json('data.sale_return_transaction.id'),
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Sale Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.2, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.22, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.220, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.2197, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.21970, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(128.2, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(128.16, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(128.155, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(128.1552, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['items'][0]['rpu'] = 100.********;
    $saleReturnInput['items'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['items'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(128.15515, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput['items'][0]['rpu_without_gst'] = -10000;
    $saleReturnInput['items'][0]['rpu_with_gst'] = -10000;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertNotEquals(100, $saleReturnTransaction->saleReturnItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $saleReturnTransaction->saleReturnItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput['items'][0]['total'] = -10000;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(28, intval($saleReturnTransaction->saleReturnItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $gst = GstTax::whereTaxRate(5.00)->first();

    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['items'][$key]['discount_type'] = 2;
        $saleReturnInput['items'][$key]['discount_value'] = 10;
        $saleReturnInput['items'][$key]['discount_type_2'] = 2;
        $saleReturnInput['items'][$key]['discount_value_2'] = 10;
        $saleReturnInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    foreach ($saleReturnTransaction->saleReturnItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput['items'][0]['total'] = -10000;
    $saleReturnInput['items'][0]['gst_id'] = $gst->id;
    $saleReturnInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.13, $saleReturnTransaction->saleReturnItems->first()->total);
});

test('validation with update negative and wrong sub total for update sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);
    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(100, intval($saleReturnTransaction->saleReturnItems->first()->total));
});

test('validation with update negative and wrong total for update sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['total'] = -620;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $saleReturnInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $saleReturnTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional chanrges total for update sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(10, intval($saleReturnTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(67.04, $saleReturnTransaction->cgst);
    $this->assertEquals(67.04, $saleReturnTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnItems')->whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(134.08, $saleReturnTransaction->igst);
});

test('validation with update negative and wrong cgst when deemed export - intrastate classification for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Intrastate',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(67.04, $saleReturnTransaction->cgst);
});

test('validation with update negative and wrong igst when deemed export - interstate classification for update sale return transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Interstate',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['items'] as $key => $item) {
        $saleReturnInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(134.08, $saleReturnTransaction->igst);
});

test('validation with update negative and wrong taxable value for update sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(10, intval($saleReturnTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update sale return transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make()->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Sale Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['ledgers'][0]['decimal'] = 1;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.2, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['ledgers'][0]['decimal'] = 2;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.22, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['ledgers'][0]['decimal'] = 3;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.220, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['ledgers'][0]['decimal'] = 4;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.2197, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['ledgers'][0]['decimal'] = 5;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.21970, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['ledgers'][0]['decimal'] = 1;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(128.2, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['ledgers'][0]['decimal'] = 2;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(128.16, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['ledgers'][0]['decimal'] = 3;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(128.155, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['ledgers'][0]['decimal'] = 4;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(128.1552, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $saleReturnInput['ledgers'][0]['decimal'] = 5;
    $saleReturnInput['ledgers'][0]['rpu'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $saleReturnInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(128.15515, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $saleReturnInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertNotEquals(100, $saleReturnTransaction->saleReturnLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $saleReturnTransaction->saleReturnLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput['ledgers'][0]['total'] = -10000;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(28, intval($saleReturnTransaction->saleReturnLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();
    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
        $saleReturnInput['ledgers'][$key]['discount_type'] = 2;
        $saleReturnInput['ledgers'][$key]['discount_value'] = 10;
        $saleReturnInput['ledgers'][$key]['discount_type_2'] = 2;
        $saleReturnInput['ledgers'][$key]['discount_value_2'] = 10;
        $saleReturnInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    foreach ($saleReturnTransaction->saleReturnLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput['ledgers'][0]['total'] = -10000;
    $saleReturnInput['ledgers'][0]['gst_id'] = $gst->id;
    $saleReturnInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(78.13, $saleReturnTransaction->saleReturnLedgers->first()->total);
});

test('validation with update negative and wrong sub total for update sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('saleReturnLedgers')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(100, intval($saleReturnTransaction->saleReturnLedgers->first()->total));
});

test('validation with update negative and wrong total for update sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['total'] = -620;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $saleReturnInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $saleReturnTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional chanrges total for update sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(10, intval($saleReturnTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when sale intra state taxable classification for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Intra State Taxable',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(67.04, $saleReturnTransaction->cgst);
    $this->assertEquals(67.04, $saleReturnTransaction->sgst);
});

test('validation with update negative and wrong igst when sale inter state taxable classification for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Sale Inter State Taxable',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(134.08, $saleReturnTransaction->igst);
});

test('validation with update negative and wrong cgst when deemed export - intrastate classification for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Intrastate',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(67.04, $saleReturnTransaction->cgst);
});

test('validation with update negative and wrong igst when deemed export - interstate classification for update sale return transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $saleReturnInput = SaleReturnTransactionFactory::new()->make([
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Deemed Export - Interstate',
    ])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($saleReturnInput['ledgers'] as $key => $ledger) {
        $saleReturnInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::whereId($response->json('data.sale_return_transaction.id'))->first();
    $this->assertEquals(134.08, $saleReturnTransaction->igst);
});

test('validation with update negative and wrong taxable value for update sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
    $saleReturnTransaction = SaleReturnTransaction::with('additionalCharges')->whereId($saleReturnData['transaction_id'])->first();
    $this->assertEquals(10, intval($saleReturnTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update sale return transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $saleReturnInput = SaleReturnTransactionFactory::new()->make(['sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE])->toArray();
    $saleReturnInput['date'] = Carbon::now()->format('d-m-Y');
    $saleReturnData = StoreSaleReturnTransactionAction::run($saleReturnInput);

    $saleReturnInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('sale-return-transaction-update', $saleReturnData['transaction_id']), $saleReturnInput);

    //asert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Sale Return Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('sale_return_transactions', [
        'id' => $saleReturnData['transaction_id'],
        'company_id' => 1,
        'credit_note_number' => $response->json('data.sale_return_transaction.credit_note_number'),
        'sale_return_item_type' => SaleReturnTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});
