<?php

use App\Actions\v1\Transactions\ExpenseCreditNote\StoreExpenseCNTransactionAction;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\GstTax;
use App\Models\Master\ItemMasterGoods;
use Carbon\Carbon;
use Database\Factories\Api\ExpenseCreditNoteTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Expense Credit Note Transaction Item Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate expense credit note transaction item invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make(['is_gst_na' => true, 'is_cgst_sgst_igst_calculated' => true])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Addition charge total is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = $gst->id;
        $expenseCreditNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = $gst->id;
        $expenseCreditNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = $gst->id;
        $expenseCreditNoteInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate expense credit note transaction item invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'is_round_off_not_changed' => 1,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate expense credit note transaction item invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Expense Credit Note Transaction Account Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate expense credit note transaction account invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make(['expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = true;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Addition charge total is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $expenseCreditNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'cgst' => 65.64,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $expenseCreditNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $expenseCreditNoteInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate expense credit note transaction account invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_round_off_not_changed' => 1,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate expense credit note transaction account invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Expense Credit Note Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.2, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.22, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.220, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.2197, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.21970, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(128.2, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(128.16, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(128.155, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(128.1552, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(128.15515, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = -10000;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = -10000;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertNotEquals(100, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['total'] = -10000;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(28, intval($creditNoteTransaction->expenseCreditNoteItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = $gst->id;
        $expenseCreditNoteInput['items'][$key]['discount_type'] = 2;
        $expenseCreditNoteInput['items'][$key]['discount_value'] = 10;
        $expenseCreditNoteInput['items'][$key]['discount_type_2'] = 2;
        $expenseCreditNoteInput['items'][$key]['discount_value_2'] = 10;
        $expenseCreditNoteInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    foreach ($creditNoteTransaction->expenseCreditNoteItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(100, intval($creditNoteTransaction->expenseCreditNoteItems->first()->total));
});

test('validation with update negative and wrong total for expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make(['total' => -620])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make(['gross_value' => -100])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $expenseCreditNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('additionalCharges')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertNotEquals(10.00, $creditNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('additionalCharges')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($creditNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when intrastate purchase taxable classification for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(67.04, $creditNoteTransaction->cgst);
    $this->assertEquals(67.04, $creditNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(134.08, $creditNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make(['taxable_value' => -100])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('addLess')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($creditNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make(['grand_total' => -100])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Expense Credit Note Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 1;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.2, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.22, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 3;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.220, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 4;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.2197, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 5;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.21970, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 1;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(128.2, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(128.16, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 3;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(128.155, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 4;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(128.1552, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 5;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(128.15515, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertNotEquals(100, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['total'] = -10000;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(28, intval($creditNoteTransaction->expenseCreditNoteLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $expenseCreditNoteInput['ledgers'][$key]['discount_type'] = 2;
        $expenseCreditNoteInput['ledgers'][$key]['discount_value'] = 10;
        $expenseCreditNoteInput['ledgers'][$key]['discount_type_2'] = 2;
        $expenseCreditNoteInput['ledgers'][$key]['discount_value_2'] = 10;
        $expenseCreditNoteInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    foreach ($creditNoteTransaction->expenseCreditNoteLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['total'] = -10000;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(78.13, $creditNoteTransaction->expenseCreditNoteLedgers->first()->total);
});

test('validation with update negative and wrong sub total for expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make(['expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(100, intval($creditNoteTransaction->expenseCreditNoteLedgers->first()->total));
});

test('validation with update negative and wrong total for expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'total' => -620,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'gross_value' => -100,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $expenseCreditNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('additionalCharges')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertNotEquals(10.00, $creditNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make(['expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('additionalCharges')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($creditNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(67.04, $creditNoteTransaction->cgst);
    $this->assertEquals(67.04, $creditNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(134.08, $creditNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'taxable_value' => -100,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $expenseCreditNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('addLess')->whereId($response->json('data.expense_credit_note_transaction.id'))->first();
    $this->assertEquals(10, intval($creditNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'grand_total' => -100,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');

    //act
    $response = $this->postJson(route('expense-credit-note-transaction'), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction created Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $response->json('data.expense_credit_note_transaction.id'),
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Expense Credit Note Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.2, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.22, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.220, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.2197, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.21970, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(128.2, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(128.16, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(128.155, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(128.1552, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(128.15515, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['rpu_without_gst'] = -10000;
    $expenseCreditNoteInput['items'][0]['rpu_with_gst'] = -10000;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertNotEquals(100, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $creditNoteTransaction->expenseCreditNoteItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['total'] = -10000;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(28, intval($creditNoteTransaction->expenseCreditNoteItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = $gst->id;
        $expenseCreditNoteInput['items'][$key]['discount_type'] = 2;
        $expenseCreditNoteInput['items'][$key]['discount_value'] = 10;
        $expenseCreditNoteInput['items'][$key]['discount_type_2'] = 2;
        $expenseCreditNoteInput['items'][$key]['discount_value_2'] = 10;
        $expenseCreditNoteInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    foreach ($creditNoteTransaction->expenseCreditNoteItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['total'] = -10000;
    $expenseCreditNoteInput['items'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.13, $creditNoteTransaction->expenseCreditNoteItems->first()->total);
});

test('validation with update negative and wrong sub total for update expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteItems')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(100, intval($creditNoteTransaction->expenseCreditNoteItems->first()->total));
});

test('validation with update negative and wrong total for update expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['total'] = -620;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $expenseCreditNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('additionalCharges')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertNotEquals(10.00, $creditNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('additionalCharges')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(10, intval($creditNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(67.04, $creditNoteTransaction->cgst);
    $this->assertEquals(67.04, $creditNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for update expense credit note transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseCreditNoteInput['items'] as $key => $item) {
        $expenseCreditNoteInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(134.08, $creditNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for update expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('addLess')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(10, intval($creditNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update expense credit note transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make()->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Expense Credit Note Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 1;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.2, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.22, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 3;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.220, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 4;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.2197, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 5;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.21970, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 1;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(128.2, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(128.16, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 3;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(128.155, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 4;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(128.1552, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['decimal'] = 5;
    $expenseCreditNoteInput['ledgers'][0]['rpu'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $expenseCreditNoteInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(128.15515, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $expenseCreditNoteInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertNotEquals(100, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $creditNoteTransaction->expenseCreditNoteLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_cgst_sgst_igst_calculated' => 1,
        'is_gst_enabled' => true,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['total'] = -10000;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(28, intval($creditNoteTransaction->expenseCreditNoteLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
        $expenseCreditNoteInput['ledgers'][$key]['discount_type'] = 2;
        $expenseCreditNoteInput['ledgers'][$key]['discount_value'] = 10;
        $expenseCreditNoteInput['ledgers'][$key]['discount_type_2'] = 2;
        $expenseCreditNoteInput['ledgers'][$key]['discount_value_2'] = 10;
        $expenseCreditNoteInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    foreach ($creditNoteTransaction->expenseCreditNoteLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['total'] = -10000;
    $expenseCreditNoteInput['ledgers'][0]['gst_id'] = $gst->id;
    $expenseCreditNoteInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(78.13, $creditNoteTransaction->expenseCreditNoteLedgers->first()->total);
});

test('validation with update negative and wrong sub total for update expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make(['expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('expenseCreditNoteLedgers')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(100, intval($creditNoteTransaction->expenseCreditNoteLedgers->first()->total));
});

test('validation with update negative and wrong total for update expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['total'] = 620;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $expenseCreditNoteInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('additionalCharges')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertNotEquals(10.00, $creditNoteTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make(['expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('additionalCharges')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(10, intval($creditNoteTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(67.04, $creditNoteTransaction->cgst);
    $this->assertEquals(67.04, $creditNoteTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for update expense credit note transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($expenseCreditNoteInput['ledgers'] as $key => $ledger) {
        $expenseCreditNoteInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(134.08, $creditNoteTransaction->igst);
});

test('validation with update negative and wrong taxable value for update expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $creditNoteTransaction = ExpenseCreditNoteTransaction::with('addLess')->whereId($creditNoteData['expense_credit_note_transaction']['id'])->first();
    $this->assertEquals(10, intval($creditNoteTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update expense credit note transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $expenseCreditNoteInput = ExpenseCreditNoteTransactionFactory::new()->make([
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $expenseCreditNoteInput['voucher_date'] = Carbon::now()->format('d-m-Y');
    $creditNoteData = StoreExpenseCNTransactionAction::run($expenseCreditNoteInput);
    $expenseCreditNoteInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('expense-credit-note-transaction-update', $creditNoteData['expense_credit_note_transaction']['id']), $expenseCreditNoteInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Expense credit note Transaction Updated Successfully',
        ]);
    $this->assertDatabaseHas('expense_credit_note_transactions', [
        'id' => $creditNoteData['expense_credit_note_transaction']['id'],
        'company_id' => 1,
        'voucher_number' => $expenseCreditNoteInput['voucher_number'],
        'expense_cn_item_type' => ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});
