<?php

use App\Actions\v1\Transactions\PurchaseOrder\StorePurchaseOrderTransactionAction;
use App\Models\GstTax;
use App\Models\Master\ItemMasterGoods;
use App\Models\PurchaseOrderTransaction;
use Database\Factories\Api\PurchaseOrderTransactionFactory;

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Purchase Order Transaction Item Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate purchase order transaction item invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make(['is_gst_na' => true, 'is_cgst_sgst_igst_calculated' => true])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $purchaseOrderInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_na' => true,
    ])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Addition charge total is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseOrderInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'cgst' => 65.64,
    ])->toArray();
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseOrderInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
    ])->toArray();
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseOrderInput['items'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate purchase order transaction item invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $purchaseOrderInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_na' => true,
        'is_round_off_not_changed' => 1,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate purchase order transaction item invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Purchase order Transaction Account Invoice Backend Validation Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validate purchase order transaction account invoice will return gst na incorrect', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make(['order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is GST NA is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return cgst, sgst, and igst calculated is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'is_cgst_sgst_igst_calculated' => true,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Is CGST, SGST, and IGST Calculated is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return subtotal is not correct when pass wrong total', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return subtotal is not correct when pass wrong total with gst na, exempt, zero in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['total'] = 10;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return subtotal is not correct when pass wrong total with gst percentage', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = true;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Subtotal is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return gross value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'gross_value' => 10,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Gross value is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return addition charge total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
    ])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_value'] = 20;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Addition charge total is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return Taxable value is not correct when pass additional gst percentage in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $gst = GstTax::whereTaxRate(28.00)->first();
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseOrderInput['ledgers'] as $key => $item) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = 2;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return taxable value is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 10,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Taxable value is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return cgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    foreach ($purchaseOrderInput['ledgers'] as $key => $ledger) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseOrderInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'CGST is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return sgst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'cgst' => 65.64,
    ])->toArray();
    foreach ($purchaseOrderInput['ledgers'] as $key => $item) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseOrderInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'SGST is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return igst is not correct in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'gross_value' => 468.78,
        'taxable_value' => 478.78,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    foreach ($purchaseOrderInput['ledgers'] as $key => $ledger) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseOrderInput['ledgers'][$key]['total'] = 78.13;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'IGST is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return classification nature type is empty', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(0.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_enabled' => true,
        'main_classification_nature_type' => null,
        'taxable_value' => 610,
    ])->toArray();
    foreach ($purchaseOrderInput['ledgers'] as $key => $item) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Classification Nature Type is empty.',
        ]);
});

test('validate purchase order transaction account invoice will return add less total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
    ])->toArray();
    $purchaseOrderInput['add_less'][0]['al_total'] = 100;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Add Less total is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return round off amount is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_round_off_not_changed' => 1,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'rounding_amount' => 10.50,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Round off amount is not correct.',
        ]);
});

test('validate purchase order transaction account invoice will return grand total is not correct', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_show_error' => true, 'app.is_update_value_with_validation' => false]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'is_gst_na' => true,
        'taxable_value' => 610,
        'grand_total' => 100,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(422)
        ->assertJson([
            'success' => false,
            'message' => 'Grand total is not correct.',
        ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Purchase Order Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.2, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.22, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.220, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.2197, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.21970, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.2, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.16, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.155, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.1552, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.15515, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = -10000;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = -10000;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertNotEquals(100, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['total'] = -10000;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(28, intval($purchaseOrderTransaction->transactionItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseOrderInput['items'][$key]['discount_type'] = 2;
        $purchaseOrderInput['items'][$key]['discount_value'] = 10;
        $purchaseOrderInput['items'][$key]['discount_type_2'] = 2;
        $purchaseOrderInput['items'][$key]['discount_value_2'] = 10;
        $purchaseOrderInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    foreach ($purchaseOrderTransaction->transactionItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['items'][0]['total'] = -10000;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.13, $purchaseOrderTransaction->transactionItems->first()->total);
});

test('validation with update negative and wrong sub total for purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();
    $purchaseOrderInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(100, intval($purchaseOrderTransaction->transactionItems->first()->total));
});

test('validation with update negative and wrong total for purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make(['total' => -620])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make(['gross_value' => -100])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseOrderInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertNotEquals(10.00, $purchaseOrderTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseOrderTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(67.04, $purchaseOrderTransaction->cgst);
    $this->assertEquals(67.04, $purchaseOrderTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(134.08, $purchaseOrderTransaction->igst);
});

test('validation with update negative and wrong taxable value for purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make(['taxable_value' => -100])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();
    $purchaseOrderInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseOrderTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make(['grand_total' => -100])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Store Purchase Order Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['decimal'] = 1;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.2, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['decimal'] = 2;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.22, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['decimal'] = 3;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.220, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['decimal'] = 4;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.2197, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['decimal'] = 5;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.21970, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['decimal'] = 1;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.2, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['decimal'] = 2;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.16, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['decimal'] = 3;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.155, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['decimal'] = 4;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.1552, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['decimal'] = 5;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.15515, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertNotEquals(100, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['total'] = -10000;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(28, intval($purchaseOrderTransaction->transactionLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    foreach ($purchaseOrderInput['ledgers'] as $key => $ledger) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseOrderInput['ledgers'][$key]['discount_type'] = 2;
        $purchaseOrderInput['ledgers'][$key]['discount_value'] = 10;
        $purchaseOrderInput['ledgers'][$key]['discount_type_2'] = 2;
        $purchaseOrderInput['ledgers'][$key]['discount_value_2'] = 10;
        $purchaseOrderInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    foreach ($purchaseOrderTransaction->transactionLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['ledgers'][0]['total'] = -10000;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.13, $purchaseOrderTransaction->transactionLedgers->first()->total);
});

test('validation with update negative and wrong sub total for purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make(['order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE])->toArray();
    $purchaseOrderInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(100, intval($purchaseOrderTransaction->transactionLedgers->first()->total));
});

test('validation with update negative and wrong total for purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'total' => -620,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'gross_value' => -100,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseOrderInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertNotEquals(10.00, $purchaseOrderTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make(['order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseOrderTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseOrderInput['ledgers'] as $key => $ledger) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(67.04, $purchaseOrderTransaction->cgst);
    $this->assertEquals(67.04, $purchaseOrderTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseOrderInput['ledgers'] as $key => $ledger) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(134.08, $purchaseOrderTransaction->igst);
});

test('validation with update negative and wrong taxable value for purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'taxable_value' => -100,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(10, intval($purchaseOrderTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'grand_total' => -100,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();

    //act
    $response = $this->postJson(route('purchase-order-transaction'), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Created Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $response->json('data.purchase_order_transaction.id'),
        'company_id' => 1,
        'order_number' => $response->json('data.purchase_order_transaction.order_number'),
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Purchase Order Transaction Item Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.2, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.22, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.220, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.2197, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(78.21970, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update purchase transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 1]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.2, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update purchase transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 2]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.16, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update purchase transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 3]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.155, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update purchase transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 4]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.1552, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update purchase transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    ItemMasterGoods::query()->update(['decimal_places_for_rate' => 5]);
    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['items'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(128.15515, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['rpu_without_gst'] = -10000;
    $purchaseOrderInput['items'][0]['rpu_with_gst'] = -10000;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertNotEquals(100, $purchaseOrderTransaction->transactionItems->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseOrderTransaction->transactionItems->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['total'] = -10000;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    $this->assertEquals(28, intval($purchaseOrderTransaction->transactionItems->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = $gst->id;
        $purchaseOrderInput['items'][$key]['discount_type'] = 2;
        $purchaseOrderInput['items'][$key]['discount_value'] = 10;
        $purchaseOrderInput['items'][$key]['discount_type_2'] = 2;
        $purchaseOrderInput['items'][$key]['discount_value_2'] = 10;
        $purchaseOrderInput['items'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($response->json('data.purchase_order_transaction.id'))->first();
    foreach ($purchaseOrderTransaction->transactionItems as $item) {
        $this->assertEquals(18.1, $item->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['total'] = -10000;
    $purchaseOrderInput['items'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['items'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(78.13, $purchaseOrderTransaction->transactionItems->first()->total);
});

test('validation with update negative and wrong sub total for update purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['items'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionItems')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(100, intval($purchaseOrderTransaction->transactionItems->first()->total));
});

test('validation with update negative and wrong total for update purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['total'] = -620;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseOrderInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertNotEquals(10.00, $purchaseOrderTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(10, intval($purchaseOrderTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(67.04, $purchaseOrderTransaction->cgst);
    $this->assertEquals(67.04, $purchaseOrderTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for update purchase order transaction item invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseOrderInput['items'] as $key => $item) {
        $purchaseOrderInput['items'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(134.08, $purchaseOrderTransaction->igst);
});

test('validation with update negative and wrong taxable value for update purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(10, intval($purchaseOrderTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update purchase order transaction item invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make()->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ITEM_INVOICE,
        'grand_total' => 620.00,
    ]);
});

/*
|----------------------------------------------------------------------------------------------------------------------------------------------------
| Update Purchase Order Transaction Account Invoice Backend Validation with update Test Case
|----------------------------------------------------------------------------------------------------------------------------------------------------
*/

test('validation with update decimal rpu amount when decimal place is 1 for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['decimal'] = 1;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(78.2, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 2 for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['decimal'] = 2;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(78.22, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 3 for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['decimal'] = 3;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(78.220, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 4 for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['decimal'] = 4;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(78.2197, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount when decimal place is 5 for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['decimal'] = 5;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(78.21970, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 1 for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['decimal'] = 1;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(128.2, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 2 for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['decimal'] = 2;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(128.16, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 3 for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['decimal'] = 3;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(128.155, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 4 for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['decimal'] = 4;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(128.1552, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update decimal rpu amount without tax when decimal place is 5 for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['decimal'] = 5;
    $purchaseOrderInput['ledgers'][0]['rpu'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = 100.********;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;
    $purchaseOrderInput['ledgers'][0]['with_tax'] = false;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['purchase_order_transaction']['id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['purchase_order_transaction']['id'])->first();
    $this->assertEquals(128.15515, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong rpu without or with gst for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['rpu_without_gst'] = -10000;
    $purchaseOrderInput['ledgers'][0]['rpu_with_gst'] = -10000;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = 2;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['transaction_id'])->first();
    $this->assertNotEquals(100, $purchaseOrderTransaction->transactionLedgers->first()->rpu_without_gst);
    $this->assertEquals(100.0, $purchaseOrderTransaction->transactionLedgers->first()->rpu_with_gst);
});

test('validation with update negative and wrong gst tax percentage for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['total'] = -10000;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['transaction_id'])->first();
    $this->assertEquals(28, intval($purchaseOrderTransaction->transactionLedgers->first()->gst_tax_percentage));
});

test('validation with update negative and wrong total discount for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(5.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    foreach ($purchaseOrderInput['ledgers'] as $key => $ledger) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = $gst->id;
        $purchaseOrderInput['ledgers'][$key]['discount_type'] = 2;
        $purchaseOrderInput['ledgers'][$key]['discount_value'] = 10;
        $purchaseOrderInput['ledgers'][$key]['discount_type_2'] = 2;
        $purchaseOrderInput['ledgers'][$key]['discount_value_2'] = 10;
        $purchaseOrderInput['ledgers'][$key]['total_discount_amount'] = 10;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['transaction_id'])->first();
    foreach ($purchaseOrderTransaction->transactionLedgers as $ledger) {
        $this->assertEquals(18.1, $ledger->total_discount_amount);
    }
});

test('validation with update negative and wrong sub total for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['total'] = -10000;
    $purchaseOrderInput['ledgers'][0]['gst_id'] = $gst->id;
    $purchaseOrderInput['ledgers'][0]['gst_tax_percentage'] = $gst->tax_rate;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['transaction_id'])->first();
    $this->assertEquals(78.13, $purchaseOrderTransaction->transactionLedgers->first()->total);
});

test('validation with update negative and wrong sub total for update purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make(['order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['ledgers'][0]['total'] = -10000;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('transactionLedgers')->whereId($purchaseOrderData['transaction_id'])->first();
    $this->assertEquals(100, intval($purchaseOrderTransaction->transactionLedgers->first()->total));
});

test('validation with update negative and wrong total for update purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['total'] = 620;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'total' => 620.00,
    ]);
});

test('validation with update negative and wrong gross value for update purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['gross_value'] = -100;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
});

test('validation with update negative and wrong additional charges gst tax percentage for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    $purchaseOrderInput['additional_charges'][0]['ac_total'] = -2000;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($purchaseOrderData['transaction_id'])->first();
    $this->assertNotEquals(10.00, $purchaseOrderTransaction->additionalCharges->first()->total);
});

test('validation with update negative and wrong additional charges total for update purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make(['order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['additional_charges'][0]['ac_total'] = -200;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($purchaseOrderData['transaction_id'])->first();
    $this->assertEquals(10, intval($purchaseOrderTransaction->additionalCharges->first()->total));
});

test('validation with update negative and wrong cgst and sgst when purchase intra state taxable classification for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Intrastate Purchase Taxable',
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseOrderInput['ledgers'] as $key => $ledger) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::whereId($purchaseOrderData['transaction_id'])->first();
    $this->assertEquals(67.04, $purchaseOrderTransaction->cgst);
    $this->assertEquals(67.04, $purchaseOrderTransaction->sgst);
});

test('validation with update negative and wrong igst when purchase inter state taxable classification for update purchase order transaction account invoice in gst company', function () {
    //arrange
    setGstCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true, 'app.is_validation_testing' => false]);
    $gst = GstTax::whereTaxRate(28.00)->first();

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'is_gst_enabled' => true,
        'is_cgst_sgst_igst_calculated' => 1,
        'main_classification_nature_type' => 'Interstate Purchase Taxable',
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['additional_charges'][0]['ac_gst_rate_id'] = $gst->id;
    foreach ($purchaseOrderInput['ledgers'] as $key => $ledger) {
        $purchaseOrderInput['ledgers'][$key]['gst_id'] = $gst->id;
    }

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::whereId($purchaseOrderData['transaction_id'])->first();
    $this->assertEquals(134.08, $purchaseOrderTransaction->igst);
});

test('validation with update negative and wrong taxable value for update purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['taxable_value'] = -100;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'taxable_value' => 610.00,
    ]);
});

test('validation with update negative and wrong addless total for update purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['add_less'][0]['al_total'] = -100;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'gross_value' => 600.00,
    ]);
    $purchaseOrderTransaction = PurchaseOrderTransaction::with('additionalCharges')->whereId($purchaseOrderData['transaction_id'])->first();
    $this->assertEquals(10, intval($purchaseOrderTransaction->addLess->first()->total));
});

test('validation with update negative and wrong grand total for update purchase order transaction account invoice', function () {
    //arrange
    setCompany(1);
    config(['app.is_check_validation' => true, 'app.is_update_value_with_validation' => true]);

    $purchaseOrderInput = PurchaseOrderTransactionFactory::new()->make([
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
    ])->toArray();
    $purchaseOrderData = StorePurchaseOrderTransactionAction::run($purchaseOrderInput);
    $purchaseOrderInput['grand_total'] = -100;

    //act
    $response = $this->postJson(route('purchase-order-transaction-update', $purchaseOrderData['transaction_id']), $purchaseOrderInput);

    //assert
    $response->assertStatus(200)
        ->assertJson([
            'success' => true,
            'message' => 'Purchase Order Transaction Updated Successfully.',
        ]);
    $this->assertDatabaseHas('purchase_order_transactions', [
        'id' => $purchaseOrderData['transaction_id'],
        'company_id' => 1,
        'order_number' => $purchaseOrderInput['order_number'],
        'order_type' => PurchaseOrderTransaction::ACCOUNTING_INVOICE,
        'grand_total' => 620.00,
    ]);
});
