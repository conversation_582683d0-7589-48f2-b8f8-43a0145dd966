import axios from "axios";
import { use<PERSON>allback, useContext, useEffect, useRef, useState } from "react";
import { Card, Col, Container, Form, Row, Tab, Tabs } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useReactToPrint } from "react-to-print";
import barCodeimg1 from "../../assets/images/barcode-img-1.png";
import preview from "../../assets/images/preview.svg";
import MultiReactSelect from "../../components/ui/MultiReactSelect";
import ReactSelect from "../../components/ui/ReactSelect";
import Toast from "../../components/ui/Toast";
import { toastType } from "../../constants";
import { StateContext } from "../../context/StateContext";
import CustomHelmet from "../../shared/helmet";
import Loader from "../../shared/loader";
import { errorToast } from "../../store/actions/toastAction";
import { getBarcodeItemdetails, getBarcodeItemGroupList, getBarcodeItemList, getBarcodeLabelSize, getBarcodeSettingDetails } from "../../store/print-barcode/printBarcodeSlice";
import PrintBarcodeModal from "../modal/print-barcode/PrintBarcodeModal";
import PrintBarcodeTable from "./table/PrintBarcodeTable";
import CustomBarcodeSize from "./CustomBarcodeSize";

const printerOptions = [
    { label: "Regular Printer", value: "regular_printer" },
    { label: "Label Printer", value: "label_printer" },
];

const regularSizeOptions = [
    { label: "A4 (65 Labels)", value: 1, labelView: 65, type: 1 },
    { label: "A4 (48 Label)", value: 2, labelView: 48, type: 2 },
    { label: "A4 (24 Labels)", value: 3, labelView: 24, type: 3 },
];

const sizeOptions = [
    { label: "2 : 1 (2 Labels)", value: 1, labelView: 2, type: 4, width: '110mm', height: '30mm' },
    { label: "2 : 1 (1 Label)", value: 2, labelView: 1, type: 5, width: '55mm', height: '30mm' },
    { label: "1.5 : 1 (2 Labels)", value: 3, labelView: 2, type: 6, width: '84mm', height: '30mm' },
    { label: "1.5 : 1 (1 Labels)", value: 4, labelView: 1, type: 7, width: '45mm', height: '30mm' },
    { label: "2.5 : 1.5 (1 Labels)", value: 5, labelView: 1, type: 8, width: '66mm', height: '40mm' },
    { label: "3 : 1 (1 Labels)", value: 6, labelView: 1, type: 9, width: '80mm', height: '30mm' },
    { label: "2.5 : 1 (1 Labels)", value: 7, labelView: 1, type: 10, width: '65mm', height: '25mm' },
];

const labelSizes = {
    1: { width: '110mm', height: '30mm' },      // 2x1 (2 Labels)
    2: { width: '55mm', height: '30mm' },       // 2x1 (1 Label)
    3: { width: '84mm', height: '30mm' },     // 1.5x1 (2 Labels)
    4: { width: '45mm', height: '30mm' },     // 1.5x1 (1 Label)
    5: { width: '66mm', height: '40mm' },     // 2.5x1.5 (1 Label)
    6: { width: '80mm', height: '30mm' },     // 3x1 (1 Label)
    7: { width: '65mm', height: '25mm' },     // 60mm x 25mm (1 Label)
};

const itemSelectionOptions = [
    { id: "all_items", label: "All Items", value: 1 },
    { id: "groups_wise_item_list", label: "Groups Wise Item List", value: 2 },
    { id: "individual_items", label: "Individual Items", value: 3 },
    { id: "groups_individual", label: "Groups Wise Item List + Individual Items", value: 4 },
];

const PrintBarcode = () => {
    const [formState, setFormState] = useState({
        selectedPrinter: printerOptions[0].value,
        selectedSize: regularSizeOptions[0].value,
        selectedItemType: 2,
        selectedItemGroups: [],
        selectedItemIndividual: [],
    });
    const [tableData, setTableData] = useState([]);
    const [itemGroupsList, setitemGroupsList] = useState([]);
    const [itemIndividualList, setitemIndividualList] = useState([]);
    const [showModal, setShowModal] = useState(false);
    const [isIndividual, setIsIndividual] = useState(true);
    const [loading, setLoading] = useState(false);
    const [printLoading, setPrintLoading] = useState(false);
    const [isBarcodeLoading, setIsBarcodeLoading] = useState(false);
    const [isCustomBarcodeSize, setIsCustomBarcodeSize] = useState(false);
    const [LabelSizeOptions, setLabelSizeOptions] = useState([]);
    const [allNumberOfLabel, setAllNumberOfLabel] = useState(1);
    const { barcodeSize } = useContext(StateContext);
    const dispatch = useDispatch();
    const { printBarcode } = useSelector(state => state);

    const LabelBarcodeDetails = LabelSizeOptions?.find(item => formState.selectedPrinter === "label_printer" && item?.value === formState?.selectedSize);

    const selectedSizeOption = formState.selectedPrinter === "regular_printer"
        ? regularSizeOptions.find(opt => opt.value === formState?.selectedSize)
        : LabelSizeOptions.find(opt => opt.value === formState?.selectedSize);

    const labelView = selectedSizeOption ? selectedSizeOption.labelView : 1;

    let width, height;
    if (formState.selectedPrinter === "regular_printer") {
        width = '210mm';
        height = '297mm'; // fallback height
    } else {
        // width = `${(LabelBarcodeDetails?.width * LabelBarcodeDetails?.barcodes_per_row) + (11 + (5 * (labelView - 1)))}mm`;
        width = `${(LabelBarcodeDetails?.width * LabelBarcodeDetails?.barcodes_per_row) + ((barcodeSize?.pageMarginLeft + barcodeSize?.pageMarginRight) + (barcodeSize?.barcodeBetweenColumns * (labelView - 1)))}mm`;
        // height = `${(LabelBarcodeDetails?.height) + 11}mm`;
        height = `${(LabelBarcodeDetails?.height) + (barcodeSize?.pageMarginTop + barcodeSize?.pageMarginBottom)}mm`;
    }

    useEffect(() => {
        if (printBarcode.customBarcodeSize?.length > 0 && formState.selectedPrinter === "label_printer") {
            const sizeOptionsData = printBarcode.customBarcodeSize.map((item) => {
                return {
                    ...item,
                    label: `${item.width} : ${item.height} (${item.barcodes_per_row} Labels)`,
                    value: item.id,
                    labelView: item.barcodes_per_row,
                    type: item.id,
                    isCustomBarcodeSize: true
                };
            });

            setLabelSizeOptions(sizeOptionsData);

        } else if (formState.selectedPrinter === "label_printer") {
            setLabelSizeOptions([]);
        }
    }, [printBarcode.customBarcodeSize, formState.selectedPrinter]);

    const isTwoLabel = formState.selectedPrinter === "label_printer" && LabelSizeOptions?.find(item => item?.value === formState?.selectedSize)

    const [headerCheckboxes, setHeaderCheckboxes] = useState({
        sku: true,
        header: true,
        line1: true,
        line2: true,
        line3: true,
        line4: true,
    });

    const [headerSelectValues, setHeaderSelectValues] = useState({
        header: { value: "", label: "Select Field" },
        line1: { value: "", label: "Select Field" },
        line2: { value: "", label: "Select Field" },
        line3: { value: "", label: "Select Field" },
        line4: { value: "", label: "Select Field" },
    });

    const handleChange = async (key, selected) => {
        // setFormState(prevState => ({ ...prevState, [key]: value }));
        setIsIndividual(true);

        if (key === "selectedItemGroups" || key === "selectedItemIndividual") {
            const previousSelectedItems = formState[key];

            const removedItems = previousSelectedItems?.filter(
                item => !selected.some(newItem => newItem.value === item.value)
            );

            if (removedItems?.length > 0 && key === "selectedItemGroups") {
                const updatedData = tableData?.filter(item =>
                    !removedItems.some(removed => removed.items_id.includes(item.id))
                );
                setIsIndividual(false);
                setTableData(updatedData);
            } else if (removedItems?.length > 0 && key === "selectedItemIndividual") {
                const updatedData = tableData?.filter(item => item.id !== removedItems[0]?.id)
                setIsIndividual(false);
                setTableData(updatedData);
            }
        }

        setFormState(prevState => {
            let updatedState = { ...prevState, [key]: selected };

            if (key === "selectedItemGroups") {
                const previousSelectedItems = formState[key];
                const removedItems = previousSelectedItems?.filter(
                    item => !selected.some(newItem => newItem.value === item.value)
                );
                const previousGroups = formState.selectedItemGroups || [];

                const selectedItemIds = selected.flatMap(group => group.items_id);

                const selectedIndividuals = itemIndividualList.filter(item =>
                    selectedItemIds.includes(item.value)
                );

                if (removedItems?.length > 0) {
                    const updateItems = updatedState.selectedItemIndividual?.filter(item => selectedIndividuals?.some(selected => selected?.id === item?.id));
                    updatedState.selectedItemIndividual = updateItems;
                } else {

                    const addedGroups = selected.filter(
                        group => !previousGroups.some(prev => prev.value === group.value)
                    );

                    const selectedItemIds = addedGroups.flatMap(group => group.items_id);

                    const selectedIndividuals = itemIndividualList.filter(item =>
                        selectedItemIds.includes(item.value)
                    );

                    const existingIndividuals = updatedState.selectedItemIndividual || [];

                    // const combinedIndividuals = [
                    //     ...existingIndividuals,
                    //     ...selectedIndividuals
                    // ]
                    const combinedIndividualsMap = new Map();

                    [...existingIndividuals, ...selectedIndividuals].forEach(item => {
                        combinedIndividualsMap.set(item.value, item);
                    });

                    const combinedIndividuals = Array.from(combinedIndividualsMap.values());

                    updatedState.selectedItemIndividual = combinedIndividuals;
                }

            } else if (key === "selectedItemIndividual") {
                const previousSelectedItems = formState[key];
                const removedItems = previousSelectedItems?.filter(
                    item => !selected.some(newItem => newItem.value === item.value)
                );

                if (removedItems?.length > 0) {
                    let updatedGroups = updatedState.selectedItemGroups?.filter(group => {
                        const remainingItems = group.items_id.filter(id =>
                            selected.some(item => item.value === id)
                        );
                        return remainingItems.length > 0;
                    });

                    updatedState.selectedItemGroups = updatedGroups;

                }

            }
            return updatedState;
        });

        if (key === "selectedItemType") {

        setHeaderSelectValues({
            header: { value: "", label: "Select Field" },
            line1: { value: "", label: "Select Field" },
            line2: { value: "", label: "Select Field" },
            line3: { value: "", label: "Select Field" },
            line4: { value: "", label: "Select Field" },
        })
        setAllNumberOfLabel(1);
            setFormState((prevState) => ({
                ...prevState,
                selectedItemGroups: [],
                selectedItemIndividual: [],
            }));
        };

        if (key === "selectedPrinter") {
            setFormState((prevState) => ({
                ...prevState,
                selectedSize: 1
            }));
        }

    };

    useEffect(() => {
        ; (async () => {
            const lastGroupId = formState?.selectedItemGroups?.length > 0
                ? formState.selectedItemGroups[formState.selectedItemGroups.length - 1]
                : null;

            const lastItemId = formState?.selectedItemIndividual?.length > 0
                ? formState.selectedItemIndividual[formState.selectedItemIndividual.length - 1]
                : null;

            const Params = {
                type: formState?.selectedItemType,
                ...(lastGroupId && (formState.selectedItemType === 2 || formState.selectedItemType === 4) && { group_id: lastGroupId?.id }),
                ...(lastItemId && (formState.selectedItemType === 3 || formState.selectedItemType === 4) && { item_id: lastItemId?.id })
            };

            if (formState.selectedItemType === 2 && !lastGroupId) {
                setTableData([]);
            } else if (formState.selectedItemType === 3 && !lastItemId) {
                setTableData([]);
            } else if (formState.selectedItemType === 4 && !lastGroupId && !lastItemId) {
                setTableData([]);
            }

            if ((formState.selectedItemType === 1 || lastGroupId || lastItemId) && isIndividual) {
                setIsBarcodeLoading(true);
                const response = await dispatch(getBarcodeItemdetails(Params));

                if (response?.success) {
                    setIsBarcodeLoading(false);
                    if (response?.data?.length > 0) {
                        const newData = response?.data?.filter((item) => {
                            return !tableData.some((existingItem) => existingItem.id === item.id);
                        });

                        // const updateData = newData?.map((item) => ({
                        //     ...item,
                        //     labels: 1,
                        //     header: "",
                        //     line1: "",
                        //     line2: "",
                        //     line3: "",
                        //     line4: "",
                        // }))

                        const updateData = newData?.map((item) => {
                            const updatedItem = {
                                ...item,
                                labels: allNumberOfLabel || 1,
                                header: headerSelectValues?.header?.value || "",
                                line1: headerSelectValues?.line1?.value || "",
                                line2: headerSelectValues?.line2?.value || "",
                                line3: headerSelectValues?.line3?.value || "",
                                line4: headerSelectValues?.line4?.value || "",
                            };

                            // Handle custom_text fields dynamically
                            if (updatedItem.header === "custom_text") {
                                updatedItem.custom_text_header = headerSelectValues?.custom_header || "";
                            }
                            if (updatedItem.line1 === "custom_text") {
                                updatedItem.custom_text_line1 = headerSelectValues?.custom_line1 || "";
                            }
                            if (updatedItem.line2 === "custom_text") {
                                updatedItem.custom_text_line2 = headerSelectValues?.custom_line2 || "";
                            }
                            if (updatedItem.line3 === "custom_text") {
                                updatedItem.custom_text_line3 = headerSelectValues?.custom_line3 || "";
                            }
                            if (updatedItem.line4 === "custom_text") {
                                updatedItem.custom_text_line4 = headerSelectValues?.custom_line4 || "";
                            }

                            return updatedItem;
                        });
                        setTableData((prevData) => [...prevData, ...updateData]);
                    }

                }
            }

        })()
    }, [formState?.selectedItemGroups, formState?.selectedItemIndividual, formState?.selectedItemType])

    const handleShow = async () => {
        setShowModal(true);
        const selectedBarcodeSize = formState?.selectedPrinter === "regular_printer" ? regularSizeOptions.find(item => item?.value === formState?.selectedSize) : { type: 11 };

        if (selectedBarcodeSize.type) {
            dispatch(getBarcodeSettingDetails(selectedBarcodeSize.type));
        }
    }

    const handleClose = () => setShowModal(false);

    useEffect(() => {
        document.getElementById("showName").innerHTML = "Print Details";
        dispatch(getBarcodeLabelSize());
    }, []);

    useEffect(() => {
        ; (async () => {
            const itemsList = await dispatch(getBarcodeItemList());

            if (itemsList?.success) {
                const itemData = itemsList?.data?.barcode_items?.length > 0 ? itemsList?.data?.barcode_items?.map((item) => ({
                    ...item,
                    label: item?.name,
                    value: item?.id
                })) : []
                setitemIndividualList(itemData);
            }

            const itemGroups = await dispatch(getBarcodeItemGroupList());

            if (itemGroups?.success) {
                const data = itemGroups?.data?.barcode_item_groups?.length > 0 ? itemGroups?.data?.barcode_item_groups?.map((item) => ({
                    ...item,
                    label: item?.name,
                    value: item?.id
                })) : []
                setitemGroupsList(data);
            }
        })()
    }, []);

    // const selectedSizeOption = formState.selectedPrinter === "regular_printer"
    //     ? regularSizeOptions.find(opt => opt.value === formState?.selectedSize)
    //     : LabelSizeOptions.find(opt => opt.value === formState?.selectedSize);

    // const labelView = selectedSizeOption ? selectedSizeOption.labelView : 1;

    const printRef = useRef();

    const handleDownload = async () => {
        setLoading(true);
        document.getElementById('downloadBtn').disabled = true;

        try {
            if (printRef.current) {
                const cssStyles = `
               <style>
/* Responsive adjustments */


/* Modal Styling */
.printview-modal .modal-header {
    padding: 15px 32px;
    box-shadow: 0px 0px 30px 0px #00000040;
    z-index: 2;
}

.printview-modal .barcode-card {
    min-width: 207px !important;
}
    
.w-fit-content {
    width: fit-content;
    min-width: fit-content;
}

.printview-modal .modal-content {
    border-radius: 0 !important;
    height: 85vh;
}

.printview-modal .modal-body {
    padding: 47px 20px 20px 20px;
}

.printview-modal .modal-header p {
    font-size: 20px;
}

/* Utility Classes */
.barcode-print-border {
    border: 1px solid #9c9c9c;
    border-radius: 6px;
}

.barcode-card-print,
.barcode-card-print-without-border {
    padding: 1px 1px;
    border: none !important;
    border-radius: 6px;
}

.lh-1 { line-height: 1 !important; }
.mb-0 { margin-bottom: 0px !important; }
.mb-1 { margin-bottom: 0.25rem !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }
.d-flex { display: flex !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-start { align-items: flex-start !important; }

.text-black {
    color: black !important;
}

*:not(i) {
    font-family: Inter, sans-serif !important;
}

/* Barcode Content */
.barcode-content, .barcode-content-print {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.barcode-content {
    justify-content: center;
}

.barcode-content .barcode-number,
.barcode-content-print .barcode-number {
    letter-spacing: 0.3px;
}

.barcode_image { width: 100%; }

.barcode_header {
    font-size: 12px;
    font-weight: 700;
    max-width: 90%;
    white-space: wrap;
    text-wrap: wrap;
}

.fw-7 { font-weight: 700 !important; }

.content-box {
    border-radius: 7px;
    box-shadow: 0 0 5px 0 #00000040;
    background-color: white;
}

.barcode_sku {
    font-size: 14px;
    font-weight: 500;
    max-width: 90%;
    white-space: nowrap;
    text-emphasis: ellipsis;
    margin-bottom: 0px !important;
}

/* Font Sizes for Labels */
.barcode-img-2 {
    width: 157px;
    height: 23px;
}

.barcodeSize48 { font-size: 12px; }

.barcodeHeader64, .barcodeHeader48, .barcodesku24,
.barcodeline1_48, .barcodeline2_48, .barcodeline4_48 {
    font-size: 10px;
}

.barcodeHeader24, .barcodeline1_24, .barcodeline2_24, .barcodeline4_24 {
    font-size: 12px;
}

.barcodeHeader-label,
.barcodeline4_label {
    font-size: 12px;
    margin-bottom: 2px !important;
}

.barcodesku-label {
    font-size: 12px;
    margin-bottom: 1px !important;
}

.barcode-image-height {
    min-height: 20px !important;
    max-height: 30px !important;
    margin-bottom: 2px !important;
}

.barcodeline1_label,
.barcodeline2_label,
.barcodeline4_label,
.barcodesku-label34 {
    font-size: 9px;
    margin-bottom: 0px !important;
}
p {
    margin-top: 0;
    margin-bottom: 1rem;
}

.barcodesku64, .barcodesku48 { font-size: 7px; }
.barcodeline1_64, .barcodeline2_64, .barcodeline4_64 { font-size: 8px; }

/* Height Utility */
.print-details-container .h-16px,
.barcode-content-print .h-16px { height: 16px !important; }
.print-details-container .h-18px,
.barcode-content-print .h-18px { height: 18px !important; }
.print-details-container .h-20px,
.barcode-content-print .h-20px { height: 20px !important; }
.print-details-container .h-22px,
.barcode-content-print .h-22px { height: 22px !important; }
.print-details-container .h-25px,
.barcode-content-print .h-25px { height: 25px !important; }

.dynamic-description-header { font-weight: 600 !important; }

/* Pagination & Tabs */
.pagination-container-Recurring {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    padding: 10px 0;
    border-radius: 8px;
}

.recurring-tabs .nav-item .nav-link {
    font-size: 14px;
    font-weight: 600;
    padding: 8px 15px !important;
}

body {
    display: block;
    margin-top: 0px !important;
}

/* Barcode Layouts */
.print-barcode-container {
    display: flex;
    flex-direction: column;
    // align-items: center;
    // justify-content: center;
    padding: 0px 2px 0;
    margin-left: ${barcodeSize?.pageMarginLeft}mm !important;
    margin-right: ${barcodeSize?.pageMarginRight}mm !important;
}

.barcodeContainer64 {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-column-gap: 5px;
    grid-row-gap: 6px !important;
    grid-column-gap: ${barcodeSize?.barcodeBetweenColumns}mm;
    grid-row-gap: ${barcodeSize?.barcodeBetweenRows}mm !important;
    width: fit-content
}

.barcodeContainer48 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-column-gap: ${barcodeSize?.barcodeBetweenColumns}mm;
    grid-row-gap: ${barcodeSize?.barcodeBetweenRows}mm !important;
    width: fit-content
}

.barcodeContainer24 {
    grid-template-columns: repeat(3, 1fr);
    grid-row-gap: ${barcodeSize?.barcodeBetweenRows}mm !important;
    display: grid;
    grid-column-gap: ${barcodeSize?.barcodeBetweenColumns}mm;
    width: fit-content
}

/* Label Layouts */
.barcodeContainerlabel13,
.barcodeContainerlabel13Print,
.barcodeContainerlabel24,
.barcodeContainerlabel24Print,
.barcodeContainerlabel5,
.barcodeContainerlabel5Print {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-column-gap: ${barcodeSize?.barcodeBetweenRows}mm;
    width: fit-content
}

.barcodeContainerlabel13 {
    grid-template-columns: repeat(2, 1fr);
    grid-row-gap: ${barcodeSize?.barcodeBetweenRows}mm !important;
    width: fit-content
}

.barcodeContainerlabel13Print {
    grid-template-columns: repeat(2, 1fr);
    grid-row-gap: ${barcodeSize?.barcodeBetweenRows}mm !important;
    margin-top: 2px;
    width: fit-content
}

.barcodeContainerlabel24 {
    grid-row-gap: ${barcodeSize?.barcodeBetweenRows}mm !important;
    width: fit-content
}

.barcodeContainerlabel24Print {
    grid-row-gap: ${barcodeSize?.barcodeBetweenRows}mm !important;
    margin-top: 2px;
    width: fit-content
}

.barcodeContainerlabel5 {
    grid-row-gap: ${barcodeSize?.barcodeBetweenRows}mm !important;
    width: fit-content
}

.barcodeContainerlabel5Print {
    grid-row-gap: ${barcodeSize?.barcodeBetweenRows}mm !important;
    margin-top: 2px;
    width: fit-content
}

.barcodeNotExist {
    padding: 6rem 0;
}

.barcode-details {
    max-height: 620px;
}

.barcode-details .label-details-table thead th {
    position: sticky;
    top: 0;
    z-index: 9;
}

.barcode_lines { gap: 1px; }
.print-gap-5 { row-gap: 20px; }

.barcodeBreak { page-break-after: always; }

@page{
    margin-top: ${barcodeSize.pageMarginTop}mm;
    margin-bottom: ${barcodeSize.pageMarginBottom}mm;
}

 }
}

@media screen {
    html, body {
     margin: 0;
     padding: 0;
     width: ${width};
     height: ${height};
     overflow: hidden;
    }
  }

@media print {

    html, body {
    //   height:100vh;
      margin: 0 !important;
      padding: 0 !important;
      overflow: hidden;
    }

}


</style>
`;

                const fullHTML = `
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    ${cssStyles}
                </head>
                <body style="width: ${width}; margin: 0 0;">
                           <div class="h-100 overflow-auto">
                                <div class="print-barcode-container" >
                    ${printRef.current.innerHTML}
                    </div>
                    </div>
                </body>
                </html>
            `;

                const response = await axios.post(
                    '/react-api/download-barcode-pdf',
                    {
                        html: fullHTML,
                        options: {
                            displayHeaderFooter: false,
                            printBackground: true,
                            format: formState.selectedPrinter === "regular_printer" ? 1 : LabelBarcodeDetails?.id,
                            width,
                            height
                        },
                    },
                    {
                        responseType: 'blob',
                        headers: { 'Content-Type': 'application/json' },
                    }
                );

                const blob = new Blob([response.data], { type: 'application/pdf' });
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = 'barcode.pdf';
                link.click();
            }
        } catch (error) {
            dispatch(
                errorToast({
                    text: "Something went wrong please try again after some time.",
                    type: toastType.ERROR,
                })
            );
        } finally {
            setLoading(false);
            document.getElementById('downloadBtn').disabled = false;
        }
    };


    const handleAfterPrint = useCallback(() => {
        setPrintLoading(false)
    }, [])

    const handleBeforePrint = useCallback(() => {
        setPrintLoading(true)
        return Promise.resolve();
    }, [])

    const handlePrint = useReactToPrint({
        contentRef: printRef,
        onAfterPrint: handleAfterPrint,
        onBeforePrint: handleBeforePrint,
    });

    const handleCustomClose = () => {
        setIsCustomBarcodeSize(false);
    }

    return (
        <>
            <CustomHelmet
                title={"Print Details"}
            />
            {isBarcodeLoading && <Loader />}
            <div className="position-relative print-details">
                <Card className="bg-transparent">
                    <Tabs
                        defaultActiveKey="PrintDetails"
                        id="Print-details-tabs"
                        className="printdetails-tab flex-nowrap mx-3"
                    >
                        <Tab eventKey="PrintDetails" title="Print Details" className="fw-medium">
                            <div fluid className="print-details-container py-6 px-lg-10 px-sm-8 px-6 bg-white scroll-color position-relative">
                                <div className="print-gap-5 d-flex flex-wrap gap-4 position-relative justify-content-between">
                                    <div>
                                        <div className="mb-4 d-flex align-content-center flex-column gap-4">
                                            <Form.Group>
                                                <div className="focus-shadow cursor-pointer w-100 max-w-285px" style={{ cursor: "pointer" }}>
                                                    <ReactSelect
                                                        options={printerOptions}
                                                        placeholder="Printer"
                                                        defaultLabel="Select Printer"
                                                        islabel={true}
                                                        value={formState.selectedPrinter}
                                                        onChange={option => handleChange("selectedPrinter", option.value)}
                                                        className="border-0 cursor-pointer"
                                                        isMulti={false}
                                                    />
                                                </div>
                                            </Form.Group>
                                            {/* <Form.Group className="w-100 max-w-285px">
                                                    <div className="focus-shadow cursor-pointer w-100 max-w-285px" style={{ cursor: "pointer" }}>
                                                        <ReactSelect
                                                            options={formState.selectedPrinter === "regular_printer" ? regularSizeOptions : LabelSizeOptions}
                                                            placeholder="Printing Size"
                                                            islabel={true}
                                                            value={formState?.selectedSize}
                                                            onChange={option => handleChange("selectedSize", option.value)}
                                                            className="border-0 cursor-pointer"
                                                            isMulti={false}
                                                        />
                                                    </div>
                                                </Form.Group> */}

                                            <Form.Group>
                                                <div className="input-group flex-nowrap max-w-285px">
                                                    <div className={`position-relative h-40px w-100 focus-shadow ${formState.selectedPrinter === "label_printer" ? 'pe-36px' : ""}`}>
                                                        <ReactSelect
                                                            options={formState.selectedPrinter === "regular_printer" ? regularSizeOptions : LabelSizeOptions}
                                                            placeholder="Printing Size"
                                                            islabel={true}
                                                            value={formState?.selectedSize}
                                                            onChange={option => handleChange("selectedSize", option.value)}
                                                            className="border-0 cursor-pointer"
                                                            isMulti={false}
                                                        />
                                                    </div>
                                                    {formState.selectedPrinter === "label_printer" ? <button
                                                        type="button"
                                                        className="input-group-text custom-group-text"
                                                        onClick={() => setIsCustomBarcodeSize(true)}
                                                    >
                                                        <i className="fas fa-plus text-gray-900"></i>
                                                    </button> : null}
                                                </div>
                                            </Form.Group>
                                        </div>
                                        <div>
                                            <p className="fs-14 fw-5 mb-0">Select Items:</p>
                                            <p className="fs-14 fw-5 mb-0 text-secondary">(Show Items)</p>
                                            <div className="d-flex gap-3 align-items-center mb-4 flex-wrap">
                                                {itemSelectionOptions.map(option => (
                                                    <Form.Check
                                                        key={option.id}
                                                        type="radio"
                                                        id={option.id}
                                                        name="itemSelection"
                                                        label={option.label}
                                                        value={option.value}
                                                        checked={formState.selectedItemType === option.value}
                                                        onChange={() => handleChange("selectedItemType", option.value)}
                                                    />
                                                ))}
                                            </div>
                                            <div className="d-flex flex-column gap-4 w-100">
                                                {(formState.selectedItemType === 2 || formState.selectedItemType === 4) && <Form.Group>
                                                    <div className="input-group flex-nowrap">
                                                        <div className="position-relative  w-100 focus-shadow multi-custome">
                                                            <MultiReactSelect
                                                                options={itemGroupsList}
                                                                value={formState.selectedItemGroups}
                                                                onChange={selected => handleChange("selectedItemGroups", selected)}
                                                                placeholder='Select Items Groups'
                                                            />
                                                        </div>
                                                    </div>
                                                </Form.Group>}
                                                {(formState.selectedItemType === 3 || formState.selectedItemType === 4) && <Form.Group>
                                                    <div className="input-group flex-nowrap">
                                                        <div className="position-relative  w-100 focus-shadow multi-custome">
                                                            <MultiReactSelect
                                                                options={itemIndividualList}
                                                                value={formState.selectedItemIndividual}
                                                                onChange={selected => handleChange("selectedItemIndividual", selected)}
                                                                placeholder='Select Items Individual'
                                                            />
                                                        </div>
                                                    </div>
                                                </Form.Group>}

                                            </div>
                                        </div>
                                    </div>
                                    {/* Print Barcode preview section */}
                                    <div className="overflow-auto">
                                        <div className="barcode-section ms-lg-auto position-relative">
                                            <p className="fs-18 fw-5 mb-2 text-primary text-center">Preview</p>
                                            <div className="d-flex flex-wrap flex-lg-nowrap gap-1 barcode-box">
                                                <div className="position-relative">
                                                    <div className="barcode-details-heading d-flex align-items-center gap-1">
                                                        <div className="barcode-height-span">
                                                            {formState.selectedPrinter === "regular_printer" ? (
                                                                labelView === 65
                                                                    ? "21 mm"
                                                                    : labelView === 48
                                                                        ? "23 mm"
                                                                        : labelView === 24
                                                                            ? "34 mm"
                                                                            : LabelBarcodeDetails?.height
                                                                                ? `${LabelBarcodeDetails.height} mm`
                                                                                : ""
                                                            ) : LabelBarcodeDetails?.height ? `${LabelBarcodeDetails.height} mm` : ""}
                                                        </div>
                                                        <div className="barcode-left-line"></div>
                                                        <div className={`${formState.selectedPrinter === "regular_printer"
                                                            ? labelView === 65
                                                                ? "barcode-length65 barcode-lengthMM"
                                                                : labelView === 48
                                                                    ? "barcode-length48 barcodeHeaderPDF48 barcode-lengthMM"
                                                                    : "barcode-length24 barcode-lengthMM"
                                                            : "barcode_length-other barcode-length"} barcode-card position-relative`}

                                                            style={{
                                                                ...(formState.selectedPrinter === "label_printer"
                                                                    ? {
                                                                        width: `${LabelBarcodeDetails?.width}mm`,
                                                                        height: `${LabelBarcodeDetails?.height}mm`,
                                                                    }
                                                                    : {}),
                                                                display: "flex",
                                                                justifyContent: "center",
                                                                alignItems: "center",
                                                                flexDirection: "column",
                                                                boxSizing: "border-box",
                                                                overflow: "hidden",
                                                                textAlign: "center",
                                                            }}
                                                        >
                                                            <div className={` barcode-card-print d-flex  justify-content-center align-items-start h-100 last:`}>
                                                                <div className="barcode-content-print w-100 text-center d-flex align-items-center h-100 justify-content-start">
                                                                    {headerCheckboxes.header && <p
                                                                        className={`${formState.selectedPrinter === "regular_printer"
                                                                            ? labelView === 65
                                                                                ? `barcodeHeader64 barcodeHeaderPDF64`
                                                                                : labelView === 48
                                                                                    ? `barcodeHeader48 barcodeHeaderPDF48`
                                                                                    : `barcodeHeader24`
                                                                            : "barcodeHeader-label"}  fw-7 text-center mb-0 text-black`}
                                                                    >Header</p>}
                                                                    <div className=" barcode-image-height h-100 w-100 flex-grow-1 ">
                                                                        <img src={barCodeimg1} alt="imgaes" className="barcode_image w-100 h-100"
                                                                        />
                                                                    </div>
                                                                    {headerCheckboxes.sku && <div
                                                                        className={`${formState.selectedPrinter === "regular_printer"
                                                                            ? labelView === 65
                                                                                ? `barcodesku64 barcodeskuPDF64 lh-1`
                                                                                : labelView === 48
                                                                                    ? `barcodesku48 lh-1 barcodeskuPDF48`
                                                                                    : `barcodesku24`
                                                                            : "barcodesku-label"} barcode-number fw-7 text-black text-center mb-1`}

                                                                    >V92YLF5LL60E18VOTX</div>}
                                                                    {headerCheckboxes.line1 && <p
                                                                        className={` ${formState.selectedPrinter === "regular_printer"
                                                                            ? labelView === 65
                                                                                ? `barcodeline1_64 barcodeline_PDF lh-1`
                                                                                : labelView === 48
                                                                                    ? `barcodeline1_48 lh-1 barcodeline_PDF`
                                                                                    : `barcodeline1_24`
                                                                            : "barcodeline1_label lh-1"} fw-6 text-black text-center mb-1`}
                                                                    >Line 1</p>}
                                                                    <div className={headerCheckboxes.line2 && headerCheckboxes.line3 ? "d-flex justify-content-between w-100" : "d-flex justify-content-center"}>
                                                                        {headerCheckboxes.line2 && <p
                                                                            className={` ${formState.selectedPrinter === "regular_printer"
                                                                                ? labelView === 65
                                                                                    ? `barcodeline2_64 lh-1 barcodeline_PDF`
                                                                                    : labelView === 48
                                                                                        ? `barcodeline2_48 lh-1 barcodeline_PDF`
                                                                                        : `barcodeline2_24`
                                                                                : "barcodeline2_label lh-1 "} fw-5 text-black text-center mb-1`}
                                                                        >Line 2</p>}
                                                                        {headerCheckboxes.line3 && <p
                                                                            className={` ${formState.selectedPrinter === "regular_printer"
                                                                                ? labelView === 65
                                                                                    ? `barcodeline2_64 lh-1 barcodeline_PDF`
                                                                                    : labelView === 48
                                                                                        ? `barcodeline2_48 lh-1 barcodeline_PDF`
                                                                                        : `barcodeline2_24`
                                                                                : "barcodeline2_label lh-1"} fw-5 text-black text-center  mb-1`}
                                                                        >Line 3</p>}
                                                                    </div>
                                                                    {headerCheckboxes.line4 && <p
                                                                        className={` ${formState.selectedPrinter === "regular_printer"
                                                                            ? labelView === 65
                                                                                ? `barcodeline4_64 lh-1 barcodeline_PDF`
                                                                                : labelView === 48
                                                                                    ? `barcodeline4_48 lh-1 barcodeline_PDF`
                                                                                    : `barcodeline4_24`
                                                                            : "barcodeline4_label lh-1"} fw-4 text-black text-center mb-0`}
                                                                    >Line 4</p>}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="barcode-height-bottom">
                                                            {formState.selectedPrinter === "regular_printer" ? (
                                                                labelView === 65
                                                                    ? "38 mm"
                                                                    : labelView === 48
                                                                        ? "48 mm"
                                                                        : labelView === 24
                                                                            ? "64 mm"
                                                                            : LabelBarcodeDetails?.width
                                                                                ? `${LabelBarcodeDetails.width} mm`
                                                                                : ""
                                                            ) : LabelBarcodeDetails?.width ? `${LabelBarcodeDetails.width} mm` : ""}
                                                        </div>
                                                        <div className="barcode-bottom-line"></div>
                                                    </div>
                                                </div>
                                                {isTwoLabel?.labelView === 2 && <div>
                                                    <div className={`${formState.selectedPrinter === "regular_printer"
                                                        ? labelView === 65
                                                            ? "barcode-length65 barcode-lengthMM"
                                                            : labelView === 48
                                                                ? "barcode-length48 barcodeHeaderPDF48 barcode-lengthMM"
                                                                : "barcode-length24 barcode-lengthMM"
                                                        : "barcode_length-other"} barcode-card position-relative`}

                                                        style={{
                                                            ...(formState.selectedPrinter === "label_printer"
                                                                ? {
                                                                    width: `${LabelBarcodeDetails?.width}mm`,
                                                                    height: `${LabelBarcodeDetails?.height}mm`,
                                                                }
                                                                : {}),
                                                            display: "flex",
                                                            justifyContent: "center",
                                                            alignItems: "center",
                                                            flexDirection: "column",
                                                            boxSizing: "border-box",
                                                            overflow: "hidden",
                                                            textAlign: "center",
                                                        }}
                                                    >
                                                        <div className={` barcode-card-print d-flex  justify-content-center align-items-start h-100 last:`}>
                                                            <div className="barcode-content-print w-100 text-center d-flex align-items-center h-100 justify-content-start">
                                                                {headerCheckboxes.header && <p
                                                                    className={`${formState.selectedPrinter === "regular_printer"
                                                                        ? labelView === 65
                                                                            ? `barcodeHeader64 barcodeHeaderPDF64`
                                                                            : labelView === 48
                                                                                ? `barcodeHeader48 barcodeHeaderPDF48`
                                                                                : `barcodeHeader24`
                                                                        : "barcodeHeader-label"}  fw-7 text-center mb-0 text-black`}
                                                                >Header</p>}
                                                                <div className=" barcode-image-height h-100 w-100 flex-grow-1 ">
                                                                    <img src={barCodeimg1} alt="imgaes" className="barcode_image w-100 h-100"
                                                                    />
                                                                </div>
                                                                {headerCheckboxes.sku && <div
                                                                    className={`${formState.selectedPrinter === "regular_printer"
                                                                        ? labelView === 65
                                                                            ? `barcodesku64 barcodeskuPDF64 lh-1`
                                                                            : labelView === 48
                                                                                ? `barcodesku48 lh-1 barcodeskuPDF48`
                                                                                : `barcodesku24`
                                                                        : "barcodesku-label"} barcode-number fw-7 text-black text-center mb-1`}

                                                                >V92YLF5LL60E18VOTX</div>}
                                                                {headerCheckboxes.line1 && <p
                                                                    className={` ${formState.selectedPrinter === "regular_printer"
                                                                        ? labelView === 65
                                                                            ? `barcodeline1_64 barcodeline_PDF lh-1`
                                                                            : labelView === 48
                                                                                ? `barcodeline1_48 lh-1 barcodeline_PDF`
                                                                                : `barcodeline1_24`
                                                                        : "barcodeline1_label lh-1"} fw-6 text-black text-center mb-1`}
                                                                >Line 1</p>}
                                                                <div className={headerCheckboxes.line2 && headerCheckboxes.line3 ? "d-flex justify-content-between w-100" : "d-flex justify-content-center"}>
                                                                    {headerCheckboxes.line2 && <p
                                                                        className={` ${formState.selectedPrinter === "regular_printer"
                                                                            ? labelView === 65
                                                                                ? `barcodeline2_64 lh-1 barcodeline_PDF`
                                                                                : labelView === 48
                                                                                    ? `barcodeline2_48 lh-1 barcodeline_PDF`
                                                                                    : `barcodeline2_24`
                                                                            : "barcodeline2_label lh-1"} fw-5 text-black text-center mb-1`}
                                                                    >Line 2</p>}
                                                                    {headerCheckboxes.line3 && <p
                                                                        className={` ${formState.selectedPrinter === "regular_printer"
                                                                            ? labelView === 65
                                                                                ? `barcodeline2_64 lh-1 barcodeline_PDF`
                                                                                : labelView === 48
                                                                                    ? `barcodeline2_48 lh-1 barcodeline_PDF`
                                                                                    : `barcodeline2_24`
                                                                            : "barcodeline2_label lh-1 "} fw-5 text-black text-center  mb-1`}
                                                                    >Line 3</p>}
                                                                </div>
                                                                {headerCheckboxes.line4 && <p
                                                                    className={` ${formState.selectedPrinter === "regular_printer"
                                                                        ? labelView === 65
                                                                            ? `barcodeline4_64 lh-1 barcodeline_PDF`
                                                                            : labelView === 48
                                                                                ? `barcodeline4_48 lh-1 barcodeline_PDF`
                                                                                : `barcodeline4_24`
                                                                        : "barcodeline4_label lh-1 "} fw-4 text-black text-center mb-0`}
                                                                >Line 4</p>}
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="mt-5">
                                    <p className="fs-18 fw-5 text-primary mb-4">Label Details</p>
                                    <PrintBarcodeTable
                                        headerCheckboxes={headerCheckboxes}
                                        setHeaderCheckboxes={setHeaderCheckboxes}
                                        tableData={tableData}
                                        setTableData={setTableData}
                                        formState={formState}
                                        setFormState={setFormState}
                                        setIsIndividual={setIsIndividual}
                                        headerSelectValues={headerSelectValues}
                                        setHeaderSelectValues={setHeaderSelectValues}
                                        allNumberOfLabel={allNumberOfLabel}
                                        setAllNumberOfLabel={setAllNumberOfLabel}
                                    />
                                </div>

                                {!tableData?.length > 0 && <div className="barcodeNotExist bacode-2">
                                    <p className="fs-20 fw-5 text-center mb-1">Add Items to list for Printing Barcode</p>
                                    <div className="barcode-img-2 mx-auto">
                                        <img src={barCodeimg1} alt="imagees" className="" />
                                    </div>
                                </div>
                                }
                            </div>
                        </Tab>
                    </Tabs>
                </Card >
                <Container fluid className="p-0 mt-10 fixed-bottom-section">
                    <div className="d-flex flex-wrap gap-sm-4 gap-3 fixed-buttons justify-content-end px-lg-10 px-sm-8 px-6">
                        <button
                            type="submit"
                            name="submitType"
                            value="save"
                            className="btn btn-primary d-flex align-items-center gap-2 w-auto"
                            onClick={handleShow}
                            disabled={tableData?.length > 0 ? false : true}
                        >
                            <img src={preview} alt="images" />
                            Preview
                        </button>
                    </div>
                </Container>
                <PrintBarcodeModal
                    showModal={showModal}
                    handleClose={handleClose}
                    formState={formState}
                    labelView={labelView}
                    tableData={tableData}
                    headerCheckboxes={headerCheckboxes}
                    printRef={printRef}
                    handlePrint={handlePrint}
                    handleDownload={handleDownload}
                    loading={loading}
                    printLoading={printLoading}
                    LabelBarcodeDetails={LabelBarcodeDetails}
                />
                {
                    isCustomBarcodeSize && <CustomBarcodeSize
                        isCustomBarcodeSize={isCustomBarcodeSize}
                        handleClose={handleCustomClose}
                        setFormState={setFormState}
                    />
                }
                <Toast />
            </div >
        </>
    )
}

export default PrintBarcode
