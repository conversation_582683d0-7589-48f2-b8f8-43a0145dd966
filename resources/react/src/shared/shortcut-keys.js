import { useEffect, useContext, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { StateContext } from "../context/StateContext";
import { ROUTES } from "../constants";

export const useTransactionShortcuts = (formRef) => {
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.altKey) {
        switch (e.keyCode) {
          case 83:
            e.preventDefault();
            formRef?.current?.querySelector('[name="submitType"][value="save"]')?.click();
            break;
          case 78:
            e.preventDefault();
            formRef?.current?.querySelector('[name="submitType"][value="saveAndNew"]')?.click();
            break;
          case 80:
            e.preventDefault();
            formRef?.current?.querySelector('[name="submitType"][value="saveAndPrint"]')?.click();
            break;
          case 113:
            if (typeof formRef === 'string' && formRef.trim() !== '') {
                $(`#${formRef}`).trigger("click");
            }
            break;
          default:
            break;
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [formRef]);
};

export const useKeyboardShortcuts = () => {
  const navigate = useNavigate();
  const stateContext = useContext(StateContext);

  // Safely extract values from context with fallbacks
  const hasUnsavedChanges = stateContext?.hasUnsavedChanges || false;
  const isFieldsChanges = stateContext?.isFieldsChanges || false;

  // Keep track of the last focused element for refocusing
  const lastFocusedElementRef = useRef(null);

  useEffect(() => {
    // Clear the last focused element reference when the component mounts or page changes
    lastFocusedElementRef.current = null;
  }, []);

  useEffect(() => {
    const handleKeyDown = (e) => {
      // Skip if any modifier keys are pressed except for our specific combinations
      if (e.altKey && e.key !== 'F8') return;
      // Allow Windows+A (metaKey + 'a') and Ctrl+A combinations
      if (e.metaKey && !(e.key === 'a')) return;

      // // F8 Key Press Behavior
      if (e.key === 'F8') {
        e.preventDefault();

        // If there is an existing record being edited, do not overwrite it
        // Instead, redirect to Dashboard page
        // Note: We navigate regardless of unsaved changes as per requirement
        // The requirement states "do not overwrite" but still redirect to dashboard
        try {
          navigate(ROUTES.DASHBOARD);
        } catch (error) {
          console.warn('Navigation to dashboard failed:', error);
          // Fallback to window location
          window.location.href = '/company/dashboard';
        }
        return;
      }

      // Escape Key Behavior
      if (e.key === 'Escape') {
        // Check if there's an open modal first
        const openModal = document.querySelector('.modal.show, .modal.fade.show, [role="dialog"][aria-hidden="false"]');
        const openBootstrapModal = document.querySelector('.modal-backdrop');
        const openSweetAlert = document.querySelector('.swal2-container');
        const openDropdown = document.querySelector('.dropdown-menu.show');

        // If there's an open modal, tooltip, dropdown, or SweetAlert, let the default behavior handle it
        if (openModal || openBootstrapModal || openSweetAlert || openDropdown) {
          // Don't prevent default - let the modal/component handle the escape key
          return;
        }

        e.preventDefault();

        // Check if an input field is currently focused
        const activeElement = document.activeElement;

        const isInputFocused = activeElement && (
          activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.tagName === 'SELECT' ||
          activeElement.isContentEditable ||
          activeElement.getAttribute('contenteditable') === 'true'
        );

        if (isInputFocused) {
          // Store the currently focused element and remove focus
          lastFocusedElementRef.current = activeElement;
          activeElement.blur();
        } else {
          // No input is focused - check if we have a previously focused element to refocus
          const storedElement = lastFocusedElementRef.current;
          const isStoredElementValid = storedElement &&
            document.contains(storedElement) &&
            storedElement.offsetParent !== null && // Element is visible
            !storedElement.disabled; // Element is not disabled

          if (isStoredElementValid) {
            // Check if the stored element is on the current page by checking if it's in the current viewport context
            const currentPageInputs = document.querySelectorAll('input, textarea, select, [contenteditable="true"]');
            const isElementOnCurrentPage = Array.from(currentPageInputs).includes(storedElement);

            if (isElementOnCurrentPage) {
              // Refocus the last focused element
              try {
                storedElement.focus();
                // Clear the reference after refocusing
                lastFocusedElementRef.current = null;
              } catch (error) {
                console.warn('Failed to refocus element:', error);
                lastFocusedElementRef.current = null;
              }
            } else {
              // Element is not on current page, clear reference and navigate back
              lastFocusedElementRef.current = null;
              try {
                window.history.back();
              } catch (error) {
                console.warn('Browser back navigation failed:', error);
              }
            }
          } else {
            console.log('No valid element to refocus');
            // No valid element to refocus, clear reference and navigate back
            lastFocusedElementRef.current = null;
            try {
              window.history.back();
            } catch (error) {
              console.warn('Browser back navigation failed:', error);
            }
          }
        }
        return;
      }

      // Ctrl + A, Cmd + A (Mac), and Windows + A behavior
      if ((e.ctrlKey && e.key === 'a') || (e.metaKey && e.key === 'a')) {
        // Check if user is typing inside a text input or textarea
        const activeElement = document.activeElement;
        const isTextInput = activeElement && (
          (activeElement.tagName === 'INPUT' &&
           ['text', 'email', 'password', 'search', 'url', 'tel', 'number'].includes(activeElement.type)) ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.isContentEditable ||
          activeElement.getAttribute('contenteditable') === 'true'
        );

        // If user is typing in a text field, allow default select-all behavior
        if (isTextInput) {
          return;
        }

        // Otherwise, prevent default and open Sale screen
        e.preventDefault();
        try {
          navigate(`${ROUTES.SALES}/create`);
        } catch (error) {
          console.warn('Navigation to sales create failed:', error);
          // Fallback to window location
          window.location.href = '/company/sales/create';
        }
        return;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [navigate, hasUnsavedChanges, isFieldsChanges]);
};

export const handleShortcutKeys = handleKeyPress => {
    useEffect(() => {
        // attach the event listener
        window.addEventListener("keydown", handleKeyPress);
        // remove the event listener
        return () => {
            window.removeEventListener("keydown", handleKeyPress);
        };
    }, [handleKeyPress]);
};
