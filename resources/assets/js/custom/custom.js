let csrfToken = $('meta[name="csrf-token"]').attr("content");

let jsrender = require("jsrender");
import moment from "moment";
require("alpinejs");
import hotkeys from "hotkeys-js";

const { list } = require("postcss");
const { now, isSet, isNull } = require("lodash");

window.companyFilter = "";
window.currentFinancialYearStartDate = "";
window.currentFinancialYearEndDate = "";
window.cancelFYStartDate = "";
window.cancelFYEndDate = "";

document.addEventListener("turbo:before-cache", function () {
    let currentSelect2 = ".select2-hidden-accessible";
    $(currentSelect2).each(function () {
        $(this).select2("destroy");
    });
    $(currentSelect2).each(function () {
        $(this).select2();
    });
    $(".toast").addClass("d-none");
});

document.addEventListener("DOMContentLoaded", initAllComponent);
function initAllComponent() {
    refreshCsrfToken();
    initAlertMessage();
    initMetronicComponents();
    initToastr();
    openMobileModal()
    modalInputFocus();
    inputFocus();
    initNumberFormat();
    initSelect2();
    initLoader();
    iniFixedAmountValue();
    if ($("#companyFilter").length) {
        companyFilter =
            JSON.parse(
                $("#companyFilter").val() != ""
                    ? $("#companyFilter").val()
                    : "{}"
            ) ?? {};
    }
    setCurrentFinancialYearDate();
    loadMaxLength();
    loadInputMaxLength();
    skuMaxLength()

}

function openMobileModal() {
    if (window.matchMedia("(max-width: 425px)").matches) {
        var currentTime = new Date();
        var storedTimeAfterSunset = localStorage.getItem('timeAfterSunset');
        var timeAfterSunset = new Date(storedTimeAfterSunset);
        if (currentTime >= timeAfterSunset) {
            var thirtyMinutesAfterSunset = new Date();
            thirtyMinutesAfterSunset.setMinutes(thirtyMinutesAfterSunset.getMinutes() + 5);
            localStorage.setItem('timeAfterSunset', thirtyMinutesAfterSunset.toISOString());
            $('#switchMobileAppModal').appendTo('body').modal('show');
        }
    }

}

window.loadMaxLength = function () {
    var KTBootstrapMaxlength = (function () {
        // Private functions
        var maxLength = function () {
            // textarea example
            $(".check-limit-textarea").maxlength({
                threshold: 5,
                warningClass: "badge text-primary limit-textarea",
                limitReachedClass: "badge text-primary limit-textarea",
                appendToParent: true,
                showMaxLength: false,
                showCharsTyped: false,
            });
        };

        return {
            init: function () {
                maxLength();
            },
        };
    })();

    KTBootstrapMaxlength.init();
};
window.skuMaxLength = function () {
    var KTBootstrapMaxlength = (function () {
        // Private functions
        var maxLength = function () {
            // textarea example
            $(".sku-code").maxlength({
                threshold: 5,
                warningClass: "badge text-primary sku-code-max-length",
                limitReachedClass: "badge text-primary sku-code-max-length",
                appendToParent: true,
                showMaxLength: false,
                showCharsTyped: false,
            });
        };

        return {
            init: function () {
                maxLength();
            },
        };
    })();

    KTBootstrapMaxlength.init();
};

window.loadInputMaxLength = function () {
    var KTBootstrapMaxlength = (function () {
        // Private functions
        var maxLength = function () {
            // textarea example
            $(".check-limit-input").maxlength({
                threshold: 5,
                warningClass: "badge text-primary input-max-length",
                limitReachedClass: "badge text-primary input-max-length",
                appendToParent: true,
                showMaxLength: false,
                showCharsTyped: false,
            });
        };

        return {
            init: function () {
                maxLength();
            },
        };
    })();

    KTBootstrapMaxlength.init();
};

window.getDefaultDate = function () {
    const [day, month, year] = currentFinancialYearEndDate.split("/");
    const today = new Date();
    const comparisonDate = new Date(year, month - 1, day);

    let defaultDate = today < comparisonDate ? moment().format("DD-MM-YYYY") : currentFinancialYearEndDate;

    return defaultDate;
};

listenClick(".flatpickr-input", function () {
    $(this).select();
});

listenChange("#currentFinancialYear", function () {
    if (!$("#currentFinancialYear").length) {
        return;
    }
    let currentFinancialYear = $(this).val();
    updateCompanyFilter("current_financial_year", currentFinancialYear);

    if ($("#balanceSheetReportIndex").length) {
        let newDate = currentFinancialYearEndDate.split("/");
        let searchParams = new URLSearchParams(window.location.search);
        let isShowFullDetailsBalanceSheet =
            searchParams.get("details") ?? false;
        location.href = route("company.balance-sheet-report", {
            date:
                currentFinancialYear.split(" - ")[1] +
                "-" +
                newDate[1] +
                "-" +
                newDate[0],
            details: isShowFullDetailsBalanceSheet,
            hideZeroBalance: $("#hideZeroBalanceBalanceSheet").is(":checked"),
        });
    } else if ($("#tradingProfitLossReportIndex").length) {
        let stockValuationMethod = $(
            "#methodOfStockValuationProfitAndLoss"
        ).val();
        let startDate = currentFinancialYearStartDate.split("/");
        let endDate = currentFinancialYearEndDate.split("/");
        let profitLossSearchParams = new URLSearchParams(
            window.location.search
        );
        let isTPLShowFullDetails = profitLossSearchParams.get("details") ?? "";
        let hideZeroBalance = $("#hideZeroBalanceTradingProfitLoss").is(":checked");
        location.href =
            route("company.trading-profit-loss", {
                start_date: currentFinancialYear.split(" - ")[0] + "-" + startDate[1] + "-" + startDate[0],
                end_date: currentFinancialYear.split(" - ")[1] + "-" + endDate[1] + "-" + endDate[0],
                stock_method: stockValuationMethod,
                details: isTPLShowFullDetails,
                hideZeroBalance: hideZeroBalance,
            });
    } else if ($("#gstr1ReportIndex").length) {
        let startDate = currentFinancialYearStartDate.split("/");
        let endDate = currentFinancialYearEndDate.split("/");
        var currentDate = moment();
        var financialYear = currentFinancialYear.split(' - ');

        var fyStartYear = parseInt(financialYear[0].trim());
        var fyEndYear = parseInt(financialYear[1].trim());

        // Determine current financial year (April to March)
        var currentFyStart = moment(currentDate.year() + "-04-01");
        var currentFyEnd = moment((currentDate.year() + 1) + "-03-31");

        if (currentDate.month() < 3) { // Jan=0, Feb=1, Mar=2
            currentFyStart = moment((currentDate.year() - 1) + "-04-01");
            currentFyEnd = moment(currentDate.year() + "-03-31");
        }

    var sDate, eDate;

    if (fyStartYear === currentFyStart.year() && fyEndYear === currentFyEnd.year()) {
        // Current FY selected → Show last month
        var lastMonth = currentDate.clone().subtract(1, 'month');
        sDate = lastMonth.clone().startOf('month').format('YYYY-MM-DD');
        eDate = lastMonth.clone().endOf('month').format('YYYY-MM-DD');
    } else {
        // Other FY selected → Show March of selected FY
        sDate = moment(fyEndYear + "-03-01").startOf('month').format('YYYY-MM-DD');
        eDate = moment(fyEndYear + "-03-31").format('YYYY-MM-DD');
    }

        location.href = route("company.reports.gstr-1", {
            start_date:
                sDate,
            end_date:
                eDate,
                'filter_type' : 1,
        });
    } else if ($("#gstr3bReportIndex").length) {
        let startDate = currentFinancialYearStartDate.split("/");
        let endDate = currentFinancialYearEndDate.split("/");
        location.href = route("company.reports.gstr-3b-summary", {
            start_date:
                currentFinancialYear.split(" - ")[0] +
                "-" +
                startDate[1] +
                "-" +
                startDate[0],
            end_date:
                currentFinancialYear.split(" - ")[1] +
                "-" +
                endDate[1] +
                "-" +
                endDate[0],
        });
    } else {
        setTimeout(function () {
            location.reload();
        }, 1000);
    }
});

listenChange(".company-list-select2", function () {
    let companyId = $(this).val();
    location.href = route("company.set-company", { company: companyId });
});



window.addEventListener("initSelect2", function () {
    initSelect2();
});

window.handleSearchDatatable = (tbl) => {
    const filterSearch = document.querySelector(
        '[data-datatable-filter="search"]'
    );
    filterSearch.addEventListener("keyup", function (e) {
        tbl.search(e.target.value).draw();
    });
    filterSearch.addEventListener("search", function (e) {
        tbl.search(e.target.value).draw();
    });
};

window.intiTooltip = () => {
    $("body").tooltip({ selector: "[data-bs-toggle=tooltip]" });
};

let firstTime = true;

function initMetronicComponents() {
    if (firstTime) {
        firstTime = false;
        return;
    }

    // setTimeout(function () {
    KTDialer.init();
    KTDrawer.init();
    KTPasswordMeter.init();
    KTMenu.init();
    KTImageInput.init();
    KTScroll.init();
    KTScrolltop.init();
    KTSticky.init();
    KTSwapper.init();
    KTToggle.init();
    KTApp.init();
    KTLayoutSearch.init();
    KTLayoutAside.init();
    KTLayoutToolbar.init();
    // }, 250);
}

window.purchaseIntraStateTaxArray = [
    "Intrastate Purchase Taxable",
    "Intrastate Purchase URD Taxable",
];

window.purchaseInterstateTaxArray = [
    "Interstate Purchase Taxable",
    "Interstate Purchase URD Taxable",
    "Purchase - Import of goods",
    "Purchase - Import of Service",
];

window.intraStateArray = [
    // old types
    "Sale Intra State Taxable",
    "Sale Intra State Exempt",
    "Sale Intra State Nilrated",
    // new types
    "Intrastate Sales Taxable",
    // "Deemed Export - Intrastate",

    // nothing any taxes calculation
    // 'Intrastate Sales Exempt',
    // 'Intrastate Sales Nil Rated',
];

window.interStateArray = [
    // old types
    "Sale Inter State Taxable",
    "Sale Inter State Exempt",
    "Sale Inter State Nilrated",
    // new types
    "Interstate Sales Taxable",
    // nothing any taxes calculation
    // 'Interstate Sales Exempt',
    // 'Interstate Sales Nil Rated',
];

window.exportArray = [
    // old types
    "Export Taxable",
    "Export Exempt",
    "Export Nilrated",
    "Export LUT/Bond",
    // new types
    "Export Sales Taxable",
    // 'Export Sales Exempt',
    // 'Export Sales Nil Rated',
    // 'Export Sales under LUT/Bond',
];

window.sezArray = [
    // old types
    "Sales to SEZ - Taxable",
    "Sales to SEZ - Exempt",
    "Sales to SEZ - Nilrated",
    "Sales to SEZ - LUT/Bond",
    // new types
    "Sales to SEZ Taxable",
    // 'Sales to SEZ Exempt',
    // 'Sales to SEZ Nil Rated',
    // 'Sales to SEZ under LUT/Bond',
];

window.getCurrencySymbol = getSettingCurrencySymbol;

function refreshCsrfToken() {
    csrfToken = $('meta[name="csrf-token"]').attr("content");

    $(document).ajaxError(function (event, jqxhr, settings, thrownError) {
        if (jqxhr.status == 401) {
            location.href = route("login");
        }
    });

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        }
    });
}

function initAlertMessage() {
    $(".alert").delay(5000).slideUp(300);
}

window.initSelect2 = function () {
    $("[data-control=select2]").each(function () {
        $(this).select2({
            dropdownAutoWidth: true,
            // dropdownParent: $(this).parent()
        });
    });
};

window.deleteItem = function (url, header, callFunction = null) {
    Swal.fire({
        title: "Delete !",
        text: 'Are you sure want to delete this "' + header + '" ?',
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#D9214E",
        cancelButtonText: "No, Cancel",
        confirmButtonText: "Yes, Delete",
    }).then((result) => {
        if (result.isConfirmed) {
            deleteItemAjax(url, header, callFunction);
        }
    });
};

window.deleteItemAjax = function (url, header, callFunction = null) {
    $.ajax({
        url: url,
        type: "DELETE",
        dataType: "json",
        success: function (obj) {
            if (obj.success) {
                if (isNull(callFunction)) {
                    livewire.emit("refreshDatatable");
                    livewire.emit("refresh");
                    if ($('#addPaymentModeModel').length) {
                        $('#addPaymentModeModel').modal('hide')
                    }
                }
            }
            Swal.fire({
                icon: "success",
                title: "Deleted!",
                confirmButtonColor: "#009ef7",
                text: header + " has been deleted.",
                timer: 2000,
            });
            if (typeof callFunction === 'function') {
                callFunction();
            }
        },
        error: function (data) {
            Swal.fire({
                title: "",
                text: data.responseJSON.message,
                confirmButtonColor: "#009ef7",
                icon: "error",
                timer: 5000,
            });
        },
    });
};

window.prepareTemplateRender = function (templateSelector, data) {
    let template = jsrender.templates(templateSelector);
    return template.render(data);
};

toastr.options = {
    closeButton: true,
    debug: false,
    newestOnTop: false,
    progressBar: false,
    positionClass: "toast-top-right",
    preventDuplicates: false,
    onclick: null,
    showDuration: "300",
    hideDuration: "1000",
    timeOut: "1000",
    extendedTimeOut: "1000",
    showEasing: "swing",
    hideEasing: "linear",
    showMethod: "fadeIn",
    hideMethod: "fadeOut",
};

window.displaySuccessMessage = function (message) {
    toastr.success(message);
};

window.displayErrorMessage = function (message) {
    toastr.error(message);
};

window.initToastr = function () {
    toastr.options = {
        closeButton: true,
        debug: false,
        newestOnTop: false,
        progressBar: false,
        positionClass: "toastr-top-right",
        preventDuplicates: false,
        onclick: null,
        showDuration: "300",
        hideDuration: "1000",
        timeOut: "1000",
        extendedTimeOut: "1000",
        showEasing: "swing",
        hideEasing: "linear",
        showMethod: "fadeIn",
        hideMethod: "fadeOut",
    };
};

window.displayErrorMessage = function (message) {
    toastr.error(message);
};

listenSubmit("#changePasswordForm", function (e) {
    e.preventDefault();

    $.ajax({
        url: route("change.password"),
        type: "POST",
        data: $(this).serialize(),
        success: function (result) {
            displaySuccessMessage(result.message);
            $("#changePasswordModal").modal("hide");
            $("#changePasswordForm")[0].reset();
        },
        error: function (error) {
            displayErrorMessage(error.responseJSON.message);
        },
    });
});

listenHiddenBsModal("#changePasswordModal", function () {
    $("#changePasswordForm .active").removeClass("active");
    $("#changePasswordForm")[0].reset();
});

window.withoutFocusRoutes = [
    'company.dashboard',
    'company.groups.index',
    'company.ledgers.index',
    'company.income-transaction.index',
    'company.expense-transaction.index',
    'company.receipt-transaction.index',
    'company.payment-transaction.index',
    'company.journal-transaction.index',
    'company.item-master-groups.index',
    'company.item-masters.index',
    'company.print-barcode.index',
    'company.print-barcode.index',
    'company.transport-master.index',
    // 'company.cess-rates.index',
    'company.reports-menu',
    'company.ageing-report',
    'company.sale-report',
    'company.purchase-report',
    'company.day-book-report',
    'company.cash-bank-report',
    'company.report-stock',
    'company.broker-report',
    'company.trial-balance-report',
    'company.trading-profit-loss',
    'company.balance-sheet-report',
    'company.cash-flow-report',
    'company.e-invoice-table-index',
    'company.e-way-bill-table-index',
    'company.reports.gstr-1',
    'company.reports.gstr-3b-summary',
    'company.input-tax-register-report',
    'company.output-tax-register-report',
    'company.hsn-summary-outward-report',
    'company.hsn-summary-inward-report',
    'company.tds-liability-report',
    'company.tds-return-report',
    'company.tcs-liability-report',
    'company.tcs-return-report',
    'company.company-team-management.index',
    'company.setting.index',
    'company.shortcut-key.index',
    'company.support.index',
    'company.transfer-closing-balance.index',
    'company.ledger-report',
    'company.broker-master.index',
    'company.bill-wise-profit-report',
    'company.item-wise-profit-report',
    'company.party-wise-sales-purchase-report',
];

window.withoutFocusURLParameters = [
    'ledgerId',
];

window.inputFocus = () => {
    loadInputMaxLength();

    if (typeof currentRouteName != 'undefined' && !withoutFocusRoutes.includes(currentRouteName) && !withoutFocusURLParameters.includes(currentUrlParameter)) {
        $('input:not([readonly="readonly"]):not("#companyFilter"):not([type="hidden"]):not([type="search"]):not([class="select2-search__field"]):not([name="order_date"]):not([name="document_date"]):not([name="challan_date"])').first().focus();
    }

    if (typeof currentRouteName != 'undefined' && currentRouteName == "company.ledger-report" && !withoutFocusURLParameters.includes(currentUrlParameter)) {
        setTimeout(function () {
            $('#ledgerReportLists').select2('open');
        }, 500);
    }

    if (typeof currentRouteName != 'undefined' && currentRouteName == "company.party-wise-sales-purchase-report" && !withoutFocusURLParameters.includes(currentUrlParameter)) {
        setTimeout(function () {
            $('#salesPurchaseReportPartyList').select2('open');
        }, 500);
    }
};

const modalInputFocus = () => {
    $(function () {
        $(document).on("shown.bs.modal", ".modal", function () {
            $(this).find('input:not([readonly="readonly"]):text:not(.preview-date-filter)').first().focus();
        });
    });
};

window.changeCountry = function (countryId, stateId, cityElementId = null) {
    $.ajax({
        url: route("states.list"),
        type: "get",
        dataType: "json",
        data: { countryId: countryId },
        success: function (data) {
            if (cityElementId) {
                $(cityElementId).empty();
                $(cityElementId).append(
                    $('<option value=""></option>').text("Select City")
                );
            }
            $(stateId).empty();
            $(stateId).append(
                $('<option value=""></option>').text("Select State")
            );
            $.each(data.data, function (i, v) {
                $(stateId).append(
                    $("<option></option>").attr("value", v).text(i)
                );
            });
        },
    });
};

window.changeState = function (countryId, stateId, cityElementId) {
    let city = $(cityElementId).val();

    $.ajax({
        url: route("cities.list"),
        type: "get",
        dataType: "json",
        data: {
            stateId: stateId,
            country: countryId,
        },
        success: function (data) {
            $(cityElementId).empty();
            $(cityElementId).append(
                $('<option value=""></option>').text("Select City")
            );
            $.each(data.data, function (i, v) {
                $(cityElementId).append(
                    $("<option></option>").attr("value", i).text(v)
                );
                if (city && city == i) {
                    $(cityElementId).val(city).trigger("change");
                }
            });
            if ($("#sameAsShippingAddressAsBillingAddress").is(":checked")) {
                $("#shippingCity option").each(function () {
                    if (this.value == $("#billingCity").val()) {
                        $(this).prop("selected", true);
                        $(this).trigger("change");
                    }
                });
            }
        },
    });
};

window.initNumberFormat = function () {
    $(".currency-format").each(function () {
        formatCurrency($(this));
    });
};

window.isJson = function (str) {
    try {
        JSON.parse(str);
    } catch (e) {
        return false;
    }
    return true;
};

Object.defineProperty(String.prototype, "capitalize", {
    value: function () {
        const arr = this.split(" ");
        for (let i = 0; i < arr.length; i++) {
            arr[i] = arr[i].charAt(0).toUpperCase() + arr[i].slice(1);
        }
        return arr.join(" ");
    },
    enumerable: false,
});

listenKeyup("[type=text].capitalize", function () {
    $(this).val($(this).val().capitalize());
});

listen("focus", ".select2-selection.select2-selection--single", function (e) {
    $(this)
        .closest(".select2-container")
        .siblings("select:enabled")
        .select2("open");
});

listenKeyup(".tan-number-validation", function () {
    $(this).val($(this).val().toUpperCase());
    let inputValues = $(this).val();
    let regex = /[A-Z]{4}[0-9]{5}[A-Z]{1}$/;
    if (inputValues.length === 10 && regex.test(inputValues)) {
        $(".tan-number-invalid").addClass("d-none");
        return regex.test(inputValues);
    } else {
        $(".tan-number-invalid").removeClass("d-none");
    }
    if (inputValues.length === 0) {
        $(".tan-number-invalid").addClass("d-none");
    }
});
listenKeyup(".bank-ifsc-number", function () {
    $(this).val($(this).val().toUpperCase());
    let inputValues = $(this).val();
    let reg = /[A-Z|a-z]{4}[0][a-zA-Z0-9]{6}$/;
    if (inputValues.match(reg)) {
        $(".ifsc-number-invalid").addClass("d-none");
        return true;
    } else {
        $(".ifsc-number-invalid").removeClass("d-none");
    }
});

listenKeyup(".pf-number", function () {
    $(this).val($(this).val().toUpperCase());
});

listenKeyup(".check-gst-number", async function () {
    $(this).val($(this).val().toUpperCase());
    let inputValues = $(this).val();
    let gstinFormat = new RegExp(
        "^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][a-zA-Z0-9]{3}$"
    );

    $(".company-pan-number").val(inputValues.substr(2, 10)).trigger("change");
    $(".pan-number").val(inputValues.substr(2, 10)).trigger("change");

    if (inputValues.length === 15 && gstinFormat.test(inputValues)) {
        $(".gst-number-invalid").addClass("d-none");
        $.ajax({
            url: route("company.get-gst-information-api", { gstNumber: inputValues }),
            method: "GET",
            success: async function (data) {
                let result = isJson(data.data) ? JSON.parse(data.data) : "";
                if (result.success) {
                    $(".gst-number-is-invalid").val(1);
                    let address1 = result.result.pradr.addr.bno + ", " + result.result.pradr.addr.flno + ", " + result.result.pradr.addr.bnm;
                    let address2 = result.result.pradr.addr.st;
                    let pinCode = result.result.pradr.addr.pncd;
                    let state = result.result.pradr.addr.stcd;
                    let city = result.result.pradr.addr.dst;

                    $(".address1").val(address1);
                    $(".address2").val(address2);
                    $(".pin-code").val(pinCode);
                    $(".change-country").val(101) // set india as default country
                    if ($("#sameAsShippingAddressAsBillingAddress").is(":checked")) {
                        $(".shipping_address_address_1").val(address1);
                        $(".shipping_address_address_2").val(address2);
                        $(".shipping_address_pin_code").val(pinCode);
                    }

                    if ($("#billingAndDispatchAddressSame").is(":checked")) {
                        $("#dispatchAddress1").val(address1);
                        $("#dispatchAddress2").val(address2);
                        $("#dispatchPincode").val(pinCode);
                    }

                    let gstRegistrationType = result.result.dty;
                    if (gstRegistrationType == 'Regular') {
                        $("#GSTRegistrationType").val(1);
                    } else if (gstRegistrationType == 'Composition') {
                        $("#GSTRegistrationType").val(2);
                    } else {
                        $("#GSTRegistrationType").val(3);
                    }

                    let addressResponse = await $.ajax({
                        url: route("get-address", { country: $(".change-country").val() ?? $("#billingCountryId").val(), state: state, city: city }),
                        type: "get",
                        dataType: "json",
                    });

                    if ($('#billingTradeName').length) {
                        $('#billingTradeName').val(result.result.tradeNam)
                    }

                    if ($('.company-trade-name').length) {
                        $('.company-trade-name').val(result.result.tradeNam)
                        $('.company-legal-name').val(result.result.lgnm)

                        if ($('#billingAndDispatchAddressSame').is(":checked")) {
                            $("#dispatchAddress1").val(address1);
                            $("#dispatchAddress2").val(address2);
                            $("#billingPincode").val(pinCode);
                        }
                    }

                    $('#brokerMaster').val(result.result.tradeNam)
                    $('#transportMaster').val(result.result.tradeNam)

                    if ($('#ledgerName').length) {
                        $('#ledgerName').val(result.result.tradeNam)
                        $('.ledger-name').val(result.result.tradeNam)
                        $('#NameOfProprietor').val(result.result.tradeNam)
                    }

                    if ($(".shipping_address_address_1").length && $("#sameAsShippingAddressAsBillingAddress").is(":checked")) {
                        $(".shipping-gst-country").val(addressResponse.data.countryId).trigger('change')
                        $(".shipping_address_address_1").val(address1);
                        $(".shipping_address_address_2").val(address2);
                        $(".shipping_address_pin_code").val(pinCode);
                        $(".shipping_address_country_id").val(101) // set india as default country
                        await Promise.all([
                            appendOptionToStateSelect(
                                $(".shipping_address_state_id"),
                                $(".shipping_address_country_id").val() ??
                                $("#supplierShippingCountry").val()
                            ),
                            appendOptionToCitySelect(
                                $(".shipping_address_city_id"),
                                addressResponse.data.stateId
                            ),
                        ]);

                        $(".shipping_address_state_id option").each(function () {
                            if (this.value == addressResponse.data.stateId) {
                                $(this).prop("selected", true);
                            }
                        });

                        $(".shipping_address_city_id option").each(function () {
                            if (this.value == addressResponse.data.cityId) {
                                $(this).prop("selected", true);
                            }
                        });
                        initSelect2();
                    }

                    if (addressResponse.success) {
                        await Promise.all([
                            appendOptionToStateSelect(
                                $(".change-state"),
                                $(".change-country").val() ??
                                $("#billingCountryId").val()
                            ),
                            appendOptionToCitySelect(
                                $(".change-city"),
                                addressResponse.data.stateId
                            ),
                        ]);

                        $(".change-state option").each(function () {
                            if (
                                this.value == addressResponse.data.stateId
                            ) {
                                $(this).prop("selected", true);
                            }
                        });

                        $(".change-city option").each(function () {
                            if (this.value == addressResponse.data.cityId) {
                                $(this).prop("selected", true);
                            }
                        });

                        let country = $("#billingCountryId").select2('data')
                        initSelect2();
                        $(".billing-address-label").html(
                            address1 +
                            ", " +
                            address2 +
                            ", " +
                            city +
                            ", " +
                            state +
                            ", " +
                            country[0].text +
                            " " +
                            pinCode
                        );
                        if ($("#sameAsShippingAddressAsBillingAddress").is(":checked")) {
                            await Promise.all([
                                appendOptionToStateSelect(
                                    $(".shipping_address_state_id"),
                                    $(
                                        ".shipping_address_country_id"
                                    ).val() ??
                                    $("#supplierShippingCountry").val()
                                ),
                                appendOptionToCitySelect(
                                    $(".shipping_address_city_id"),
                                    addressResponse.data.stateId
                                ),
                            ]);

                            $(".shipping_address_state_id option").each(
                                function () {
                                    if (
                                        this.value ==
                                        addressResponse.data.stateId
                                    ) {
                                        $(this).prop("selected", true);
                                    }
                                }
                            );

                            $(".shipping_address_city_id option").each(
                                function () {
                                    if (
                                        this.value ==
                                        addressResponse.data.cityId
                                    ) {
                                        $(this).prop("selected", true);
                                    }
                                }
                            );
                            initSelect2();
                        }

                        if ($("#billingAndDispatchAddressSame").is(":checked") ||
                            $("#billingAndDispatchAddressSameHide").is(":checked")) {
                            $("#dispatchCountryId").val(101) // set india as default country
                            await Promise.all([
                                appendOptionToStateSelect(
                                    $("#dispatchStateId"),
                                    $("#dispatchCountryId").val() ??
                                    $("#billingCountryId").val()
                                ),
                                appendOptionToCitySelect(
                                    $("#dispatchCityId"),
                                    addressResponse.data.stateId
                                ),
                            ]);
                            $("#dispatchStateId option").each(function () {
                                if (
                                    this.value ==
                                    addressResponse.data.stateId
                                ) {
                                    $(this).prop("selected", true);
                                }
                            });
                            $("#dispatchCityId option").each(function () {
                                if (
                                    this.value ==
                                    addressResponse.data.cityId
                                ) {
                                    $(this).prop("selected", true);
                                }
                            });
                            initSelect2();
                        }
                    }
                } else {
                    displayErrorMessage("GST number is Invalid");
                    $(".gst-number-is-invalid").val(0);
                }
            },
        });
    } else {
        $(".gst-number-invalid").removeClass("d-none");
    }
    if (inputValues.length === 0) {
        $(".company-pan-number").val(null).trigger("change");
        $(".gst-number-invalid").addClass("d-none");
    }
});

listenKeyup(".shipping-address-gstin", async function () {
    $(this).val($(this).val().toUpperCase());
    let inputValues = $(this).val();
    let gstinFormat = new RegExp(
        "^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][a-zA-Z0-9]{3}$"
    );

    $(".company-pan-number").val(inputValues.substr(2, 10)).trigger("change");

    if (inputValues.length === 15 && gstinFormat.test(inputValues)) {
        $(".gst-shipping-number-invalid").addClass("d-none");
        $.ajax({
            url: route("company.get-gst-information-api", { gstNumber: inputValues }),
            method: "GET",
            success: async function (data) {
                let result = isJson(data.data) ? JSON.parse(data.data) : "";
                if (result.success) {
                    $(".gst-number-is-invalid").val(1);
                    let address1 = result.result.pradr.addr.bno + ", " + result.result.pradr.addr.flno + ", " + result.result.pradr.addr.bnm;
                    let address2 = result.result.pradr.addr.st;
                    let pinCode = result.result.pradr.addr.pncd;
                    let state = result.result.pradr.addr.stcd;
                    let city = result.result.pradr.addr.dst;
                    $(".change-country").val(101)// set india as default country
                    $('.shipping-address-name').val(result.result.tradeNam);
                    $(".shipping_address_address_1").val(address1);
                    $(".shipping_address_address_2").val(address2);
                    $(".shipping_address_pin_code").val(pinCode);

                    let country = $(".change-country").val() ?? $("#billingCountryId").val();

                    let addressResponse = await $.ajax({
                        url: route("get-address", { country: country, state: state, city: city }),
                        type: "get",
                        dataType: "json",
                    });

                    $(".shipping_address_country_id option").each(function () {
                        if (this.value == country) {
                            $(this).prop("selected", true);
                        }
                    });

                    await Promise.all([
                        appendOptionToStateSelect(
                            $(".shipping_address_state_id"),
                            $(".shipping_address_country_id").val() ??
                            $("#supplierShippingCountry").val()
                        ),
                        appendOptionToCitySelect(
                            $(".shipping_address_city_id"),
                            addressResponse.data.stateId
                        ),
                    ]);

                    $(".shipping_address_state_id option").each(function () {
                        if (this.value == addressResponse.data.stateId) {
                            $(this).prop("selected", true);
                        }
                    });

                    $(".shipping_address_city_id option").each(function () {
                        if (this.value == addressResponse.data.cityId) {
                            $(this).prop("selected", true);
                        }
                    });
                    initSelect2();
                }
            }
        });
    } else {
        $(".gst-shipping-number-invalid").removeClass("d-none");
    }
    if (inputValues.length === 0) {
        $(".company-pan-number").val(null).trigger("change");
        $(".gst-shipping-number-invalid").addClass("d-none");
    }
});

listenKeyup(".ledger-shipping-address-gstin", async function () {
    $(this).val($(this).val().toUpperCase());
    let inputValues = $(this).val();
    let gstinFormat = new RegExp(
        "^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][a-zA-Z0-9]{3}$"
    );

    $(".company-pan-number").val(inputValues.substr(2, 10)).trigger("change");

    if (inputValues.length === 15 && gstinFormat.test(inputValues)) {
        $(".ledger-shipping-number-invalid").addClass("d-none");
        $.ajax({
            url: route("company.get-gst-information-api", { gstNumber: inputValues }),
            method: "GET",
            success: async function (data) {
                let result = isJson(data.data) ? JSON.parse(data.data) : "";
                if (result.success) {
                    $(".gst-number-is-invalid").val(1);
                    let address1 = result.result.pradr.addr.bno + ", " + result.result.pradr.addr.flno + ", " + result.result.pradr.addr.bnm;
                    let address2 = result.result.pradr.addr.st;
                    let pinCode = result.result.pradr.addr.pncd;
                    let state = result.result.pradr.addr.stcd;
                    let city = result.result.pradr.addr.dst;
                    $(".change-country").val(101) // set india as default country
                    $('.ledger-shipping-address-name').val(result.result.tradeNam);
                    $(".ledger-shipping-address-1").val(address1);
                    $(".ledger-shipping-address-2").val(address2);
                    $(".ledger-shipping-address-pin-code").val(pinCode);

                    let country = $(".change-country").val() ?? $("#billingCountryId").val();

                    let addressResponse = await $.ajax({
                        url: route("get-address", { country: country, state: state, city: city }),
                        type: "get",
                        dataType: "json",
                    });

                    $(".ledger-shipping-address-country-id option").each(function () {
                        if (this.value == country) {
                            $(this).prop("selected", true);
                        }
                    });

                    await Promise.all([
                        appendOptionToStateSelect(
                            $(".ledger-shipping-address-state-id"),
                            $(".ledger-shipping-address-country-id").val() ??
                            $("#supplierShippingCountry").val()
                        ),
                        appendOptionToCitySelect(
                            $(".ledger-shipping-address-city-id"),
                            addressResponse.data.stateId
                        ),
                    ]);

                    $(".ledger-shipping-address-state-id option").each(function () {
                        if (this.value == addressResponse.data.stateId) {
                            $(this).prop("selected", true);
                        }
                    });

                    $(".ledger-shipping-address-city-id option").each(function () {
                        if (this.value == addressResponse.data.cityId) {
                            $(this).prop("selected", true);
                        }
                    });
                    initSelect2();
                }
            }
        });
    } else {
        $(".ledger-shipping-number-invalid").removeClass("d-none");
    }
    if (inputValues.length === 0) {
        $(".company-pan-number").val(null).trigger("change");
        $(".ledger-shipping-number-invalid").addClass("d-none");
    }
});

listenKeyup(".ledger-billing-address-gstin", async function () {
    $(this).val($(this).val().toUpperCase());
    let inputValues = $(this).val();
    let gstinFormat = new RegExp(
        "^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][a-zA-Z0-9]{3}$"
    );

    $(".company-pan-number").val(inputValues.substr(2, 10)).trigger("change");

    if (inputValues.length === 15 && gstinFormat.test(inputValues)) {
        $(".ledger-billing-number-invalid").addClass("d-none");
        $.ajax({
            url: route("company.get-gst-information-api", { gstNumber: inputValues }),
            method: "GET",
            success: async function (data) {
                let result = isJson(data.data) ? JSON.parse(data.data) : "";
                if (result.success) {
                    $(".gst-number-is-invalid").val(1);
                    let address1 = result.result.pradr.addr.bno + ", " + result.result.pradr.addr.flno + ", " + result.result.pradr.addr.bnm;
                    let address2 = result.result.pradr.addr.st;
                    let pinCode = result.result.pradr.addr.pncd;
                    let state = result.result.pradr.addr.stcd;
                    let city = result.result.pradr.addr.dst;
                    $(".change-country").val(101) // set india as default country
                    $(".ledger-billing-address-1").val(address1);
                    $(".ledger-billing-address-2").val(address2);
                    $(".ledger-billing-address-pin-code").val(pinCode);

                    if ($("#sameAsShippingAddressAsBillingAddress").is(":checked")) {
                        $(".ledger-shipping-address-1").val(address1);
                        $(".ledger-shipping-address-2").val(address2);
                        $(".ledger-shipping-address-pin-code").val(pinCode);
                    }

                    $('#ledgerName').val(result.result.tradeNam)
                    $('.ledger-name').val(result.result.tradeNam)
                    $('#NameOfProprietor').val(result.result.tradeNam)

                    let country = $(".change-country").val() ?? $("#billingCountryId").val();

                    let addressResponse = await $.ajax({
                        url: route("get-address", { country: country, state: state, city: city }),
                        type: "get",
                        dataType: "json",
                    });

                    $(".ledger-shipping-address-country-id option").each(function () {
                        if (this.value == country) {
                            $(this).prop("selected", true);
                        }
                    });

                    if (addressResponse.success) {
                        await Promise.all([
                            appendOptionToStateSelect(
                                $(".ledger-billing-address-state-id"),
                                $(".ledger-billing-address-country-id").val() ??
                                $("#billingCountryId").val()
                            ),
                            appendOptionToCitySelect(
                                $(".ledger-billing-address-city-id"),
                                addressResponse.data.stateId
                            ),
                        ]);

                        $(".ledger-billing-address-state-id option").each(function () {
                            if (
                                this.value == addressResponse.data.stateId
                            ) {
                                $(this).prop("selected", true);
                            }
                        });

                        $(".ledger-billing-address-city-id option").each(function () {
                            if (this.value == addressResponse.data.cityId) {
                                $(this).prop("selected", true);
                            }
                        });
                        initSelect2();

                        if ($("#sameAsShippingAddressAsBillingAddress").is(":checked")) {
                            await Promise.all([
                                appendOptionToStateSelect(
                                    $(".ledger-shipping-address-state-id"),
                                    $(
                                        ".ledger-billing-address-country-id"
                                    ).val() ??
                                    $("#supplierShippingCountry").val()
                                ),
                                appendOptionToCitySelect(
                                    $(".ledger-shipping-address-city-id"),
                                    addressResponse.data.stateId
                                ),
                            ]);

                            $(".ledger-shipping-address-state-id option").each(
                                function () {
                                    if (
                                        this.value ==
                                        addressResponse.data.stateId
                                    ) {
                                        $(this).prop("selected", true);
                                    }
                                }
                            );

                            $(".ledger-shipping-address-city-id option").each(
                                function () {
                                    if (
                                        this.value ==
                                        addressResponse.data.cityId
                                    ) {
                                        $(this).prop("selected", true);
                                    }
                                }
                            );
                            initSelect2();
                        }
                    }
                }
            }
        });
    } else {
        $(".ledger-billing-number-invalid").removeClass("d-none");
    }
    if (inputValues.length === 0) {
        $(".company-pan-number").val(null).trigger("change");
        $(".ledger-billing-number-invalid").addClass("d-none");
    }
});

window.removeCommas = function (str) {
    if (str === undefined) {
        return str;
    }
    return str.replace(/,/g, "");
};

listen("blur", ".check-unique-gst-number-validation", function () {
    $(this).val($(this).val().toUpperCase());
    let inputValues = $(this).val();
    let gstinFormat = new RegExp(
        "^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][a-zA-Z0-9]{3}$"
    );
    if (inputValues.length === 15 && gstinFormat.test(inputValues)) {
        let editLedgerId = 0;
        if ($(".hidden-ledger-id").length) {
            editLedgerId = $(".hidden-ledger-id").val();
        }
        $.ajax({
            type: "GET",
            url: route("company.check-gst-number-unique-validation", {
                gstNumber: inputValues,
                ledgerId: editLedgerId,
            }),
            success: function (response) {
                if (response.data.isNotUniqueGstNumber) {
                    Swal.fire({
                        icon: "warning",
                        title: "Warning!",
                        confirmButtonColor: "#f70000",
                        text: response.data.massage,
                        timer: 5000,
                    });
                }
            },
        });
    }
});

listen("blur", ".check-pan-card-unique", function () {
    $(this).val($(this).val().toUpperCase());
    let inputValues = $(this).val();
    let regex = /[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    if (inputValues.length === 10 && regex.test(inputValues)) {
        let editLedgerId = 0;
        let firstTime = false;
        if ($(".hidden-ledger-id").length) {
            editLedgerId = $(".hidden-ledger-id").val();
        }
        $.ajax({
            type: "GET",
            url: route("company.check-pan-number-unique-validation", {
                panNumber: inputValues,
                ledgerId: editLedgerId,
            }),
            success: function (response) {
                firstTime = true;
                if (response.data && firstTime) {
                    firstTime = false;
                    Swal.fire({
                        icon: "warning",
                        title: "Warning!",
                        confirmButtonColor: "#f70000",
                        text: "The Pan Number has already been taken.",
                        timer: 4000,
                    });
                }
            },
        });
    }
});

listenKeyup(".inr-currency-format", function () { });

listenKeyup(".currency-format", function () {
    formatCurrency($(this));
});

listen("blur", ".currency-format", function () {
    formatCurrency($(this), "blur");
});

window.formatNumber = function (n) {
    return Number(n.replace(/\D/g, "")).toLocaleString("en-IN", {
        maximumFractionDigits: fixDigit,
    });
};

window.formatIntAndFloat = function (n) {
    return Number(n).toLocaleString("en-IN", {
        maximumFractionDigits: fixDigit,
    });
};

window.formatCurrency = function (input, blur) {
    // reference from https://codepen.io/559wade/pen/LRzEjj#anon-signup

    // get input value
    var input_val = input.val();

    // don't validate empty input
    if (input_val === "") {
        return;
    }

    // original length
    var original_len = input_val.length;

    // initial caret position
    var caret_pos = input.prop("selectionStart");

    // check for decimal
    if (input_val.indexOf(".") >= 0) {
        var decimal_pos = input_val.indexOf(".");

        var left_side = input_val.substring(0, decimal_pos);
        var right_side = input_val.substring(decimal_pos);

        left_side = formatNumber(left_side);
        // validate right side
        right_side = formatNumber(right_side);

        // On blur make sure 2 numbers after decimal
        if (blur === "blur") {
            right_side += "00";
        }

        // Limit decimal to only 2 digits
        right_side = right_side.substring(0, 2);

        // join number by .
        // input_val = left_side + '.' + right_side;
    } else {
        // no decimal entered
        // add commas to number
        // remove all non-digits
        input_val = formatNumber(input_val);

        // final formatting
        if (blur === "blur") {
            input_val += ".00";
        }
    }

    // send updated string to input
    input.val(input_val);
};

window.rateWithOutGstAmountCalculation = function (withOutGstAmount, getTaxValue, withGstElement) {
    let decimalPlaces = fixDigit;
    // if ($('#saleTransactionForm').length || $('#updateSaleTransactionForm').length) {
    //     decimalPlaces = fix5Digit;
    // } else {
    //     let amountString = withOutGstAmount.toString();
    //     if (amountString.indexOf(".") !== -1) {
    //         decimalPlaces = amountString.split(".")[1].length;
    //         decimalPlaces = decimalPlaces < 3 ? decimalPlaces : 3;
    //     }
    // }

    let elementId = $(withGstElement).attr('data-rpu-with-gst-amount');
    let decimalElement = $('.item-' + elementId).find("input[name='decimal_for_rate'][data-element=" + elementId + "]");
    if (decimalElement.length && decimalElement.val() != "") {
        decimalPlaces = decimalElement.val();
    }

    let taxAmount = (withOutGstAmount * getTaxValue) / 100;
    let withGstAmount = +withOutGstAmount + +taxAmount;
    if ($("#isCgstSgstIgstCalculated").val() == 0) {
        withGstElement.val(withOutGstAmount);
    } else {
        withGstElement.val(parseFloat(withGstAmount.toFixed(decimalPlaces)));
    }
}

window.rateWithGstAmountCalculation = function (withGstAmount, getTaxValue, withoutGstElement) {
    let decimalPlaces = fixDigit;
    // if ($('#saleTransactionForm').length || $('#updateSaleTransactionForm').length) {
    //     decimalPlaces = fix5Digit;
    // } else {
    //     let amountString = withGstAmount.toString();
    //     if (amountString.indexOf(".") !== -1) {
    //         decimalPlaces = amountString.split(".")[1].length;
    //         decimalPlaces = decimalPlaces < 3 ? decimalPlaces : 3;
    //     }
    // }

    let elementId = $(withoutGstElement).attr('data-rpu-without-gst-amount');
    let decimalElement = $('.item-' + elementId).find("input[name='decimal_for_rate'][data-element=" + elementId + "]");
    if (decimalElement.length && decimalElement.val() != "") {
        decimalPlaces = decimalElement.val();
    }

    let taxAmount = (withGstAmount * getTaxValue) / (+100 + +getTaxValue);
    let withOutGstAmount = withGstAmount - taxAmount;
    if ($("#isCgstSgstIgstCalculated").val() == 0) {
        withoutGstElement.val(withGstAmount);
    } else {
        withoutGstElement.val(parseFloat(withOutGstAmount.toFixed(decimalPlaces)));
    }
}

window.getNotEnabledTotalItemsCalculation = function (elementId) {
    let rowElement = $('.item-' + elementId);
    let quantityElement = rowElement.find("[data-quantity=" + elementId + "]");
    let quantity = quantityElement.val();

    let rateAmountElement = "";
    if (rowElement.find("[data-rpu-amount]").length) {
        rateAmountElement = rowElement.find("[data-rpu-amount=" + elementId + "]");
    } else {
        rateAmountElement = rowElement.find("[data-rpu-without-gst-amount=" + elementId + "]");
    }

    let discountTypeElement = rowElement.find("[data-discount-type=" + elementId + "]");
    let discountType = discountTypeElement.val();
    let discountValueElement = rowElement.find("[data-discount-value=" + elementId + "]");
    let totalDiscountAmount = 0;
    if (discountType == 1) {
        totalDiscountAmount = discountValueElement.val() * quantity;
    }
    if (discountType == 2) {
        totalDiscountAmount = quantityElement.val() * ((rateAmountElement.val() * discountValueElement.val()) / 100);
    }

    let subtotal = 0;
    if (quantity != 0) {
        subtotal = quantityElement.val() * rateAmountElement.val();
    }

    if (totalDiscountAmount && $.isNumeric(totalDiscountAmount) && totalDiscountAmount != 0 && quantity != 0) {
        subtotal = subtotal - totalDiscountAmount;
    }
    let taxableValue = quantity * rateAmountElement.val() - totalDiscountAmount;
    rowElement.find("[data-total-discount-amount-value=" + elementId + "]").val(totalDiscountAmount);
    rowElement.find("[data-total-amount-value=" + elementId + "]").text(subtotal.toFixed(fixDigit));
    rowElement.find("[data-hidden-sub-total-value=" + elementId + "]").val(subtotal.toFixed(fixDigit));
    rowElement.find("[data-hidden-tds-taxable-value=" + elementId + "]").val(taxableValue.toFixed(fixDigit));

    calculateTCSAmount();
    grandTotalCalculation();
};

window.totalItemsCalculation = function (elementId) {
    let rowElement = $('.item-' + elementId);
    let quantityElement = rowElement.find("[data-quantity=" + elementId + "]");
    let quantity = quantityElement.val();
    // let quantity = $("#dataQty"+elementId).val();
    let withGstAmountElement = rowElement.find("[data-rpu-with-gst-amount=" + elementId + "]");
    // let withGstAmountElement = $("#rpuWTHGSTAmt"+elementId);
    let withoutGstElement = rowElement.find("[data-rpu-without-gst-amount=" + elementId + "]");
    // let withoutGstElement = $("#rpuWOGSTAmt"+elementId);
    let withGstAmountElementValue = withGstAmountElement.val();
    let withoutGstElementValue = withoutGstElement.val();
    if ($("#isCgstSgstIgstCalculated").val() == 0) {
        withoutGstElementValue = withGstAmountElementValue;
    }
    let discountTypeElement = rowElement.find("[data-discount-type=" + elementId + "]");
    let discountType = discountTypeElement.val();
    let discountValueElement = rowElement.find("[data-discount-value=" + elementId + "]");
    // let taxAmount = withGstAmountElementValue - withoutGstElementValue;
    // let getTaxValue = $(document).find("[data-gst-tax-value=" + elementId + "]").val();
    let totalDiscountAmount = 0;

    if (discountType == 1) {
        totalDiscountAmount = discountValueElement.val() * quantity;
    }

    if (discountType == 2) {
        totalDiscountAmount = (withoutGstElementValue * discountValueElement.val()) / 100;
        totalDiscountAmount = totalDiscountAmount * quantity;
    }

    let subtotal = 0;
    if (quantity != 0) {
        subtotal = quantity * withoutGstElementValue;
    }

    if (totalDiscountAmount && $.isNumeric(totalDiscountAmount) && totalDiscountAmount != 0 && quantity != 0) {
        subtotal -= totalDiscountAmount;
    }

    let cessRate = rowElement.find("[data-hidden-cess-rate-value=" + elementId + "]").val();
    cessRate = cessRate == "" ? 0 : cessRate;
    let taxableValue = quantity * withoutGstElementValue - totalDiscountAmount;
    let cessAmount = (taxableValue * parseFloat(cessRate)) / 100;
    rowElement.find("[data-hidden-cess-amount-value=" + elementId + "]").val(cessAmount.toFixed(fixDigit));
    rowElement.find("[data-total-discount-amount-value=" + elementId + "]").val(totalDiscountAmount.toFixed(fixDigit));
    rowElement.find("[data-total-amount-value=" + elementId + "]").text(subtotal.toFixed(fixDigit));
    rowElement.find("[data-hidden-sub-total-value=" + elementId + "]").val(subtotal.toFixed(fixDigit));
    rowElement.find("[data-hidden-tds-taxable-value=" + elementId + "]").val(taxableValue.toFixed(fixDigit));

    calculateTCSAmount();
    if (cessRate != 0) {
        calculateCessAmount();
    }
    if (quantity != 0) {
        changeShippingFrightCharge();
        changePackingCharge();
    }
    grandTotalCalculation();
};

window.calculateCessAmount = function () {
    let totalCessAmount = 0;
    $(".hidden-cess-amount").each(function () {
        totalCessAmount += parseFloat($(this).val());
    });

    $(".change-cess-rate").val(totalCessAmount.toFixed(fixDigit));
};

listenKeyup(".change-cess-rate", function () {
    grandTotalCalculation();
});

window.number_format = function (number, decimals = 2) {
    let dec_point = ".";
    let thousands_sep = ",";
    // Strip all characters but numerical ones.
    number = (number + "").replace(/[^0-9+\-Ee.]/g, "");
    var n = !isFinite(+number) ? 0 : +number,
        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
        sep = thousands_sep,
        dec = dec_point,
        s = "",
        toFixedFix = function (n, prec) {
            var k = Math.pow(10, prec);
            return "" + Math.round(n * k) / k;
        };
    // Fix for IE parseFloat(0.55).toFixed(0) = 0;
    s = (prec ? toFixedFix(n, prec) : "" + Math.round(n)).split(".");
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || "").length < prec) {
        s[1] = s[1] || "";
        s[1] += new Array(prec - s[1].length + 1).join("0");
    }
    return s.join(dec);
};

window.grandTotalCalculation = function () {
    let totalAmount = 0;
    let totalTdsTaxableAmount = 0;

    $(".hidden-sub-total").each(function () {
        if ($(this).val() && $(this).val() != 0) {
            totalAmount += parseFloat($(this).val());
        }
    });
    $(".hidden-tds-taxable-value").each(function () {
        if ($(this).val() && $(this).val() != 0) {
            totalTdsTaxableAmount += parseFloat($(this).val());
        }
    });

    if ($(".transaction-module-name").val() == "saleTransaction") {
        totalClassificationCalculation(".rate-with-out-gst", ".change-ledger-amount-with-out-gst");
    } else if ($(".transaction-module-name").val() == "saleReturnTransaction") {
        totalClassificationCalculation(".sale-return-rate-with-out-gst", ".sale-return-change-ledger-amount-with-out-gst");
    } else if ($(".transaction-module-name").val() == "incomeDebitNoteTransaction") {
        totalClassificationCalculation(".income-debit-note-rate-with-out-gst", ".income-debit-note-change-ledger-amount-with-out-gst");
    } else if ($(".transaction-module-name").val() == "incomeCreditNoteTransaction") {
        totalClassificationCalculation(".income-credit-note-rate-with-out-gst", ".income-credit-note-change-ledger-amount-with-out-gst");
    } else if ($(".transaction-module-name").val() == "incomeEstimateQuoteTransaction") {
        totalClassificationCalculation(".income-estimate-quote-rate-with-out-gst", ".income-estimate-quote-change-ledger-amount-with-out-gst");
    } else if ($(".transaction-module-name").val() == "purchaseTransaction") {
        totalPurchaseClassificationCalculation(".purchase-rate-with-out-gst", ".purchase-change-ledger-amount-with-out-gst");
    } else if ($(".transaction-module-name").val() == "purchaseReturnTransaction" || $(".transaction-module-name").val() == "expenseDebitNoteTransaction") {
        totalPurchaseClassificationCalculation(".purchase-return-rate-with-out-gst", ".purchase-return-change-ledger-amount-with-out-gst");
    } else if ($(".transaction-module-name").val() == "expenseCreditNoteTransaction") {
        totalPurchaseClassificationCalculation(".expense-credit-note-rate-with-out-gst", ".expense-credit-note-change-ledger-amount-with-out-gst");
    } else if ($(".transaction-module-name").val() == "purchaseOrderTransaction") {
        totalPurchaseClassificationCalculation(".purchase-order-rate-with-out-gst", ".purchase-order-ledger-amount-with-out-gst");
    }

    let shippingAndFrightCharge = $(".change-shipping-fright-charge").val();
    if (shippingAndFrightCharge && shippingAndFrightCharge != 0) {
        totalAmount += parseFloat(shippingAndFrightCharge);
    }

    let packingCharge = $(".change-packing-charge").val();
    if (packingCharge && packingCharge != 0) {
        totalAmount += parseFloat(packingCharge);
    }

    let tcsAmount = $(".change-tcs-amount").val();
    if (tcsAmount && tcsAmount != 0) {
        totalAmount += parseFloat(tcsAmount);
    }

    if ($("#mainIsRcmApplicable").val() == 0) {
        let cgstAmount = $("#totalCgstTax").val();
        if (cgstAmount && cgstAmount != 0) {
            totalAmount += parseFloat(cgstAmount);
        }
        let sgstAmount = $("#totalSgstTax").val();
        if (sgstAmount && sgstAmount != 0) {
            totalAmount += parseFloat(sgstAmount);
        }
        let igstAmount = $("#totalIgstTax").val();
        if (igstAmount && igstAmount != 0) {
            totalAmount += parseFloat(igstAmount);
        }
    }

    let cessAmount = $(".change-cess-rate").val() == "" || !$(".change-cess-rate").val() ? 0 : $(".change-cess-rate").val();
    totalAmount += parseFloat(cessAmount);

    let classificationNatureType = $("#mainClassificationNatureType").val();
    if (classificationNatureType == "Export Sales Taxable" || classificationNatureType == "Sales to SEZ Taxable") {
        totalAmount = (totalAmount - $("#totalIgstTax").val());
    }

    let grandFinalAmount = totalAmount;
    // let subRoundOffAmount = $('.round-off-amount').val();
    // if (isNaN(subRoundOffAmount)) {
    //     subRoundOffAmount = 0;
    // }
    let roundOffAmount = 0;
    let roundOffMethod = $(".sale-round-off-method").val();
    if (roundOffMethod == 1) {
        grandFinalAmount = totalAmount.toFixed(fixDigit);
        roundOffAmount = Math.abs(grandFinalAmount - totalAmount).toFixed(fixDigit);
    } else if (roundOffMethod == 2) {
        grandFinalAmount = Math.floor(totalAmount).toFixed(fixDigit);
        roundOffAmount = (grandFinalAmount - totalAmount).toFixed(fixDigit);
    } else if (roundOffMethod == 3) {
        grandFinalAmount = Math.round(totalAmount).toFixed(fixDigit);
        roundOffAmount = (grandFinalAmount - totalAmount).toFixed(fixDigit);
    } else if (roundOffMethod == 4) {
        grandFinalAmount = Math.ceil(totalAmount).toFixed(fixDigit);
        roundOffAmount = Math.abs(grandFinalAmount - totalAmount).toFixed(fixDigit);
    } else {
        grandFinalAmount = totalAmount.toFixed(fixDigit);
    }

    // roundOffAmount -= subRoundOffAmount;
    $(".total-amount").text(totalAmount.toFixed(fixDigit));
    $(".hidden-total-amount").val(totalAmount.toFixed(fixDigit));
    $("#grandTotalForAddCgst").val(totalAmount.toFixed(fixDigit));
    $(".round-off-amount").val(roundOffAmount);
    $(".grand-total-amount").text(grandFinalAmount);
    $(".hidden-grand-total-amount").val(grandFinalAmount);
    $(".tds-taxable-value").val(totalTdsTaxableAmount.toFixed(fixDigit));

    if ($(".purchase-ledger-tds").length && $(".purchase-ledger-tds").val() != "") {
        $(".purchase-ledger-tds").val("").trigger("change");
    }
    if ($(".purchase-return-ledger-tds").length && $(".purchase-return-ledger-tds").val() != "") {
        $(".purchase-return-ledger-tds").val("").trigger("change");
    }
    if ($(".expense-debit-note-ledger-tds").length && $(".expense-debit-note-ledger-tds").val() != "") {
        $(".expense-debit-note-ledger-tds").val("").trigger("change");
    }
    if ($(".expense-credit-note-ledger-tds").length && $(".expense-credit-note-ledger-tds").val() != "") {
        $(".expense-credit-note-ledger-tds").val("").trigger("change");
    }
};

window.totalClassificationCalculation = function (rateWithGstClass, ledgerWithoutGstClass) {
    let classificationNatureType = $("#mainClassificationNatureType").val();
    let isRcmApplicable = $("#mainIsRcmApplicable").val();
    if ($("#isCompanyGstApplicable").val() == 1) {
        let totalCgstTax = 0.0;
        let totalSgstTax = 0.0;
        let totalIgstTax = 0.0;
        let roundOffAmount = 0.0;
        let isShippingPackingTaxCalculation = false;
        if ($(".item-accounting-type:checked").val() == 2) {
            console.log(ledgerWithoutGstClass, "ledgerWithoutGstClass")
            $(rateWithGstClass).each(function () {
                let elementId = $(this).attr("data-rpu-without-gst-amount");
                let rowElement = $('.item-' + elementId);
                let gstTax = rowElement.find("[data-gst-tax-value=" + elementId + "]").val();
                console.log(gstTax, "gst tax")
                let quantityElement = rowElement.find("[data-quantity=" + elementId + "]");
                let quantity = quantityElement.val();
                let withoutGstElement = rowElement.find("[data-rpu-without-gst-amount=" + elementId + "]");
                let discountTypeElement = rowElement.find("[data-discount-type=" + elementId + "]");
                let discountType = discountTypeElement.val();
                let discountValueElement = rowElement.find("[data-discount-value=" + elementId + "]");
                let centralTaxCalculationValue = rowElement.find("[data-classification-cgst-tax=" + elementId + "]");
                let stateTaxCalculationValue = rowElement.find("[data-classification-sgst-tax=" + elementId + "]");
                let integratedTaxCalculationValue = rowElement.find("[data-classification-igst-tax=" + elementId + "]");
                let subTotalAmount = rowElement.find("[data-hidden-sub-total-value=" + elementId + "]").val();

                let totalDiscountAmount = 0;
                if (discountType == 1) {
                    totalDiscountAmount = discountValueElement.val() * quantity;
                }

                if (discountType == 2) {
                    totalDiscountAmount = (withoutGstElement.val() * discountValueElement.val()) / 100;
                    totalDiscountAmount = totalDiscountAmount * quantity;
                }

                let subTotal = quantity * withoutGstElement.val() - totalDiscountAmount;
                let itemCgstTax = 0;
                let itemSgstTax = 0;
                let itemIgstTax = 0;

                centralTaxCalculationValue.val(0);
                stateTaxCalculationValue.val(0);
                integratedTaxCalculationValue.val(0);

                if ($.inArray(classificationNatureType, intraStateArray) > -1) {
                    let finalValue = 0;
                    if (isRcmApplicable == 1) {
                        centralTaxCalculationValue.val(finalValue);
                        stateTaxCalculationValue.val(finalValue);
                    } else {
                        /* let cgstTax = (parseFloat(gstTax) / 2).toFixed(fixDigit); */
                        let cgstTax = (parseFloat(gstTax) / 2);
                        finalValue = ((subTotal * cgstTax) / 100).toFixed(fixDigit);
                        if (finalValue == "NaN") {
                            finalValue = 0;
                        }
                        centralTaxCalculationValue.val(finalValue);
                        stateTaxCalculationValue.val(finalValue);
                        integratedTaxCalculationValue.val(0);
                        isShippingPackingTaxCalculation = true;
                    }
                    totalCgstTax += parseFloat(finalValue);
                    totalSgstTax += parseFloat(finalValue);
                    itemCgstTax += parseFloat(finalValue);
                    itemSgstTax += parseFloat(finalValue);
                }
                if ($.inArray(classificationNatureType, interStateArray) > -1) {
                    let finalValue = 0;
                    if (isRcmApplicable == 1) {
                        integratedTaxCalculationValue.val(finalValue);
                    } else {
                        /* let igstTax = parseFloat(gstTax).toFixed(fixDigit); */
                        let igstTax = parseFloat(gstTax);
                        finalValue = ((subTotal * igstTax) / 100).toFixed(fixDigit);
                        if (finalValue == "NaN") {
                            finalValue = 0;
                        }
                        integratedTaxCalculationValue.val(parseFloat(finalValue));
                        centralTaxCalculationValue.val(0);
                        stateTaxCalculationValue.val(0);
                        isShippingPackingTaxCalculation = true;
                    }
                    totalIgstTax += parseFloat(finalValue);
                    itemIgstTax += parseFloat(finalValue);
                }
                if ($.inArray(classificationNatureType, exportArray) > -1 || $.inArray(classificationNatureType, sezArray) > -1) {
                    /* let igstTax = parseFloat(gstTax).toFixed(fixDigit); */
                    let igstTax = parseFloat(gstTax);
                    let finalValue = ((subTotal * igstTax) / 100).toFixed(fixDigit);
                    if (finalValue == "NaN") {
                        finalValue = 0;
                    }
                    integratedTaxCalculationValue.val(parseFloat(finalValue));
                    centralTaxCalculationValue.val(0);
                    stateTaxCalculationValue.val(0);
                    totalIgstTax += parseFloat(finalValue);
                    itemIgstTax += parseFloat(finalValue);
                    isShippingPackingTaxCalculation = true;
                }
                if (classificationNatureType == "Deemed Export - Intrastate") {
                    let finalValue = 0;
                    /* let cgstTax = (parseFloat(gstTax) / 2).toFixed(fixDigit); */
                    let cgstTax = (parseFloat(gstTax) / 2);
                    finalValue = ((subTotal * cgstTax) / 100).toFixed(fixDigit);
                    if (finalValue == "NaN") {
                        finalValue = 0;
                    }
                    centralTaxCalculationValue.val(parseFloat(finalValue));
                    stateTaxCalculationValue.val(parseFloat(finalValue));
                    integratedTaxCalculationValue.val(0);
                    totalCgstTax += parseFloat(finalValue);
                    totalSgstTax += parseFloat(finalValue);
                    itemCgstTax += parseFloat(finalValue);
                    itemSgstTax += parseFloat(finalValue);
                    isShippingPackingTaxCalculation = true;
                }
                if (classificationNatureType == "Deemed Export - Interstate") {
                    let finalValue = 0;
                    /* let igstTax = parseFloat(gstTax).toFixed(fixDigit); */
                    let igstTax = parseFloat(gstTax);
                    finalValue = ((subTotal * igstTax) / 100).toFixed(fixDigit);
                    if (finalValue == "NaN") {
                        finalValue = 0;
                    }
                    integratedTaxCalculationValue.val(parseFloat(finalValue));
                    centralTaxCalculationValue.val(0);
                    stateTaxCalculationValue.val(0);
                    totalIgstTax += parseFloat(finalValue);
                    itemIgstTax += parseFloat(finalValue);
                    isShippingPackingTaxCalculation = true;
                }
                if (itemCgstTax !== 0 || itemSgstTax !== 0 || itemIgstTax !== 0) {
                    roundOffAmount += subTotal + itemCgstTax + itemSgstTax + itemIgstTax - subTotalAmount;
                }
            });
        } else {
            $(ledgerWithoutGstClass).each(function () {
                let elementId = $(this).attr("ledger-data-rate-with-out-gst-amount");
                let rowElement = $('.ledger-' + elementId);
                let gstTax = rowElement.find("[ledger-data-gst-tax-value=" + elementId + "]").val();
                let withOutGstAmountElement = rowElement.find("[ledger-data-rate-with-out-gst-amount=" + elementId + "]");
                let discountTypeElement = rowElement.find("[ledger-data-discount-type=" + elementId + "]");
                let discountValueElement = rowElement.find("[ledger-data-discount-value=" + elementId + "]");
                let centralTaxCalculationValue = rowElement.find("[ledger-data-classification-cgst-tax=" + elementId + "]");
                let stateTaxCalculationValue = rowElement.find("[ledger-data-classification-sgst-tax=" + elementId + "]");
                let integratedTaxCalculationValue = rowElement.find("[ledger-data-classification-igst-tax=" + elementId + "]");
                let subTotalAmount = rowElement.find("[ledger-data-hidden-sub-total-value=" + elementId + "]").val();
                let withOutAmount = withOutGstAmountElement.val();
                let discountType = discountTypeElement.val();

                let totalDiscountAmount = 0;
                if (discountType == 1 && discountValueElement.val() != 0) {
                    totalDiscountAmount = discountValueElement.val();
                }
                if (discountType == 2 && discountValueElement.val() != 0) {
                    totalDiscountAmount = (withOutAmount * discountValueElement.val()) / 100;
                }

                let subTotal = withOutAmount - totalDiscountAmount;
                let itemCgstTax = 0;
                let itemSgstTax = 0;
                let itemIgstTax = 0;
                if ($.inArray(classificationNatureType, intraStateArray) > -1) {
                    let finalValue = 0;
                    if (isRcmApplicable == 1) {
                        centralTaxCalculationValue.val(finalValue);
                        stateTaxCalculationValue.val(finalValue);
                    } else {
                        /* let cgstTax = (parseFloat(gstTax) / 2).toFixed(fixDigit); */
                        let cgstTax = (parseFloat(gstTax) / 2);
                        finalValue = ((subTotal * cgstTax) / 100).toFixed(fixDigit);
                        if (finalValue == "NaN") {
                            finalValue = 0;
                        }
                        centralTaxCalculationValue.val(parseFloat(finalValue));
                        stateTaxCalculationValue.val(parseFloat(finalValue));
                        integratedTaxCalculationValue.val(0);
                        isShippingPackingTaxCalculation = true;
                    }
                    totalCgstTax += parseFloat(finalValue);
                    totalSgstTax += parseFloat(finalValue);
                    itemCgstTax += parseFloat(finalValue);
                    itemSgstTax += parseFloat(finalValue);
                }
                if ($.inArray(classificationNatureType, interStateArray) > -1) {
                    let finalValue = 0;
                    if (isRcmApplicable == 1) {
                        integratedTaxCalculationValue.val(finalValue);
                    } else {
                        /* let igstTax = parseFloat(gstTax).toFixed(fixDigit); */
                        let igstTax = parseFloat(gstTax);
                        finalValue = ((subTotal * igstTax) / 100).toFixed(fixDigit);
                        if (finalValue == "NaN") {
                            finalValue = 0;
                        }
                        integratedTaxCalculationValue.val(finalValue);
                        centralTaxCalculationValue.val(0);
                        stateTaxCalculationValue.val(0);
                    }
                    totalIgstTax += parseFloat(finalValue);
                    itemIgstTax += parseFloat(finalValue);
                    isShippingPackingTaxCalculation = true;
                }
                if (
                    $.inArray(classificationNatureType, exportArray) > -1 ||
                    $.inArray(classificationNatureType, sezArray) > -1
                ) {
                    /* let igstTax = parseFloat(gstTax).toFixed(fixDigit); */
                    let igstTax = parseFloat(gstTax);
                    let finalValue = ((subTotal * igstTax) / 100).toFixed(fixDigit);
                    if (finalValue == "NaN") {
                        finalValue = 0;
                    }
                    integratedTaxCalculationValue.val(parseFloat(finalValue));
                    centralTaxCalculationValue.val(0);
                    stateTaxCalculationValue.val(0);
                    totalIgstTax += parseFloat(finalValue);
                    itemIgstTax += parseFloat(finalValue);
                    isShippingPackingTaxCalculation = true;
                }
                if (classificationNatureType == "Deemed Export - Intrastate") {
                    let finalValue = 0;
                    /* let cgstTax = (parseFloat(gstTax) / 2).toFixed(fixDigit); */
                    let cgstTax = (parseFloat(gstTax) / 2);
                    finalValue = ((subTotal * cgstTax) / 100).toFixed(fixDigit);
                    if (finalValue == "NaN") {
                        finalValue = 0;
                    }
                    centralTaxCalculationValue.val(finalValue);
                    stateTaxCalculationValue.val(finalValue);
                    integratedTaxCalculationValue.val(0);
                    totalCgstTax += parseFloat(finalValue);
                    totalSgstTax += parseFloat(finalValue);
                    itemCgstTax += parseFloat(finalValue);
                    itemSgstTax += parseFloat(finalValue);
                    isShippingPackingTaxCalculation = true;
                }
                if (classificationNatureType == "Deemed Export - Interstate") {
                    let finalValue = 0;
                    /* let igstTax = parseFloat(gstTax).toFixed(fixDigit); */
                    let igstTax = parseFloat(gstTax);
                    finalValue = ((subTotal * igstTax) / 100).toFixed(fixDigit);
                    if (finalValue == "NaN") {
                        finalValue = 0;
                    }
                    integratedTaxCalculationValue.val(finalValue);
                    centralTaxCalculationValue.val(0);
                    stateTaxCalculationValue.val(0);
                    totalIgstTax += parseFloat(finalValue);
                    itemIgstTax += parseFloat(finalValue);
                    isShippingPackingTaxCalculation = true;
                }
                if (itemCgstTax !== 0 || itemSgstTax !== 0 || itemIgstTax !== 0) {
                    roundOffAmount += subTotal + itemCgstTax + itemSgstTax + itemIgstTax - subTotalAmount;
                }
            });
        }
        // $('.round-off-amount').val(roundOffAmount.toFixed(fixDigit));
        if (!isShippingPackingTaxCalculation) {
            $(".hidden-shipping-charge-sgst-tax-amount").val(0);
            $(".hidden-shipping-charge-cgst-tax-amount").val(0);
            $(".hidden-shipping-charge-igst-tax-amount").val(0);
            let withOutGstAmount = $(".change-shipping-fright-charge").val();
            if (withOutGstAmount == "") {
                withOutGstAmount = 0.0;
            }
            $(".shipping-freight-with-gst").text(window.getCurrencySymbol + " " + withOutGstAmount);
            $(".hidden-shipping-freight-with-gst").val(withOutGstAmount);
        }
        let shippingSgstAmount = $(".hidden-shipping-charge-sgst-tax-amount").val();
        let shippingCgstAmount = $(".hidden-shipping-charge-cgst-tax-amount").val();
        let shippingIgstAmount = $(".hidden-shipping-charge-igst-tax-amount").val();
        shippingSgstAmount = parseFloat(shippingSgstAmount);
        shippingCgstAmount = parseFloat(shippingCgstAmount);
        shippingIgstAmount = parseFloat(shippingIgstAmount);
        if (isNaN(shippingSgstAmount)) {
            shippingSgstAmount = 0;
        }
        if (isNaN(shippingCgstAmount)) {
            shippingCgstAmount = 0;
        }
        if (isNaN(shippingIgstAmount)) {
            shippingIgstAmount = 0;
        }
        totalSgstTax += shippingSgstAmount ?? 0;
        totalCgstTax += shippingCgstAmount ?? 0;
        totalIgstTax += shippingIgstAmount ?? 0;
        if (!isShippingPackingTaxCalculation) {
            $(".hidden-packing-charge-sgst-tax-amount").val(0);
            $(".hidden-packing-charge-cgst-tax-amount").val(0);
            $(".hidden-packing-charge-igst-tax-amount").val(0);
            let withOutGstAmount = $(".change-packing-charge").val();
            if (withOutGstAmount == "") {
                withOutGstAmount = 0.0;
            }
            $(".packing-charge-with-gst").text(window.getCurrencySymbol + " " + withOutGstAmount);
            $(".hidden-packing-charge-with-gst").val(withOutGstAmount);
        }
        let packingSgstAmount = $(".hidden-packing-charge-sgst-tax-amount").val();
        let packingCgstAmount = $(".hidden-packing-charge-cgst-tax-amount").val();
        let packingIgstAmount = $(".hidden-packing-charge-igst-tax-amount").val();
        packingSgstAmount = parseFloat(packingSgstAmount);
        packingCgstAmount = parseFloat(packingCgstAmount);
        packingIgstAmount = parseFloat(packingIgstAmount);
        if (isNaN(packingSgstAmount)) {
            packingSgstAmount = 0;
        }
        if (isNaN(packingCgstAmount)) {
            packingCgstAmount = 0;
        }
        if (isNaN(packingIgstAmount)) {
            packingIgstAmount = 0;
        }
        totalSgstTax += packingSgstAmount ?? 0;
        totalCgstTax += packingCgstAmount ?? 0;
        totalIgstTax += packingIgstAmount ?? 0;
        $("#totalIgstTax").val(totalIgstTax.toFixed(fixDigit));
        $("#totalSgstTax").val(totalSgstTax.toFixed(fixDigit));
        $("#totalCgstTax").val(totalCgstTax.toFixed(fixDigit));
        $(".total-igst-tax").text(totalIgstTax.toFixed(fixDigit));
        $(".total-sgst-tax").text(totalSgstTax.toFixed(fixDigit));
        $(".total-cgst-tax").text(totalCgstTax.toFixed(fixDigit));
    }
};

window.getValueOfIsCgstSgstIgstCalculated = function (classificationNatureType, isRcmApplicable, isAllGstNull = false) {
    let isCgstSgstIgstCalculated = false;

    if (isAllGstNull) {
        return 0;
    }

    if ($.inArray(classificationNatureType, intraStateArray) > -1) {
        isCgstSgstIgstCalculated = isRcmApplicable != 1;
    }
    if ($.inArray(classificationNatureType, interStateArray) > -1) {
        isCgstSgstIgstCalculated = isRcmApplicable != 1;
    }
    if ($.inArray(classificationNatureType, exportArray) > -1 || $.inArray(classificationNatureType, sezArray) > -1) {
        isCgstSgstIgstCalculated = true;
    }
    if (classificationNatureType == "Deemed Export - Intrastate") {
        isCgstSgstIgstCalculated = true;
    }
    if (classificationNatureType == "Deemed Export - Interstate") {
        isCgstSgstIgstCalculated = true;
    }

    if ($.inArray(classificationNatureType, purchaseIntraStateTaxArray) > -1) {
        isCgstSgstIgstCalculated = true;
    }
    if ($.inArray(classificationNatureType, purchaseInterstateTaxArray) > -1) {
        isCgstSgstIgstCalculated = true;
    }
    if ($.inArray(classificationNatureType, exportArray) > -1) {
        isCgstSgstIgstCalculated = false;
    }
    if ($.inArray(classificationNatureType, sezArray) > -1) {
        isCgstSgstIgstCalculated = false;
    }

    return isCgstSgstIgstCalculated ? 1 : 0;
};

window.totalPurchaseClassificationCalculation = function (rateWithGstClass, ledgerWithoutGstClass) {
    let classificationNatureType = $("#mainClassificationNatureType").val();
    if ($("#isCompanyGstApplicable").val() == 1) {
        let totalCgstTax = 0.0;
        let totalSgstTax = 0.0;
        let totalIgstTax = 0.0;
        let roundOffAmount = 0.0;
        let isSHippingAndPackingChargeTaxAmountCalculation = false;

        if ($(".item-accounting-type:checked").val() == 2) {
            $(rateWithGstClass).each(function () {
                let elementId = $(this).attr("data-rpu-without-gst-amount");
                let rowElement = $('.item-' + elementId);
                let gstTax = rowElement.find("[data-gst-tax-value=" + elementId + "]").val();
                let quantityElement = rowElement.find("[data-quantity=" + elementId + "]");
                let quantity = quantityElement.val();
                let withoutGstElement = rowElement.find("[data-rpu-without-gst-amount=" + elementId + "]");
                let discountTypeElement = rowElement.find("[data-discount-type=" + elementId + "]");
                let discountType = discountTypeElement.val();
                let discountValueElement = rowElement.find("[data-discount-value=" + elementId + "]");
                let centralTaxCalculationValue = rowElement.find("[data-classification-cgst-tax=" + elementId + "]");
                let stateTaxCalculationValue = rowElement.find("[data-classification-sgst-tax=" + elementId + "]");
                let integratedTaxCalculationValue = rowElement.find("[data-classification-igst-tax=" + elementId + "]");
                let subTotalAmount = rowElement.find("[data-hidden-sub-total-value=" + elementId + "]").val();
                let totalDiscountAmount = 0;
                if (discountType == 1) {
                    totalDiscountAmount = discountValueElement.val() * quantity;
                }
                if (discountType == 2) {
                    totalDiscountAmount = (withoutGstElement.val() * discountValueElement.val()) / 100;
                    totalDiscountAmount = totalDiscountAmount * quantity;
                }
                let subTotal = quantity * withoutGstElement.val() - totalDiscountAmount;
                let itemCgstTax = 0;
                let itemSgstTax = 0;
                let itemIgstTax = 0;
                centralTaxCalculationValue.val(0);
                stateTaxCalculationValue.val(0);
                integratedTaxCalculationValue.val(0);

                if ($.inArray(classificationNatureType, purchaseIntraStateTaxArray) > -1) {
                    /* let cgstTax = (parseFloat(gstTax) / 2).toFixed(fixDigit); */
                    let cgstTax = (parseFloat(gstTax) / 2);
                    let finalValue = ((subTotal * cgstTax) / 100).toFixed(fixDigit);
                    if (finalValue == "NaN") {
                        finalValue = 0;
                    }
                    centralTaxCalculationValue.val(parseFloat(finalValue));
                    stateTaxCalculationValue.val(parseFloat(finalValue));
                    integratedTaxCalculationValue.val(0);

                    totalCgstTax += parseFloat(finalValue);
                    totalSgstTax += parseFloat(finalValue);
                    itemCgstTax += parseFloat(finalValue);
                    itemSgstTax += parseFloat(finalValue);
                    isSHippingAndPackingChargeTaxAmountCalculation = true;
                }
                if ($.inArray(classificationNatureType, purchaseInterstateTaxArray) > -1) {
                    /* let igstTax = parseFloat(gstTax).toFixed(fixDigit); */
                    let igstTax = parseFloat(gstTax);
                    let finalValue = ((subTotal * igstTax) / 100).toFixed(fixDigit);
                    if (finalValue == "NaN") {
                        finalValue = 0;
                    }
                    integratedTaxCalculationValue.val(parseFloat(finalValue));
                    centralTaxCalculationValue.val(0);
                    stateTaxCalculationValue.val(0);
                    totalIgstTax += parseFloat(finalValue);
                    itemIgstTax += parseFloat(finalValue);
                    isSHippingAndPackingChargeTaxAmountCalculation = true;
                }
                if (itemCgstTax !== 0 || itemSgstTax !== 0 || itemIgstTax !== 0) {
                    roundOffAmount += subTotal + itemCgstTax + itemSgstTax + itemIgstTax - subTotalAmount;
                }
            });
        } else {
            $(ledgerWithoutGstClass).each(function () {
                let elementId = $(this).attr("ledger-data-rate-with-out-gst-amount") ?? $(this).attr("ledger-data-rate-with-gst-amount");
                let rowElement = $('.ledger-' + elementId);
                let gstTax = rowElement.find("[ledger-data-gst-tax-value=" + elementId + "]").val();
                let withOutGstAmountElement = rowElement.find("[ledger-data-rate-with-out-gst-amount=" + elementId + "]");
                let discountTypeElement = rowElement.find("[ledger-data-discount-type=" + elementId + "]");
                let discountValueElement = rowElement.find("[ledger-data-discount-value=" + elementId + "]");
                let centralTaxCalculationValue = rowElement.find("[ledger-data-classification-cgst-tax=" + elementId + "]");
                let stateTaxCalculationValue = rowElement.find("[ledger-data-classification-sgst-tax=" + elementId + "]");
                let integratedTaxCalculationValue = rowElement.find("[ledger-data-classification-igst-tax=" + elementId + "]");
                let withOutAmount = withOutGstAmountElement.val();
                let discountType = discountTypeElement.val();
                let totalDiscountAmount = 0;
                if (discountType == 1 && discountValueElement.val() != 0) {
                    totalDiscountAmount = discountValueElement.val();
                }
                if (discountType == 2 && discountValueElement.val() != 0) {
                    totalDiscountAmount =
                        (withOutAmount * discountValueElement.val()) / 100;
                }

                let subTotal = withOutAmount - totalDiscountAmount;
                let subTotalAmount = rowElement.find("[ledger-data-hidden-sub-total-value=" + elementId + "]").val();
                let itemCgstTax = 0;
                let itemSgstTax = 0;
                let itemIgstTax = 0;
                if ($.inArray(classificationNatureType, purchaseIntraStateTaxArray) > -1) {
                    /* let cgstTax = (parseFloat(gstTax) / 2).toFixed(fixDigit); */
                    let cgstTax = (parseFloat(gstTax) / 2);
                    let finalValue = ((subTotal * cgstTax) / 100).toFixed(fixDigit);
                    if (finalValue == "NaN") {
                        finalValue = 0;
                    }
                    centralTaxCalculationValue.val(parseFloat(finalValue));
                    stateTaxCalculationValue.val(parseFloat(finalValue));
                    integratedTaxCalculationValue.val(0);
                    totalCgstTax += parseFloat(finalValue);
                    totalSgstTax += parseFloat(finalValue);
                    itemCgstTax += parseFloat(finalValue);
                    itemSgstTax += parseFloat(finalValue);
                    isSHippingAndPackingChargeTaxAmountCalculation = true;
                }
                if ($.inArray(classificationNatureType, purchaseInterstateTaxArray) > -1) {
                    /* let igstTax = parseFloat(gstTax).toFixed(fixDigit); */
                    let igstTax = parseFloat(gstTax);
                    let finalValue = ((subTotal * igstTax) / 100).toFixed(fixDigit);
                    if (finalValue == "NaN") {
                        finalValue = 0;
                    }
                    integratedTaxCalculationValue.val(parseFloat(finalValue));
                    centralTaxCalculationValue.val(0);
                    stateTaxCalculationValue.val(0);
                    totalIgstTax += parseFloat(finalValue);
                    itemIgstTax += parseFloat(finalValue);
                    isSHippingAndPackingChargeTaxAmountCalculation = true;
                }
                if ($.inArray(classificationNatureType, exportArray) > -1) {
                    //TO DO
                    isSHippingAndPackingChargeTaxAmountCalculation = false;
                }
                if ($.inArray(classificationNatureType, sezArray) > -1) {
                    //TO DO
                    isSHippingAndPackingChargeTaxAmountCalculation = false;
                }
                if (itemCgstTax !== 0 || itemSgstTax !== 0 || itemIgstTax !== 0) {
                    roundOffAmount += subTotal + itemCgstTax + itemSgstTax + itemIgstTax - subTotalAmount;
                }
            });
        }
        // $('.round-off-amount').val(roundOffAmount.toFixed(fixDigit));
        if (!isSHippingAndPackingChargeTaxAmountCalculation) {
            $(".hidden-shipping-charge-sgst-tax-amount").val(0);
            $(".hidden-shipping-charge-cgst-tax-amount").val(0);
            $(".hidden-shipping-charge-igst-tax-amount").val(0);
            let withOutGstAmount = $(".change-shipping-fright-charge").val();
            if (withOutGstAmount == "") {
                withOutGstAmount = 0.0;
            }
            $(".shipping-freight-with-gst").text(
                window.getCurrencySymbol + " " + withOutGstAmount
            );
            $(".hidden-shipping-freight-with-gst").val(withOutGstAmount);
        }
        let shippingSgstAmount = $(".hidden-shipping-charge-sgst-tax-amount").val();
        let shippingCgstAmount = $(".hidden-shipping-charge-cgst-tax-amount").val();
        let shippingIgstAmount = $(".hidden-shipping-charge-igst-tax-amount").val();
        shippingSgstAmount = parseFloat(shippingSgstAmount);
        shippingCgstAmount = parseFloat(shippingCgstAmount);
        shippingIgstAmount = parseFloat(shippingIgstAmount);
        if (isNaN(shippingSgstAmount)) {
            shippingSgstAmount = 0;
        }
        if (isNaN(shippingCgstAmount)) {
            shippingCgstAmount = 0;
        }
        if (isNaN(shippingIgstAmount)) {
            shippingIgstAmount = 0;
        }
        totalSgstTax += shippingSgstAmount ?? 0;
        totalCgstTax += shippingCgstAmount ?? 0;
        totalIgstTax += shippingIgstAmount ?? 0;
        if (!isSHippingAndPackingChargeTaxAmountCalculation) {
            $(".hidden-packing-charge-sgst-tax-amount").val(0);
            $(".hidden-packing-charge-cgst-tax-amount").val(0);
            $(".hidden-packing-charge-igst-tax-amount").val(0);
            let withOutGstAmount = $(".change-packing-charge").val();
            if (withOutGstAmount == "") {
                withOutGstAmount = 0.0;
            }
            $(".packing-charge-with-gst").text(window.getCurrencySymbol + " " + withOutGstAmount);
            $(".hidden-packing-charge-with-gst").val(withOutGstAmount);
        }
        let packingSgstAmount = $(".hidden-packing-charge-sgst-tax-amount").val();
        let packingCgstAmount = $(".hidden-packing-charge-cgst-tax-amount").val();
        let packingIgstAmount = $(".hidden-packing-charge-igst-tax-amount").val();
        packingSgstAmount = parseFloat(packingSgstAmount);
        packingCgstAmount = parseFloat(packingCgstAmount);
        packingIgstAmount = parseFloat(packingIgstAmount);
        if (isNaN(packingSgstAmount)) {
            packingSgstAmount = 0;
        }
        if (isNaN(packingCgstAmount)) {
            packingCgstAmount = 0;
        }
        if (isNaN(shippingIgstAmount)) {
            packingIgstAmount = 0;
        }
        totalSgstTax += packingSgstAmount ?? 0;
        totalCgstTax += packingCgstAmount ?? 0;
        totalIgstTax += packingIgstAmount ?? 0;
        $("#totalIgstTax").val(totalIgstTax.toFixed(fixDigit));
        $("#totalSgstTax").val(totalSgstTax.toFixed(fixDigit));
        $("#totalCgstTax").val(totalCgstTax.toFixed(fixDigit));
        $(".total-igst-tax").text(totalIgstTax.toFixed(fixDigit));
        $(".total-sgst-tax").text(totalSgstTax.toFixed(fixDigit));
        $(".total-cgst-tax").text(totalCgstTax.toFixed(fixDigit));
    }
};

window.gstNotEnabledCalculationLedgerSubTotal = function (elementId) {
    let rowElement = $('.ledger-' + elementId);
    let amountElement = rowElement.find("[ledger-data-amount=" + elementId + "]");
    let discountTypeElement = rowElement.find("[ledger-data-discount-type=" + elementId + "]");
    let discountValueElement = rowElement.find("[ledger-data-discount-value=" + elementId + "]");

    let amountValue = amountElement.val();
    let discountType = discountTypeElement.val();

    let totalDiscountAmount = 0;
    if (discountType == 1 && discountValueElement.val() != 0) {
        totalDiscountAmount = discountValueElement.val();
    }
    if (discountType == 2 && discountValueElement.val() != 0) {
        totalDiscountAmount = (amountValue * discountValueElement.val()) / 100;
    }
    let subTotal = amountValue - totalDiscountAmount;

    let taxableAmount = amountValue - totalDiscountAmount;

    rowElement.find("[ledger-data-total-discount-amount-value=" + elementId + "]").val(totalDiscountAmount);
    rowElement.find("[ledger-data-sub-total-value=" + elementId + "]").text(subTotal.toFixed(fixDigit));
    rowElement.find("[ledger-data-hidden-sub-total-value=" + elementId + "]").val(subTotal.toFixed(fixDigit));
    rowElement.find("[ledger-data-hidden-tds-taxable-value=" + elementId + "]").val(taxableAmount.toFixed(fixDigit));

    calculateTCSAmount();
    grandTotalCalculation();
};

window.calculationLedgerSubTotal = function (elementId) {
    let rowElement = $('.ledger-' + elementId);

    let withOutGstAmountElement = rowElement.find("[ledger-data-rate-with-out-gst-amount=" + elementId + "]");
    let discountTypeElement = rowElement.find("[ledger-data-discount-type=" + elementId + "]");
    let discountValueElement = rowElement.find("[ledger-data-discount-value=" + elementId + "]");
    let gstTaxPercentageElement = rowElement.find("[ledger-data-gst-tax-value=" + elementId + "]");

    let withOutAmount = withOutGstAmountElement.val();
    let discountType = discountTypeElement.val();

    let totalDiscountAmount = 0;
    if (discountType == 1 && discountValueElement.val() != 0) {
        totalDiscountAmount = discountValueElement.val();
    }
    if (discountType == 2 && discountValueElement.val() != 0) {
        totalDiscountAmount = (withOutAmount * discountValueElement.val()) / 100;
    }
    let subTotal = withOutAmount - totalDiscountAmount;
    let taxableAmount = withOutAmount - totalDiscountAmount;

    // if($('#isCgstSgstIgstCalculated').val() == 1) {
    //     let gstTaxAmount = subTotal * gstTaxPercentageElement.val() / 100;
    //     subTotal += gstTaxAmount;
    // }

    rowElement.find("[ledger-data-total-discount-amount-value=" + elementId + "]").val(totalDiscountAmount);
    rowElement.find("[ledger-data-sub-total-value=" + elementId + "]").text(subTotal.toFixed(fixDigit));
    rowElement.find("[ledger-data-hidden-sub-total-value=" + elementId + "]").val(subTotal.toFixed(fixDigit));
    rowElement.find("[ledger-data-hidden-tds-taxable-value=" + elementId + "]").val(taxableAmount.toFixed(fixDigit));

    calculateTCSAmount();
    changeShippingFrightCharge();
    changePackingCharge();
    grandTotalCalculation();
};

window.enabledConfiguration = function (data) {
    data.is_enabled_shipping_address
        ? $(".enabled-shipping-address").removeClass("d-none")
        : $(".enabled-shipping-address").addClass("d-none");

    data.is_enabled_broker_details
        ? $(".enabled-broker-details").removeClass("d-none")
        : $(".enabled-broker-details").addClass("d-none");

    data.is_enabled_credit_period_details
        ? $(".enabled-credit-period-details").removeClass("d-none")
        : $(".enabled-credit-period-details").addClass("d-none");

    data.is_enabled_transport_details
        ? $(".enabled-transport-details").removeClass("d-none")
        : $(".enabled-transport-details").addClass("d-none");

    data.is_enabled_tcs_details
        ? $(".enabled-tcs-details").removeClass("d-none")
        : $(".enabled-tcs-details").addClass("d-none");

    data.is_enabled_po_details_of_buyer
        ? $(".enabled-po-details").removeClass("d-none")
        : $(".enabled-po-details").addClass("d-none");

    data.is_additional_item_description
        ? $(".enabled-item-description").removeClass("d-none")
        : $(".enabled-item-description").addClass("d-none");

    data.is_additional_item_description
        ? $(".hidden-enabled-item-description").val(1)
        : $(".hidden-enabled-item-description").val(0);

    data.is_additional_ledger_description
        ? $(".ledger-enabled-description").removeClass("d-none")
        : $(".ledger-enabled-description").addClass("d-none");

    data.is_additional_ledger_description
        ? $(".ledger-hidden-enabled-description").val(1)
        : $(".ledger-hidden-enabled-description").val(0);

    data.is_enable_narration
        ? $(".enable-narration").removeClass("d-none")
        : $(".enable-narration").addClass("d-none");

    data.consolidating_items_to_invoice
        ? $("#consolidatingItemsToInvoice").val(1)
        : $("#consolidatingItemsToInvoice").val(0);

    data.is_change_gst_details
        ? $(".is-change-gst-details").val(1)
        : $(".is-change-gst-details").val(0);

    data.warn_on_negative_stock
        ? $(".warn-on-negative-stock").val(1)
        : $(".warn-on-negative-stock").val(0);

    data.enable_payment_mode
        ? $(".enable-payment-mode").val(1)
        : $(".enable-payment-mode").val(0);

    data.enable_payment_mode
        ? $(".enable-payment-mode").removeClass("d-none")
        : $(".enable-payment-mode").addClass("d-none");

    data.enable_reference_number
        ? $(".enable-reference-number").val(1)
        : $(".enable-reference-number").val(0);

    data.enable_reference_number
        ? $(".enable-reference-number").removeClass("d-none")
        : $(".enable-reference-number").addClass("d-none");

    data.is_enabled_estimate_quote
        ? $(".enabled-estimate-quote-details").removeClass("d-none")
        : $(".enabled-estimate-quote-details").addClass("d-none");

    data.is_enabled_delivery_challan
        ? $(".enabled-delivery-challan-details").removeClass("d-none")
        : $(".enabled-delivery-challan-details").addClass("d-none");
    if (typeof data.is_enabled_mrp != 'undefined') {
        data.is_enabled_mrp
            ? $(".enabled-mrp").removeClass("d-none")
            : $(".enabled-mrp").addClass("d-none");

        data.is_enabled_mrp
            ? $(".change-mrp").attr("disabled", false)
            : $(".change-mrp").attr("disabled", true);

        data.is_enabled_mrp
            ? ($(".item-name-select").removeClass("col-3"), $(".item-name-select").addClass("col-2"))
            : ($(".item-name-select").removeClass("col-2"), $(".item-name-select").addClass("col-3"));
    }
};

window.hideBackButtonOnModel = function () {
    let currentURL = window.location.href;
    let params = currentURL.split("/").slice(-2)[0]; // Create screen URL
    let paramsEdit = currentURL.split("/").slice(-3)[0]; // Edit screen URL

    let urlParams = [
        "sales",
        "sales-create",
        "sale-returns",
        "sale-returns-create",
        "income-debit-notes",
        "income-debit-notes-create",
        "income-credit-notes",
        "income-credit-notes-create",
        "purchases",
        "purchases-create",
        "purchase-create",
        "purchase-returns",
        "purchase-returns-create",
        "expense-debit-notes",
        "expense-debit-notes-create",
        "expense-credit-notes",
        "expense-credit-notes-create",
        "transaction-journal",
        "transaction-receipt",
        "transaction-payment",
        "income-estimate-quote",
        "income-estimate-quote-create",
        "purchase-order",
        "purchase-order-create",
        "delivery-challan",
        "create-sale-delivery-challan",
    ];

    let showBackButton = true;
    if ($.inArray(params, urlParams) > -1) {
        showBackButton = false;
    }

    if ($.inArray(paramsEdit, urlParams) > -1) {
        showBackButton = false;
    }

    if (!showBackButton) {
        $(".common-ledger-back-btn").each(function () {
            $(this).addClass("d-none");
        });
        $(".screen-field-modal-screen").removeClass("card-border-1");
        $(".screen-field-modal-screen").removeClass("card-body");
        // $(".screen-field-modal-screen").parent().parent().prepend("<hr/>");
    } else {
        $(".common-ledger-back-btn").each(function () {
            $(this).removeClass("d-none");
        });
    }

    $("[data-control=select2]").each(function () {
        let element = $(this);
        if (element.parents(".modal.fade").length) {
            let modalID = element.parents(".modal.fade").attr("id");
            element.select2({
                dropdownParent: $(`#${modalID}`),
            });
        }
    });
};

listen("focus", ".change-quantity-modal", function () {
    let consolidatingItemInvoiceHTML = prepareTemplateRender("#consolidatingItemInvoiceDiv");

    $(".append-consolidating-item-invoice-quantity").empty().append(consolidatingItemInvoiceHTML);
    if ($("#consolidatingItemsToInvoice").val() == 1 || $("#consolidatingItemsToInvoice").val() != 0) {
        let elementId = $(this).attr("data-quantity");
        let rowElement = $('.item-' + elementId);
        $("#addConsolidatingItemInvoice").appendTo("body").modal("show");
        $("#consolidatingItemInvoiceElementId").val(elementId);

        let editQuantityString = rowElement.find("[data-consolidating-quantity=" + elementId + "]").val();
        if (editQuantityString != "" && editQuantityString != undefined) {
            let editQuantityArray = editQuantityString.split(",");
            let editQuantityArrayLength = editQuantityArray.length;
            let totalRaw = Math.floor(editQuantityArrayLength / 4);
            for (let i = 1; i < totalRaw; i++) {
                $("#addConsolidatingItemInvoiceRow").trigger("click");
            }

            $("[name=consolidating_item_invoice_quantity]").each(function (index, value) {
                $(this).val(editQuantityArray[index] ?? 0);
                $(this).attr('min', editQuantityArray[index] > 0 ? rowElement.find("[data-decimal-for-quantity=" + elementId + "]").val() : 0);
                $(this).attr('step', editQuantityArray[index] > 0 ? rowElement.find("[data-decimal-for-quantity=" + elementId + "]").val() : 0.01);
            });
        } else {
            let editQuantityArray = { 0: $(this).val() };
            for (let i = 1; i < 1; i++) {
                $("#addConsolidatingItemInvoiceRow").trigger("click");
            }

            $("[name=consolidating_item_invoice_quantity]").each(function (index, value) {
                $(this).val(editQuantityArray[index] ?? 0);
            });
        }
    }
});

listenClick("#addConsolidatingItemInvoiceRow", function () {
    let consolidatingItemInvoiceHTML = prepareTemplateRender(
        "#consolidatingItemInvoiceDiv"
    );

    $(".append-consolidating-item-invoice-quantity").append(
        consolidatingItemInvoiceHTML
    );
});

listenKeyup("[name=consolidating_item_invoice_quantity]", function () {
    let input = $(this);
    let elementId = input.closest('.append-consolidating-item-invoice-quantity').prev().find('#consolidatingItemInvoiceElementId').val();

    let minValue = $("[data-decimal-for-quantity=" + elementId + "]").val() || 0;
    let stepValue = $("[data-decimal-for-quantity=" + elementId + "]").val() || 0.01;

    input.attr('min', minValue);
    input.attr('step', stepValue);
});

listenSubmit("#addConsolidatingItemInvoiceForm", function () {
    $("#consolidatingItemInvoiceButton").trigger("click");
});

listenClick("#consolidatingItemInvoiceButton", function () {
    let form = $("#addConsolidatingItemInvoiceForm")[0];

    if (!form.checkValidity()) {
        form.reportValidity();
        return false;
    }

    let totalQuantity = 0;

    let quantityArray = [];
    let elementId = $("#consolidatingItemInvoiceElementId").val();
    let decimalForQtyToFixed = $("[data-decimal-for-quantity=" + elementId + "]").attr('data-decimal-for-quantity-rate');

    let negativeValue = false;
    $("[name=consolidating_item_invoice_quantity]").each(function () {
        if ($(this).val() < 0) {
            displayErrorMessage("Please enter Quantity positive value");
            negativeValue = true;
            return false;
        }
        let quantity = $(this).val() != 0 ? $(this).val() : 0;

        quantityArray.push(parseFloat(quantity));
        totalQuantity += parseFloat(quantity);
    });
    if (negativeValue) {
        return false;
    }
    let quantityString = quantityArray.join(",");
    $(document)
        .find("[data-quantity=" + elementId + "]")
        .val(totalQuantity.toFixed(decimalForQtyToFixed))
        .trigger("keyup");
    $(document)
        .find("[data-consolidating-quantity=" + elementId + "]")
        .val(quantityString);
    $("#addConsolidatingItemInvoice").modal("hide");
    $("#addConsolidatingItemInvoiceForm")[0].reset();
});

function showMultipleInvoiceErorrModal(data) {

    $('.invoive-type-button').attr('data-estimates', data.estimateQuoteIds)
    $('#invoiceTypeTable').empty();
    Object.entries(data.invoiceTypeModalList).forEach(function (item) {
        $('#invoiceTypeTable').append(`
        <tr>
        <td>${item[0]}</td>
        <td>${item[1]}</td>
        </tr>
        `);
    });
    $('#multipleInvoiceErrorModal').appendTo('body').modal('show');
}

function showMultipleInvoiceDataErrorModal(data) {
    $('#dataItemList').empty();
    if (data.matchedInputName.length) {
        let unMatchedFieldMessage = `Different Value Field : ${data.matchedInputName}`;
        $('#diffrentFieldName').text(unMatchedFieldMessage);
    }
    $('#dataItemList').attr({ 'data-estimates': data.estimateQuoteIds, 'data-invoice-type': data.invoiceType });
    Object.entries(data.invoiceNumberModalList).forEach(function ([id, invoice]) {
        $('#dataItemList').append(`
            <tr>
                <th class="text-start">${invoice}</th>
                <td class="ms-5 text-end"><a href="javascript:void(0)" data-id=${id} type="button" class="btn btn-sm btn-primary modal-estimate-invoice">Continue</a></td>
            </tr>`);
    });
    $('#multipleInvoiceDataErrorModal').appendTo('body').modal('show');
}

listenChange(".change-sale-invoice-number", function () {
    let saleTransactionId = $(this).val();
    if (saleTransactionId) {
        screenLock();
        $.ajax({
            type: "GET",
            url: route("company.sales-return.get-sales-transaction", {
                saleTransactionId: saleTransactionId,
            }),
            beforeSend: function () {
                startLoader();
            },
            success: function (data) {
                let result = data.data.saleTransaction;
                if (result.payment_mode == 1) {
                    $("#paymentModCash")
                        .val(1)
                        .prop("checked", true)
                        .trigger("change");
                    $(".sale-append-payment-type-ledger-id")
                        .val(result.payment_type_ledger_id)
                        .trigger("change");
                }
                if (result.payment_mode == 2) {
                    $("#paymentModCredit")
                        .val(2)
                        .prop("checked", true)
                        .trigger("change");
                    $(".sale-append-payment-type-ledger-id")
                        .val("")
                        .trigger("change");
                }

                $(".sale-append-broker-name")
                    .val(result.broker_id)
                    .trigger("change");
                setTimeout(function () {
                    $(".sale-append-broker-name")
                        .val(result.broker_id)
                        .trigger("change");
                }, 700);
                setTimeout(function () {
                    $(".sale-append-brokrage").val(result.brokerage_for_sale);
                    if (result.brokerage_on_value_type == 1) {
                        $(".sale-append-brokerage-on-sale")
                            .val(1)
                            .prop("checked", true);
                    }
                    if (result.brokerage_on_value_type == 2) {
                        $(".sale-append-brokerage-on-purchase")
                            .val(2)
                            .prop("checked", true);
                    }
                }, 2500);

                setTimeout(function () {
                    if ($(".sale-append-credit-period").length) {
                        $(".sale-append-credit-period").val(
                            result.credit_period
                        );
                    }
                }, 700);

                setTimeout(function () {
                    if (result.credit_period_type == 1) {
                        $(".sale-append-credit-period-month")
                            .val(1)
                            .prop("checked", true);
                    }
                    if (result.credit_period_type == 2) {
                        $(".sale-append-credit-period-day")
                            .val(2)
                            .prop("checked", true);
                    }
                }, 1500);

                $(".sale-append-transport-name")
                    .val(result.transport_id)
                    .trigger("change");
                $(".sale-append-document-number").val(
                    result.transporter_document_number
                );
                if ($(".sale-append-po-number").length) {
                    $(".sale-append-po-number").val(result.po_no);
                }
                let saleDate = new Date();
                let transportDate = new Date();
                let poDate = new Date();
                if (result.date && result.date !== null) {
                    saleDate = new Date(result.date);
                    $(".sale-append-date").val(
                        moment(saleDate).format("DD-MM-YYYY")
                    );
                } else {
                    $(".sale-append-date").val("");
                }
                if (
                    result.transporter_document_date &&
                    result.transporter_document_date !== null
                ) {
                    transportDate = new Date(result.transporter_document_date);
                    $(".sale-append-transport-date").val(
                        moment(transportDate).format("DD-MM-YYYY")
                    );
                } else {
                    $(".sale-append-transport-date").val("");
                }
                if (result.transporter_vehicle_number) {
                    $(".transporter-vehicle-number").val(
                        result.transporter_vehicle_number
                    );
                } else {
                    $(".transporter-vehicle-number").val("");
                }
                if (result.po_date && result.po_date !== null) {
                    poDate = new Date(result.po_date);
                    if ($(".sale-append-po-date").length) {
                        $(".sale-append-po-date").flatpickr({
                            allowInput: true,
                            altFormat: "d-m-Y",
                            ariaDateFormat: "d-m-Y",
                            dateFormat: "d-m-Y",
                            defaultDate: moment(poDate).format("DD-MM-YYYY"),
                        });
                    }
                } else {
                    $(".sale-append-po-date").val("");
                }

                //for addresses
                let shippingAddresses = data.data.shippingAddress;
                let billingAddresses = data.data.billingAddress;
                let gstin = result.gstin;
                let shippingGstin = data.data.saleTransaction.shipping_gstin;
                let shippingPartyName = data.data.saleTransaction.shipping_name;

                $(".shipping-address-name").val(shippingPartyName);
                $(".shipping-address-gstin").val(shippingGstin);
                appendAddresses(shippingAddresses, billingAddresses, gstin);
                $(".sale-append-tcs-tax-id")
                    .val(result.tcs_tax_id)
                    .trigger("change");
                setTimeout(function () {
                    $(".sale-append-tcs-rate").val(result.tcs_rate);
                }, 1000);
                setTimeout(function () {
                    $(".sale-append-shipping-charge").val(
                        result.shipping_freight
                    );
                    $(".sale-append-packing-charge").val(result.packing_charge);
                    $(".sale-append-shipping-charge-with-gst").text(
                        result.shipping_freight_with_gst ?? 0
                    );
                    $(".sale-append-hidden-shipping-charge-with-gst").val(
                        result.shipping_freight_with_gst ?? 0
                    );
                    $(".sale-append-packing-charge-with-gst").text(
                        result.packing_charge_with_gst ?? 0
                    );
                    $(".sale-append-hidden-packing-charge-with-gst").val(
                        result.packing_charge_with_gst ?? 0
                    );
                    $(".hidden-shipping-charge-cgst-tax-amount").val(
                        result.shipping_freight_cgst_amount ?? 0
                    );
                    $(".hidden-shipping-charge-sgst-tax-amount").val(
                        result.shipping_freight_sgst_amount ?? 0
                    );
                    $(".hidden-shipping-charge-igst-tax-amount").val(
                        result.shipping_freight_igst_amount ?? 0
                    );
                    $(".hidden-packing-charge-cgst-tax-amount").val(
                        result.packing_charge_cgst_amount ?? 0
                    );
                    $(".hidden-packing-charge-sgst-tax-amount").val(
                        result.packing_charge_sgst_amount ?? 0
                    );
                    $(".hidden-packing-charge-igst-tax-amount").val(
                        result.packing_charge_igst_amount ?? 0
                    );
                    $(".sale-append-cess-rate").val(result.cess);
                    $(".sale-append-tcs-amount").val(result.tcs_amount);
                    $(".sale-append-cgst-value").val(result.cgst);
                    $(".sale-append-sgst-value").val(result.sgst);
                    $(".sale-append-igst-value").val(result.igst);
                    $(".sale-append-cgst-text").text(result.cgst);
                    $(".sale-append-sgst-text").text(result.sgst);
                    $(".sale-append-igst-text").text(result.igst);
                    $(".sale-append-total-text").text(result.total);
                    $(".sale-append-total-value").val(result.total);
                    $(".sale-append-round-amount").val(
                        result.rounding_amount.toFixed(fixDigit)
                    );
                    $(".sale-append-grand-total-value").val(result.grand_total);
                    $(".sale-append-grand-total-text").text(result.grand_total);
                    $(".sale-append-narration").text(result.narration);
                    $(".sale-append-term-and-condition").text(
                        result.term_and_condition
                    );
                }, 2500);
                $(".sale-append-classification-nature-type").val(
                    data.data.classificationNatureType ?? null
                );
                $(".sale-append-classification-is-rcm-applicable").val(
                    data.data.isRcmApplicable ?? 0
                );
                $("#isCgstSgstIgstCalculated").val(
                    data.data.saleTransaction.is_cgst_sgst_igst_calculated
                );
                $("#isGstNa").val(data.data.saleTransaction.is_gst_na);
                setTimeout(function () {
                    let routeName = null;
                    if ($(".transaction-module-name").val() == "saleReturnTransaction") {
                        routeName = route(
                            "company.sales-return.append-sale-transaction-items",
                            { saleTransactionId: saleTransactionId }
                        );
                    }
                    if ($(".transaction-module-name").val() == "incomeDebitNoteTransaction") {
                        routeName = route(
                            "company.income-debit-notes.append-sale-transaction-items",
                            { saleTransactionId: saleTransactionId }
                        );
                    }
                    if ($(".transaction-module-name").val() == "incomeCreditNoteTransaction") {
                        routeName = route(
                            "company.income-credit.append-sale-transaction-items",
                            { saleTransactionId: saleTransactionId }
                        );
                    }

                    if ($(".transaction-module-name").val() == "deliveryChallanTransaction") {
                        routeName = route("company.delivery-challan.append-sale-transaction-items", { saleTransactionId: saleTransactionId });
                    }

                    if (routeName) {
                        $.ajax({
                            type: "get",
                            url: routeName,
                            success: function (data) {
                                $(".sale-items-append").empty().append(data.data);
                                initSelect2();
                                if ($(".transaction-module-name").val() == "saleReturnTransaction") {
                                    generateUniqueIdForAppendScreenSaleReturn();
                                }
                                if ($(".transaction-module-name").val() == "incomeDebitNoteTransaction") {
                                    generateIncomeDebitNoteUniqueIdForAppendScreen();
                                }
                                if ($(".transaction-module-name").val() == "incomeCreditNoteTransaction") {
                                    generateIncomeCreditNoteUniqueIdForAppendScreen();
                                }
                                if ($(".transaction-module-name").val() == "purchaseReturnTransaction") {
                                    generateUniqueIdForAppendScreenPurchaseReturn();
                                }

                                if ($(".transaction-module-name").val() == "deliveryChallanTransaction") {
                                    generateUniqueIdForAppendDeliveryChallanScreen();
                                }

                            },
                            complete: function () {
                                loadMaxLength();
                                stopLoader();
                                screenUnLock();
                            },
                        });
                    }
                }, 2000);
            },
        });
    } else {
        location.href = window.location.href;
    }
});

function handleInvoiceNumber(eQTransactionIds, invoiceType = null, invoiceNumber = null) {
    $.ajax({
        type: "POST",
        url: route("company.sales.manage-multiple-estimate-quote-transaction", { eQTransactionIds: eQTransactionIds, invoiceType: invoiceType ?? "", invoiceNumber: invoiceNumber ?? "" }),
        beforeSend: function () {
            startLoader();
        },
        success: function (data) {
            let result = data.data;

            if (result.isShowInvoiceTypeModal) {
                showMultipleInvoiceErorrModal(result);
            } else if (result.isShowInvoiceNumberModal) {
                showMultipleInvoiceDataErrorModal(result);
            }

            stopLoader();
            screenUnLock();

            if (!result.isShowInvoiceTypeModal && !result.isShowInvoiceNumberModal) {
                startLoader();
                screenLock();
                $('.change-sale-eq-document-number').val(result.estimateQuoteIds);
                fillSaleTransactionData(result);
                $(".item-append").empty().append(result.html);
                initSelect2();
                generateUniqueIdForAppendScreen();
            }
        }
    });
}

function handleDeliveryChallanNumber(dCTransactionIds, invoiceType = null, invoiceNumber = null) {
    $.ajax({
        type: "POST",
        url: route("company.sales.manage-multiple-delivery-challan-transaction", { dCTransactionIds: dCTransactionIds, invoiceType: invoiceType ?? "", invoiceNumber: invoiceNumber ?? "" }),
        beforeSend: function () {
            startLoader();
        },
        success: function (data) {
            let result = data.data;

            if (result.isShowInvoiceNumberModal) {
                showMultipaleDeliveryChallanDataErrorModal(result);
            }

            stopLoader();
            screenUnLock();

            if (!result.isShowInvoiceNumberModal) {
                startLoader();
                screenLock();
                $(".change-sale-eq-document-number").val(
                    result.estimateQuoteIds
                );
                $(".item-append").empty().append(result.html);
                fillDeliveryChallanDataToSaleTransaction(result);
                initSelect2();
                generateUniqueIdForAppendScreen();
            }
        },
    });
}

function showMultipaleDeliveryChallanDataErrorModal(data) {
    $(".append-delivery-challan-content").text('There is a difference between multiple delivery challan transaction, Choose one transaction.')
    $('#dataItemList').empty();
    if (data.matchedInputName.length) {
        let unMatchedFieldMessage = `Different Value Field : ${data.matchedInputName}`;
        $('#diffrentFieldName').text(unMatchedFieldMessage);
    }
    $('#dataItemList').attr({ 'data-dChallan': data.deliveryChallanIds, 'data-invoice-type': data.invoiceType });
    Object.entries(data.invoiceNumberModalList).forEach(function ([id, invoice]) {
        $('#dataItemList').append(`
            <tr>
                <th class="text-start">${invoice}</th>
                <td class="ms-5 text-end"><a href="javascript:void(0)" data-id=${id} type="button" class="btn btn-sm btn-primary modal-delivery-challan-invoice">Continue</a></td>
            </tr>`);
    });
    $('#multipleInvoiceDataErrorModal').appendTo('body').modal('show');
}

function fillSaleTransactionData(data) {
    let result = data.estimateQuoteTransaction;

    $('#mainClassificationNatureType').val(result.classificationNatureType);
    $('#mainIsRcmApplicable').val(result.isRcmApplicable);

    $(".sale-broker-name").val(result.broker_id).trigger("change");

    setTimeout(function () {
        $(".brokerage-for-sale").val(result.brokerage);
        if (result.brokerage_on_value_type == 1) {
            $(".brokerage-on-sale").val(1).prop("checked", true);
        }
        if (result.brokerage_on_value_type == 2) {
            $(".brokerage-on-purchase").val(2).prop("checked", true);
        }
    }, 2500);

    setTimeout(function () {
        if ($(".credit-period").length) {
            $(".credit-period").val(result.credit_period);
        }
    }, 700);

    setTimeout(function () {
        if (result.credit_period_type == 1) {
            $(".credit-period-month").val(1).prop("checked", true);
        }
        if (result.credit_period_type == 2) {
            $(".credit-period-day").val(2).prop("checked", true);
        }
    }, 1500);

    $(".transport-name").val(result.transport_id).trigger("change");
    $(".transporter-document-number").val(result.transporter_document_number).trigger("change");
    $(".transporter-vehicle-number").val(result.transporter_vehicle_number);
    if ($(".po-no").length) {
        $(".po-no").val(result.po_no);
    } else {
        $(".po-no").val('');
    }

    let transportDate = new Date();
    if (result.transporter_document_date) {
        transportDate = moment(new Date(result.transporter_document_date)).format("DD-MM-YYYY");
    } else {
        transportDate = null;
    }

    $(".transporter-document-date").val(transportDate);

    let poDate = new Date();
    if (result.po_date) {
        poDate = new Date(result.po_date);
    }

    if ($(".po-date").length) {
        $(".po-date").flatpickr({
            allowInput: true,
            altFormat: "d-m-Y",
            ariaDateFormat: "d-m-Y",
            dateFormat: "d-m-Y",
            defaultDate: moment(poDate).format("DD-MM-YYYY"),
        });
    }

    let shippingAddresses = result.shippingAddress;
    let billingAddresses = result.billingAddress;
    let gstin = result.gstin;
    let shippingGstin = result.shipping_gstin
    let shippingName = result.shipping_name

    $('.shipping-address-gstin').val(shippingGstin);
    $('.shipping-address-name').val(shippingName);
    appendAddresses(shippingAddresses, billingAddresses, gstin);
    $(".change-tcs-tax-id").val(result.tcs_tax_id).trigger("change");

    setTimeout(function () {
        $(".tcs-tax-rate").val(result.tcs_rate);
    }, 1000);

    setTimeout(function () {
        $(".change-shipping-fright-charge").val(result.shipping_freight);
        $(".change-packing-charge").val(result.packing_charge);
        $(".shipping-freight-with-gst").text(result.shipping_freight_with_gst ?? 0);
        $(".hidden-shipping-freight-with-gst").val(result.shipping_freight_with_gst ?? 0);
        $(".packing-charge-with-gst").text(result.packing_charge_with_gst ?? 0);
        $(".hidden-packing-charge-with-gst").val(result.packing_charge_with_gst ?? 0);
        $(".hidden-shipping-charge-cgst-tax-amount").val(result.shipping_freight_cgst_amount ?? 0);
        $(".hidden-shipping-charge-sgst-tax-amount").val(result.shipping_freight_sgst_amount ?? 0);
        $(".hidden-shipping-charge-igst-tax-amount").val(result.shipping_freight_igst_amount ?? 0);
        $(".hidden-packing-charge-cgst-tax-amount").val(result.packing_charge_cgst_amount ?? 0);
        $(".hidden-packing-charge-sgst-tax-amount").val(result.packing_charge_sgst_amount ?? 0);
        $(".hidden-packing-charge-igst-tax-amount").val(result.packing_charge_igst_amount ?? 0);
        $(".change-cess-rate").val(data.totalCessRate);
        // $(".hidden-cess-rate").val(data.totalCessRate);
        $(".change-tcs-amount").val(data.tcs_amount);
        $(".narration").text(result.narration);
        $(".term-and-condition").text(result.term_and_condition);
    }, 2000);

    $("#mainClassificationNatureType").val(result.classificationNatureType ?? null);
    $("#mainIsRcmApplicable").val(result.isRcmApplicable ?? 0);
    $("#isCgstSgstIgstCalculated").val(result.is_cgst_sgst_igst_calculated);
    $("#isGstNa").val(result.is_gst_na);

    setTimeout(function () {
        calculateTCSAmount();
        grandTotalCalculation();
        stopLoader();
        screenUnLock();
        loadMaxLength();
    }, 2500);
}

function fillDeliveryChallanDataToSaleTransaction(data) {
    let result = data.deliveryChallanTransaction;

    $("#mainClassificationNatureType").val(null);
    $("#mainIsRcmApplicable").val(result.isRcmApplicable);

    $(".sale-broker-name").val(result.broker_id).trigger("change");

    setTimeout(function () {
        $(".brokerage-for-sale").val(result.brokerage);
        if (result.brokerage_on_value_type == 1) {
            $(".brokerage-on-sale").val(1).prop("checked", true);
        }
        if (result.brokerage_on_value_type == 2) {
            $(".brokerage-on-purchase").val(2).prop("checked", true);
        }
    }, 2500);

    setTimeout(function () {
        if ($(".credit-period").length) {
            $(".credit-period").val(0);
        }
    }, 700);

    setTimeout(function () {
        $(".credit-period-month").val(1).prop("checked", true);
    }, 1500);

    $(".transport-name").val(result.transport_id).trigger("change");
    $(".transporter-document-number")
        .val(result.transporter_document_number)
        .trigger("change");
    $(".transporter-vehicle-number").val(result.transporter_vehicle_number);
    if ($(".po-no").length) {
        $(".po-no").val(result.po_no);
    } else {
        $(".po-no").val("");
    }

    let transportDate = new Date();
    if (result.transporter_document_date) {
        transportDate = moment(
            new Date(result.transporter_document_date)
        ).format("DD-MM-YYYY");
    } else {
        transportDate = null;
    }

    $(".transporter-document-date").val(transportDate);

    let poDate = new Date();
    if (result.po_date) {
        poDate = new Date(result.po_date);
    }

    if ($(".po-date").length) {
        $(".po-date").flatpickr({
            allowInput: true,
            altFormat: "d-m-Y",
            ariaDateFormat: "d-m-Y",
            dateFormat: "d-m-Y",
            defaultDate: moment(poDate).format("DD-MM-YYYY"),
        });
    }

    let shippingAddresses = result.shippingAddress;
    let billingAddresses = result.billingAddress;
    let gstin = result.gstin;
    let shippingGstin = result.shipping_gstin
    let shippingName = result.shipping_name

    $('.shipping-address-gstin').val(shippingGstin);
    $('.shipping-address-name').val(shippingName);
    appendAddresses(shippingAddresses, billingAddresses, gstin);
    $(".change-tcs-tax-id").val('').trigger("change");

    setTimeout(function () {
        $(".tcs-tax-rate").val('');
    }, 1000);

    setTimeout(function () {
        $(".change-shipping-fright-charge").val(0);
        $(".change-packing-charge").val(0);
        $(".shipping-freight-with-gst").text(0);
        $(".hidden-shipping-freight-with-gst").val(0);
        $(".packing-charge-with-gst").text(0);
        $(".hidden-packing-charge-with-gst").val(0);
        $(".hidden-shipping-charge-cgst-tax-amount").val(0);
        $(".hidden-shipping-charge-sgst-tax-amount").val(0);
        $(".hidden-shipping-charge-igst-tax-amount").val(0);
        $(".hidden-packing-charge-cgst-tax-amount").val(0);
        $(".hidden-packing-charge-sgst-tax-amount").val(0);
        $(".hidden-packing-charge-igst-tax-amount").val(0);
        $(".change-cess-rate").val(data.totalCessRate);
        // $(".hidden-cess-rate").val(data.totalCessRate);
        $(".change-tcs-amount").val(0);
        $(".narration").text(result.narration);
        $(".term-and-condition").text(result.term_and_condition);
    }, 2000);
    let gstTaxValue = $('.gst-tax-value').map((i, e) => $(e).attr('data-gst-name')).get();
    let isGstNull = gstTaxValue.every(val => val == 'NA');

    let customerAddress = billingAddresses.state_id;
    if (!isGstNull && $(".is-company-gst-applicable").val() == 1 && $(".is-change-gst-details").val() != "" && $(".is-change-gst-details").val() == 0) {
        if (customerAddress == $("#companyState").val()) {
            $("#mainClassificationNatureType").val(
                "Intrastate Sales Taxable"
            );
            $("#mainIsRcmApplicable").val(result.isRcmApplicable ?? 0);
            $("#isCgstSgstIgstCalculated").val(1);
        } else {
            $("#mainClassificationNatureType").val(
                "Interstate Sales Taxable"
            );
            $("#mainIsRcmApplicable").val(result.isRcmApplicable ?? 0);
            $("#isCgstSgstIgstCalculated").val(1);
        }
    }

    setTimeout(function () {
        grandTotalCalculation();
        stopLoader();
        screenUnLock();
        loadMaxLength();
    }, 2500);
}

listenClick(".modal-delivery-challan-invoice", function () {
    let dCTransactionId = $(this).attr("data-id");
    let challanIds = $("#dataItemList").attr("data-dChallan");
    let invoiceType = $("#dataItemList").attr("data-invoice-type");
    let dCTransactionIdsArray = challanIds ? challanIds.split(",") : [];
    handleDeliveryChallanNumber(dCTransactionIdsArray, invoiceType, dCTransactionId);
    $("#multipleInvoiceDataErrorModal").modal("hide");
});

listenClick('.modal-estimate-invoice', function () {
    let eQTransactionId = $(this).attr('data-id');
    let estimateIds = $('#dataItemList').attr('data-estimates');
    let invoiceType = $('#dataItemList').attr('data-invoice-type');
    let eQTransactionIdsArray = estimateIds ? estimateIds.split(',') : [];
    handleInvoiceNumber(eQTransactionIdsArray, invoiceType, eQTransactionId);
    $('#multipleInvoiceDataErrorModal').modal('hide');
});

listenClick('.invoive-type-button', function () {
    let invoiceType = $(this).attr('data-invoice-type');
    let eQTransactionIds = $(this).attr('data-estimates');
    let eQTransactionIdsArray = eQTransactionIds ? eQTransactionIds.split(',') : [];
    handleInvoiceNumber(eQTransactionIdsArray, invoiceType);
    $('#multipleInvoiceErrorModal').modal('hide');
})

window.changePrefixSuffixMethod = function (method, changeInput) {
    if (method == 2) {
        let fiscalYear = "";
        let today = new Date();
        if (today.getMonth() + 1 <= 3) {
            let currentYear = today.getFullYear();
            let getYerString = currentYear.toString();
            fiscalYear =
                today.getFullYear() - 1 + "-" + getYerString.substr(2, 4);
        } else {
            let currentYear = today.getFullYear() + 1;
            let getYerString = currentYear.toString();
            fiscalYear = today.getFullYear() + "-" + getYerString.substr(2, 4);
        }
        changeInput.val(fiscalYear);
    }
    if (method == 1) {
        changeInput.val("");
    }
};

window.appendCustomerDetails = async function (customerId) {
    if (customerId > 0) {
        startLoader();
        screenLock();

        try {
            let response = await $.ajax({
                url: route("company.gst-customer-detail-transaction", {
                    customer: customerId,
                }),
                type: "GET",
            });

            if (response.success) {
                let data = response.data.customerDetail;
                if ($(".sale-credit-limit").length) {
                    if (data.credit_limit) {
                        $(".sale-credit-limit").removeClass("d-none");
                    } else {
                        $(".sale-credit-limit").addClass("d-none");
                    }
                }
                let tcsTaxType;
                if (
                    data.pan_card_number == null ||
                    data.pan_card_number == ""
                ) {
                    tcsTaxType = 0;
                } else if (data.entity_type == 2) {
                    tcsTaxType = 1;
                } else {
                    tcsTaxType = 2;
                }

                $(".tcs-tax-type-value").val(tcsTaxType);
                $(".customer-credit-limit").text(
                    data.credit_limit_amount ?? 0.0
                );
                $(".credit-period").val(data.credit_limit_period ?? 0);
                if (data.credit_period_type == 1) {
                    $(".credit-period-month").val(1).prop("checked", true);
                }
                if (data.credit_period_type == 2) {
                    $(".credit-period-day").val(2).prop("checked", true);
                }

                $(".customer-gst-number").val(data.gstin);
                $(".broker-name").val(data.broker_master).trigger("change");
                $(".transport-name").val(data.transporter_id).trigger("change");
                setTimeout(function () {
                    if (data.brokerage) {
                        $(".brokerage-gst-for-sale").val(data.brokerage);
                    }
                    if (data.brokerage_on_value == 1) {
                        $(".brokerage-on-sale").val(1).prop("checked", true);
                    }
                    if (data.brokerage_on_value == 2) {
                        $(".brokerage-on-purchase")
                            .val(2)
                            .prop("checked", true);
                    }
                }, 1000);
                $('.shipping-address-gstin').val(response.data.customerDetail.shipping_gstin)
                $('.shipping-address-name').val(response.data.customerDetail.shipping_name)
                let billingAddress = response.data.billingAddress;
                let shippingAddress = response.data.shippingAddress;
                let customerAddress = "";
                // if (!$(".enabled-shipping-address").hasClass("d-none")) {
                //     customerAddress = shippingAddress.state_id ?? billingAddress.state_id;
                // } else {
                customerAddress = billingAddress.state_id;
                // }

                let gstTaxValue = $('.gst-tax-value').map((i, e) => $(e).attr('data-gst-name')).get();
                let isGstNull = gstTaxValue.every(val => val == 'NA');

                if (!isGstNull && $(".is-company-gst-applicable").val() == 1 && $(".is-change-gst-details").val() != "" && $(".is-change-gst-details").val() == 0) {
                    if (customerAddress == $("#companyState").val()) {
                        $("#mainClassificationNatureType").val(
                            "Intrastate Sales Taxable"
                        );
                        $("#mainIsRcmApplicable").val(0);
                        // $("#consolidatingItemsToInvoice").val(0);
                        $("#isCgstSgstIgstCalculated").val(1);
                    } else {
                        $("#mainClassificationNatureType").val(
                            "Interstate Sales Taxable"
                        );
                        $("#mainIsRcmApplicable").val(0);
                        // $("#consolidatingItemsToInvoice").val(0);
                        $("#isCgstSgstIgstCalculated").val(1);
                    }
                }

                $("#incomeBillingAddress1").val(billingAddress.address_1);
                $("#incomeBillingAddress2").val(billingAddress.address_2);
                $("#incomeBillingAddressPinCode").val(billingAddress.pin_code);
                $("#incomeBillingCountry").val(billingAddress.country_id);

                if (shippingAddress) {
                    $("#incomeShippingAddress").val(shippingAddress.address_1);
                    $("#incomeShippingAddress2").val(shippingAddress.address_2);
                    $("#incomeShippingPinCode").val(shippingAddress.pin_code);
                    $("#incomeShippingCountry").val(shippingAddress.country_id);
                }

                await Promise.all([
                    appendOptionToStateSelect(
                        $("#incomeBillingState"),
                        billingAddress.country_id
                    ),
                    appendOptionToStateSelect(
                        $("#incomeShippingState"),
                        shippingAddress.country_id
                    ),
                    appendOptionToCitySelect(
                        $("#incomeBillingCity"),
                        billingAddress.state_id
                    ),
                    appendOptionToCitySelect(
                        $("#incomeShippingCity"),
                        shippingAddress.state_id
                    ),
                ]);

                $("#incomeBillingState option").each(function () {
                    if (this.value == billingAddress.state_id) {
                        $(this).prop("selected", true);
                    }
                });
                $("#incomeShippingState option").each(function () {
                    if (this.value == shippingAddress.state_id) {
                        $(this).prop("selected", true);
                    }
                });
                $("#incomeBillingCity option").each(function () {
                    if (this.value == billingAddress.city_id) {
                        $(this).prop("selected", true);
                    }
                });
                $("#incomeShippingCity option").each(function () {
                    if (this.value == shippingAddress.city_id) {
                        $(this).prop("selected", true);
                    }
                });
                grandTotalCalculation();
                changeShippingFrightCharge();
                changePackingCharge();
                initSelect2();
                stopLoader();
                screenUnLock();
            }
        } catch (e) {
            stopLoader();
            screenUnLock();
            displayErrorMessage(e.responseJSON.message);
        }
    }
};

window.appendSupplierDetails = async function (supplierId) {
    if (supplierId > 0) {
        startLoader();
        screenLock();

        try {
            let response = await $.ajax({
                url: route("company.gst-supplier-detail-transaction", {
                    supplier: supplierId,
                }),
                type: "GET",
            });
            if (response.success) {
                let data = response.data.supplierDetail;
                let tdsTaxType;
                if (
                    data.pan_card_number == null ||
                    data.pan_card_number == ""
                ) {
                    tdsTaxType = 0;
                } else if (data.entity_type == 2) {
                    tdsTaxType = 1;
                } else {
                    tdsTaxType = 2;
                }
                $(".tds-Pan").val(data.pan_card_number);
                $(".tds-tax-type-value").val(tdsTaxType);
                $(".customer-gst-number").val(data.gstin);

                $(".broker-name").val(data.broker_master).trigger("change");
                $(".transport-name").val(data.transporter_id).trigger("change");
                setTimeout(function () {
                    if (data.brokerage) {
                        $(".brokerage-gst-for-sale").val(data.brokerage);
                    }
                    if (data.brokerage_on_value == 1) {
                        $(".brokerage-on-sale").val(1).prop("checked", true);
                    }
                    if (data.brokerage_on_value == 2) {
                        $(".brokerage-on-purchase")
                            .val(2)
                            .prop("checked", true);
                    }
                }, 1000);
                $('.shipping-address-gstin').val(response.data.supplierDetail.shipping_gstin)
                $('.shipping-address-name').val(response.data.supplierDetail.shipping_name)
                let billingAddress = response.data.billingAddress;
                let shippingAddress = response.data.shippingAddress;
                let customerAddress = "";
                // if (!$(".enabled-shipping-address").hasClass("d-none")) {
                //     customerAddress = shippingAddress.state_id ?? billingAddress.state_id;
                // } else {
                customerAddress = billingAddress.state_id;
                // }

                let gstTaxValue = $('.gst-tax-value').map((i, e) => $(e).attr('data-gst-name')).get();
                let isGstNull = gstTaxValue.every(val => val == 'NA');

                if (!isGstNull && $(".is-company-gst-applicable").val() == 1 && $(".is-change-gst-details").val() != "" && $(".is-change-gst-details").val() == 0) {
                    if (customerAddress == $("#companyState").val()) {
                        $("#mainClassificationNatureType").val(
                            "Intrastate Purchase Taxable"
                        );
                        $("#mainIsRcmApplicable").val(0);
                        // $("#consolidatingItemsToInvoice").val(0);
                        $("#isCgstSgstIgstCalculated").val(1);
                    } else {
                        $("#mainClassificationNatureType").val(
                            "Interstate Purchase Taxable"
                        );
                        $("#mainIsRcmApplicable").val(0);
                        // $("#consolidatingItemsToInvoice").val(0);
                        $("#isCgstSgstIgstCalculated").val(1);
                    }
                }

                $("#incomeBillingAddress1").val(billingAddress.address_1);
                $("#incomeBillingAddress2").val(billingAddress.address_2);
                $("#incomeBillingAddressPinCode").val(billingAddress.pin_code);
                $("#incomeBillingCountry").val(billingAddress.country_id);

                if (shippingAddress) {
                    $("#incomeShippingAddress").val(shippingAddress.address_1);
                    $("#incomeShippingAddress2").val(shippingAddress.address_2);
                    $("#incomeShippingPinCode").val(shippingAddress.pin_code);
                    $("#incomeShippingCountry").val(shippingAddress.country_id);
                }

                await Promise.all([
                    appendOptionToStateSelect(
                        $("#incomeBillingState"),
                        billingAddress.country_id
                    ),
                    appendOptionToStateSelect(
                        $("#incomeShippingState"),
                        shippingAddress.country_id
                    ),
                    appendOptionToCitySelect(
                        $("#incomeBillingCity"),
                        billingAddress.state_id
                    ),
                    appendOptionToCitySelect(
                        $("#incomeShippingCity"),
                        shippingAddress.state_id
                    ),
                ]);

                $("#incomeBillingState option").each(function () {
                    if (this.value == billingAddress.state_id) {
                        $(this).prop("selected", true);
                    }
                });
                $("#incomeShippingState option").each(function () {
                    if (this.value == shippingAddress.state_id) {
                        $(this).prop("selected", true);
                    }
                });
                $("#incomeBillingCity option").each(function () {
                    if (this.value == billingAddress.city_id) {
                        $(this).prop("selected", true);
                    }
                });
                $("#incomeShippingCity option").each(function () {
                    if (this.value == shippingAddress.city_id) {
                        $(this).prop("selected", true);
                    }
                });
                grandTotalCalculation();
                changeShippingFrightCharge();
                changePackingCharge();
                initSelect2();
                stopLoader();
                screenUnLock();
            }
        } catch (error) {
            stopLoader();
            screenUnLock();
            displayErrorMessage(error.message);
        }
    }
};

window.getBrokerDetails = function (brokerId) {
    if (brokerId.length) {
        $.ajax({
            url: route("company.gst-broker-transaction", {
                brokerId: brokerId,
            }),
            type: "GET",
            success: function (result) {
                let data = result.data;
                if (data.brokerage_for_sale) {
                    $(".brokerage-gst-for-sale").val(data.brokerage_for_sale);
                }
                if (data.brokerage_for_sale_type == 1) {
                    $(".brokerage-on-sale").val(1).prop("checked", true);
                }
                if (data.brokerage_for_sale_type == 2) {
                    $(".brokerage-on-purchase").val(2).prop("checked", true);
                }
            },
            error: function (error) {
                displayErrorMessage(error.responseJSON.message);
            },
        });
    } else {
        $(".brokerage-gst-for-sale").val(0);
        $(".brokerage-on-purchase").prop("checked", false);
        $(".brokerage-on-sale").prop("checked", false);
    }
};

listenKeyup("#totalCgstTax", function () {
    calculateTheCGSTAndSGSTTax();
});

listenKeyup("#totalSgstTax", function () {
    calculateTheCGSTAndSGSTTax();
});

listenKeyup("#totalIgstTax", function () {
    calculateTheCGSTAndSGSTTax();
});

function calculateTheCGSTAndSGSTTax() {
    let totalVal = $("#grandTotalForAddCgst").val() ?? 0;
    let cgstVal = $("#totalCgstTax").val() ?? 0;
    let sgstVal = $("#totalSgstTax").val() ?? 0;
    let igstVal = $("#totalIgstTax").val() ?? 0;
    let shippingFreightVal = $("#shipping_freight").val() ?? 0;
    let packingChargeVal = $("#packing_charge").val() ?? 0;
    let cessVal = $("#cess").val() ?? 0;
    let tcsVal = $("#tcs_amount").val() ?? 0;

    let cgstDiff = cgstVal - parseFloat($("#totalCgstTaxDiff").val() ?? 0);
    let sgstDiff = sgstVal - parseFloat($("#totalSgstTaxDiff").val() ?? 0);
    let igstDiff = igstVal - parseFloat($("#totalIgstTaxDiff").val() ?? 0);
    let shippingFreightDiff =
        shippingFreightVal -
        parseFloat($("#totalShippingFreightDiff").val() ?? 0);
    let packingChargeDiff =
        packingChargeVal - parseFloat($("#totalPackingChargeDiff").val() ?? 0);
    let cessDiff = cessVal - parseFloat($("#totalCessDiff").val() ?? 0);
    let tcsDiff = tcsVal - parseFloat($("#totalTcsDiff").val() ?? 0);

    let total = parseFloat(totalVal);

    if (cgstDiff != 0) {
        total += parseFloat(cgstDiff);
    }

    if (sgstDiff != 0) {
        total += parseFloat(sgstDiff);
    }

    if (igstDiff != 0) {
        total += parseFloat(igstDiff);
    }

    if (shippingFreightDiff != 0) {
        total += parseFloat(shippingFreightDiff);
    }

    if (packingChargeDiff != 0) {
        total += parseFloat(packingChargeDiff);
    }

    if (cessDiff != 0) {
        total += parseFloat(cessDiff);
    }

    if (tcsDiff != 0) {
        total += parseFloat(tcsDiff);
    }

    $(".total-amount").text(total.toFixed(fixDigit));
    $(".hidden-total-amount").val(total.toFixed(fixDigit)).trigger("keyup");
    $("[name=rounding_amount]")
        .val($("[name=rounding_amount]").val())
        .trigger("keyup");
}

window.calculateCgstSgstIgstTax = function (elementId) {
    let rowElement = $('.ledger-' + elementId);
    let withOutGstAmountElement = rowElement.find("[ledger-data-rate-with-out-gst-amount=" + elementId + "]");
    let discountTypeElement = rowElement.find("[ledger-data-discount-type=" + elementId + "]");
    let discountValueElement = rowElement.find("[ledger-data-discount-value=" + elementId + "]");

    let withOutAmount = withOutGstAmountElement.val();
    let discountType = discountTypeElement.val();

    let totalDiscountAmount = 0;
    if (discountType == 1 && discountValueElement.val() != 0) {
        totalDiscountAmount = discountValueElement.val();
    }
    if (discountType == 2 && discountValueElement.val() != 0) {
        totalDiscountAmount = (withOutAmount * discountValueElement.val()) / 100;
    }

    let mainClassificationNatureType = $("#mainClassificationNatureType").val();
    let gstTax = rowElement.find("[ledger-data-gst-tax-value=" + elementId + "]").val();
    let integratedTaxCalculationValue = rowElement.find("[ledger-data-classification-igst-tax=" + elementId + "]");
    let centralTaxCalculationValue = rowElement.find("[ledger-data-classification-cgst-tax=" + elementId + "]");
    let stateTaxCalculationValue = rowElement.find("[ledger-data-classification-sgst-tax=" + elementId + "]");

    let subTotal = withOutAmount - totalDiscountAmount;

    centralTaxCalculationValue.val(0);
    stateTaxCalculationValue.val(0);
    integratedTaxCalculationValue.val(0);

    if ($.inArray(mainClassificationNatureType, intraStateArray) > -1) {
        /* let cgstTax = (parseFloat(gstTax) / 2).toFixed(fixDigit); */
        let cgstTax = (parseFloat(gstTax) / 2);
        let finalValue = ((subTotal * cgstTax) / 100).toFixed(fixDigit);
        centralTaxCalculationValue.val(finalValue);
        stateTaxCalculationValue.val(finalValue);
    }

    if ($.inArray(mainClassificationNatureType, interStateArray) > -1) {
        /* let igstTax = parseFloat(gstTax).toFixed(fixDigit); */
        let igstTax = parseFloat(gstTax);
        let finalValue = ((subTotal * igstTax) / 100).toFixed(fixDigit);
        integratedTaxCalculationValue.val(finalValue);
    }

    if ($.inArray(mainClassificationNatureType, purchaseIntraStateTaxArray) > -1) {
        /* let cgstTax = (parseFloat(gstTax) / 2).toFixed(fixDigit); */
        let cgstTax = (parseFloat(gstTax) / 2);
        let finalValue = ((subTotal * cgstTax) / 100).toFixed(fixDigit);
        centralTaxCalculationValue.val(finalValue);
        stateTaxCalculationValue.val(finalValue);
    }

    if ($.inArray(mainClassificationNatureType, purchaseInterstateTaxArray) > -1) {
        /* let igstTax = parseFloat(gstTax).toFixed(fixDigit); */
        let igstTax = parseFloat(gstTax);
        let finalValue = ((subTotal * igstTax) / 100).toFixed(fixDigit);
        integratedTaxCalculationValue.val(finalValue);
    }
};

window.calculateTCSAmount = function () {
    let tcsTaxRate = $(".tcs-tax-rate").val();
    let calculatedTcsOn = $(".tcs-calculated-on-value").val();
    let roundOffMethod = $(".tcs-round-off-method").val();
    let totalAmount = 0;
    let tcsTaxAmount = 0;
    $(".hidden-sub-total").each(function () {
        if ($(this).val() && $(this).val() != 0) {
            totalAmount += parseFloat($(this).val());
        }
    });
    let finalTotal = totalAmount;

    let shippingAndFrightCharge = $(".change-shipping-fright-charge").val();
    if (shippingAndFrightCharge && shippingAndFrightCharge != 0) {
        totalAmount += parseFloat(shippingAndFrightCharge);
    }
    let packingCharge = $(".change-packing-charge").val();
    if (packingCharge && packingCharge != 0) {
        totalAmount += parseFloat(packingCharge);
    }
    if ($("#mainIsRcmApplicable").val() == 0) {
        let cgstAmount = $("#totalCgstTax").val();
        if (cgstAmount && cgstAmount != 0) {
            totalAmount += parseFloat(cgstAmount);
        }
        let sgstAmount = $("#totalSgstTax").val();
        if (sgstAmount && sgstAmount != 0) {
            totalAmount += parseFloat(sgstAmount);
        }
        let igstAmount = $("#totalIgstTax").val();
        if (igstAmount && igstAmount != 0) {
            totalAmount += parseFloat(igstAmount);
        }
    }
    let cessAmount = $(".change-cess-rate").val() == "" || !$(".change-cess-rate").val() ? 0 : $(".change-cess-rate").val();
    totalAmount += parseFloat(cessAmount);

    let classificationNatureType = $("#mainClassificationNatureType").val();
    if (classificationNatureType == "Export Sales Taxable" || classificationNatureType == "Sales to SEZ Taxable") {
        totalAmount = totalAmount - $("#totalIgstTax").val();
    }

    let grandFinalAmount = totalAmount;
    if (calculatedTcsOn == 2) {
        tcsTaxAmount = (grandFinalAmount * tcsTaxRate) / 100;
    } else {
        tcsTaxAmount = (finalTotal * tcsTaxRate) / 100;
    }

    let tcsRoundOffAmount = tcsTaxAmount;
    if (roundOffMethod == 1) {
        tcsRoundOffAmount = tcsTaxAmount.toFixed(fixDigit);
    } else if (roundOffMethod == 2) {
        tcsRoundOffAmount = Math.floor(tcsTaxAmount).toFixed(fixDigit);
    } else if (roundOffMethod == 3) {
        tcsRoundOffAmount = Math.round(tcsTaxAmount).toFixed(fixDigit);
    } else if (roundOffMethod == 4) {
        tcsRoundOffAmount = Math.ceil(tcsTaxAmount).toFixed(fixDigit);
    } else {
        tcsRoundOffAmount.toFixed(fixDigit);
    }

    // $(".change-tcs-amount").val(tcsTaxAmount.toFixed(fixDigit)).trigger("keyup");
    $(".change-tcs-amount").val(tcsTaxAmount.toFixed(fixDigit));
};

listenChange(".change-purchase-invoice-number", function () {
    let purchaseTransactionId = $(this).val();
    if (purchaseTransactionId) {
        screenLock();
        $.ajax({
            type: "GET",
            url: route("company.purchase.get-purchase-transaction", {
                purchaseTransactionId: purchaseTransactionId,
            }),
            beforeSend: function () {
                startLoader();
            },
            success: function (data) {
                let result = data.data.purchaseTransaction;
                if (result.payment_mode == 1) {
                    $("#paymentModCash")
                        .val(1)
                        .prop("checked", true)
                        .trigger("change");

                    $(".purchase-append-payment-type-ledger-id")
                        .val(result.payment_type_ledger_id)
                        .trigger("change");
                }
                if (result.payment_mode == 2) {
                    $("#paymentModCredit")
                        .val(2)
                        .prop("checked", true)
                        .trigger("change");

                    $(".purchase-append-payment-type-ledger-id")
                        .val("")
                        .trigger("change");
                }
                $(".purchase-append-broker-name")
                    .val(result.broker_id)
                    .trigger("change");
                setTimeout(function () {
                    $(".purchase-append-brokrage").val(
                        result.brokerage_for_sale
                    );
                    if (result.brokerage_on_value_type == 1) {
                        $(".purchase-append-brokerage-on-sale")
                            .val(1)
                            .prop("checked", true);
                    }
                    if (result.brokerage_on_value_type == 2) {
                        $(".purchase-append-brokerage-on-purchase")
                            .val(2)
                            .prop("checked", true);
                    }
                }, 1500);
                if ($(".purchase-append-credit-period").length) {
                    $(".purchase-append-credit-period").val(
                        result.credit_period
                    );
                }
                $(".purchase-append-transport-name")
                    .val(result.transport_id)
                    .trigger("change");
                $(".purchase-append-document-number").val(
                    result.transporter_document_number
                );
                if ($(".purchase-append-po-number").length) {
                    $(".purchase-append-po-number").val(result.po_no);
                }
                $("#isCgstSgstIgstCalculated").val(
                    result.is_cgst_sgst_igst_calculated
                );
                $("#isGstNa").val(result.is_gst_na);
                let purchaseVoucherDate = new Date();
                let transportDate = new Date();
                let poDate = new Date();
                if (result.voucher_date) {
                    purchaseVoucherDate = new Date(result.voucher_date);
                }
                if (result.transporter_document_date) {
                    transportDate = new Date(result.transporter_document_date);
                }
                if (result.transporter_vehicle_number) {
                    $(".transporter-vehicle-number").val(result.transporter_vehicle_number);
                }
                if (result.po_date) {
                    poDate = new Date(result.po_date);
                }
                $(".purchase-append-date").val(
                    moment(purchaseVoucherDate).format("DD-MM-YYYY")
                );
                $(".purchase-append-transport-date").val(
                    moment(transportDate).format("DD-MM-YYYY")
                );
                if ($(".purchase-append-po-date").length) {
                    $(".purchase-append-po-date").flatpickr({
                        allowInput: true,
                        altFormat: "d-m-Y",
                        ariaDateFormat: "d-m-Y",
                        dateFormat: "d-m-Y",
                        defaultDate: moment(poDate).format("DD-MM-YYYY"),
                    });
                }
                //for addresses
                let shippingAddresses = data.data.shippingAddress;
                let billingAddresses = data.data.billingAddress;
                let gstin = result.gstin;
                let shippingGstin = data.data.purchaseTransaction.shipping_gstin;
                let shippingPartyName = data.data.purchaseTransaction.shipping_name;

                $(".shipping-address-name").val(shippingPartyName);
                $(".shipping-address-gstin").val(shippingGstin);
                appendAddresses(shippingAddresses, billingAddresses, gstin);
                $(".purchase-append-tcs-tax-id")
                    .val(result.tcs_tax_id)
                    .trigger("change");
                setTimeout(function () {
                    $(".purchase-append-tds-rate").val(result.tcs_rate);
                    $(".purchase-append-tcs-tax-amount").val(result.tcs_amount);
                }, 1000);
                setTimeout(function () {
                    $(".purchase-append-shipping-charge").val(
                        result.shipping_freight
                    );
                    $(".purchase-append-shipping-charge-with-gst").text(
                        result.shipping_freight_with_gst
                    );
                    $(".purchase-append-hidden-shipping-charge-with-gst").val(
                        result.shipping_freight_with_gst
                    );
                    $(".purchase-append-packing-charge").val(
                        result.packing_charge
                    );
                    $(".purchase-append-packing-charge-with-gst").text(
                        result.packing_charge_with_gst
                    );
                    $(".purchase-append-hidden-packing-charge-with-gst").val(
                        result.packing_charge_with_gst
                    );
                    $(".hidden-shipping-charge-cgst-tax-amount").val(
                        result.shipping_freight_sgst_amount ?? 0
                    );
                    $(".hidden-shipping-charge-sgst-tax-amount").val(
                        result.shipping_freight_cgst_amount ?? 0
                    );
                    $(".hidden-shipping-charge-igst-tax-amount").val(
                        result.shipping_freight_igst_amount ?? 0
                    );
                    $(".hidden-packing-charge-cgst-tax-amount").val(
                        result.packing_charge_sgst_amount ?? 0
                    );
                    $(".hidden-packing-charge-sgst-tax-amount").val(
                        result.packing_charge_cgst_amount ?? 0
                    );
                    $(".hidden-packing-charge-igst-tax-amount").val(
                        result.packing_charge_igst_amount ?? 0
                    );
                    $(".purchase-append-cess-rate").val(result.cess);
                    $(".purchase-append-tcs-amount").val(result.tcs_amount);
                    $(".purchase-append-cgst-value").val(result.cgst);
                    $(".purchase-append-sgst-value").val(result.sgst);
                    $(".purchase-append-igst-value").val(result.igst);
                    $(".purchase-append-cgst-text").text(result.cgst);
                    $(".purchase-append-sgst-text").text(result.sgst);
                    $(".purchase-append-igst-text").text(result.igst);
                    $(".purchase-append-total-text").text(result.total);
                    $(".purchase-append-total-value").val(result.total);
                    $(".purchase-append-round-amount").val(
                        result.rounding_amount.toFixed(fixDigit)
                    );
                    $(".purchase-append-grand-total-value").val(
                        result.grand_total
                    );
                    $(".purchase-append-grand-total-text").text(
                        result.grand_total
                    );
                    $(".purchase-append-narration").text(
                        result.narration ?? null
                    );
                    $(".purchase-append-term-and-condition").text(
                        result.term_and_condition ?? null
                    );
                }, 2500);
                $(".purchase-append-classification-nature-type").val(
                    data.data.classificationNatureType ?? null
                );
                $(".purchase-append-is-rcm-applicable").val(
                    data.data.isRcmApplicable ?? null
                );
                setTimeout(function () {
                    let routeName = null;
                    if (
                        $(".transaction-module-name").val() ==
                        "purchaseReturnTransaction"
                    ) {
                        routeName = route(
                            "company.purchase-return.append-purchase-transaction-items",
                            { purchaseTransactionId: purchaseTransactionId }
                        );
                    }
                    if (
                        $(".transaction-module-name").val() ==
                        "expenseCreditNoteTransaction"
                    ) {
                        routeName = route(
                            "company.expense-credit-note.append-purchase-transaction-items",
                            { purchaseTransactionId: purchaseTransactionId }
                        );
                    }
                    if (
                        $(".transaction-module-name").val() ==
                        "expenseDebitNoteTransaction"
                    ) {
                        routeName = route(
                            "company.expense-debit-note.append-purchase-transaction-items",
                            { purchaseTransactionId: purchaseTransactionId }
                        );
                    }
                    if (routeName) {
                        $.ajax({
                            type: "get",
                            url: routeName,
                            success: function (data) {
                                $(".purchase-items-append")
                                    .empty()
                                    .append(data.data);
                                initSelect2();
                                if (result.pass_tds_entry) {
                                    $(".purchase-pass-tds-entry")
                                        .val(result.pass_tds_entry)
                                        .prop("checked", true);
                                    $(".purchase-pass-tds-entry").trigger(
                                        "change"
                                    );
                                }
                                $(".purchase-ledger-of-tds")
                                    .val(result.ledger_of_tds)
                                    .trigger("change");
                                $(".tds-taxable-value").val(
                                    result.tds_taxable_value
                                );
                                $(".purchase-tds-pan").val(result.tds_pan);
                                $(".purchase-tds-rate").val(result.tds_rate);
                                $(".purchase-tds-amount").val(
                                    result.tds_amount
                                );
                                if (
                                    $(".transaction-module-name").val() ==
                                    "purchaseReturnTransaction" ||
                                    $(".transaction-module-name").val() ==
                                    "expenseDebitNoteTransaction"
                                ) {
                                    generateUniqueIdForAppendScreenPurchaseReturn();
                                }
                                if (
                                    $(".transaction-module-name").val() ==
                                    "expenseCreditNoteTransaction"
                                ) {
                                    generateUniqueIdForAppendScreenExpenseCreditNote();
                                }
                            },
                            complete: function () {
                                loadMaxLength();
                                stopLoader();
                                screenUnLock();
                            },
                        });
                    }
                }, 2000);
            },
        });
    } else {
        location.href = window.location.href;
    }
});

function iniFixedAmountValue() {
    window.fixDigit = fixDigitNumber;
}

async function appendAddresses(shippingAddresses, billingAddresses, gstin) {
    $(".customer-gst-number").val(gstin);
    $("#incomeBillingAddress1").val(billingAddresses.address_1);
    $("#incomeBillingAddress2").val(billingAddresses.address_2);
    $("#incomeBillingAddressPinCode").val(billingAddresses.pin_code);

    $("#incomeBillingCountry option").each(function () {
        if (this.value == billingAddresses.country_id) {
            $(this).prop("selected", true);
        }
    });

    if (shippingAddresses) {
        $("#incomeShippingAddress").val(shippingAddresses.address_1);
        $("#incomeShippingAddress2").val(shippingAddresses.address_2);
        $("#incomeShippingPinCode").val(shippingAddresses.pin_code);
        $("#incomeShippingCountry option").each(function () {
            if (this.value == shippingAddresses.country_id) {
                $(this).prop("selected", true);
            }
        });
    }

    await Promise.all([
        appendOptionToStateSelect(
            $("#incomeBillingState"),
            billingAddresses.country_id
        ),
        appendOptionToCitySelect(
            $("#incomeBillingCity"),
            billingAddresses.state_id
        ),
    ]);

    $("#incomeBillingState option").each(function () {
        if (this.value == billingAddresses.state_id) {
            $(this).prop("selected", true);
        }
    });

    $("#incomeBillingCity option").each(function () {
        if (this.value == billingAddresses.city_id) {
            $(this).prop("selected", true);
        }
    });

    if (shippingAddresses) {
        await Promise.all([
            appendOptionToStateSelect(
                $("#incomeShippingState"),
                shippingAddresses.country_id
            ),
            appendOptionToCitySelect(
                $("#incomeShippingCity"),
                shippingAddresses.state_id
            ),
        ]);

        $("#incomeShippingState option").each(function () {
            if (this.value == shippingAddresses.state_id) {
                $(this).prop("selected", true);
            }
        });

        $("#incomeShippingCity option").each(function () {
            if (this.value == shippingAddresses.city_id) {
                $(this).prop("selected", true);
            }
        });
    }
    initSelect2();
}

window.allArrayValueSame = function (array) {
    return array.every((val) => val == array[0]);
};

Object.defineProperty(Array.prototype, "sum", {
    value: function () {
        const arr = this;
        let sum = 0;
        for (let i = 0; i < arr.length; i++) {
            sum += parseFloat(arr[i]);
        }
        return sum;
    },
    enumerable: false,
});

var originalTitle;
var thermalPrintFrame;
var fileName;
var isContentLoaded = false;
var transactionId;
var pageTitle = $('title').text();
window.pdfPreviewModal = function (url) {
    isContentLoaded = false;
    $.ajax({
        type: "GET",
        url: url,
        success: function (result) {

            $(".download-print-pdf").attr("href", result.data.data.downloadRoute);
            if (result.data.data.emailRoute == null) {
                $(".share-pdf-to-email").addClass('d-none');
            } else {
                $(".share-pdf-to-email").removeClass('d-none');
                $(".share-pdf-to-email").attr("href", result.data.data.emailRoute);
            }
            if ((result.data.data.transactionId == null && result.data.data.transactionType == null) || result.data.data.is_thermal_print) {
                $(".duplicate-triplicate-checkbox").addClass('d-none');
            } else {
                $("#duplicateInvoice").attr("data-type", result.data.data.transactionType);
                $("#triplicateInvoice").attr("data-type", result.data.data.transactionType);
                if(result.data.data.printType == 2){
                    $("#duplicateInvoice").prop('checked', true);
                }
                if(result.data.data.printType == 3){
                    $("#triplicateInvoice").prop('checked', true);
                }
                $(".duplicate-triplicate-checkbox").removeClass('d-none');
            }
            transactionId = result.data.data.transactionId
            originalTitle = document.title;
            var contents = result.data.html;
            fileName = result.data.fileName;
            thermalPrintFrame = document.createElement("iframe");
            thermalPrintFrame.style.width = '794px';
            thermalPrintFrame.style.height = '700px';
            thermalPrintFrame.id = 'thermalPrintFrame';
            thermalPrintFrame.srcdoc = contents;
            // $(".transaction-pdf-preview").empty().append(thermalPrintFrame);
            var transactionPdfPreview = document.querySelector('.transaction-pdf-preview');
            if (transactionPdfPreview) {
                transactionPdfPreview.innerHTML = '';
                transactionPdfPreview.appendChild(thermalPrintFrame);
                if (result.data.data.is_thermal_print) {
                    $(".share-pdf-to-email").addClass("d-none");
                    $(".download-print-pdf").addClass("d-none");
                    $(".sale-pdf-preview").attr("style", "height: auto !important; width: auto !important; ");
                    $(".sale-pdf-preview-modal").removeClass('modal-xl');
                    $(".sale-pdf-preview-modal").addClass('modal-md');
                } else if (result.data.data.isA5Pdf) {
                    thermalPrintFrame.style.width = '560px';
                    $(".sale-pdf-preview-modal").removeClass('modal-xl');
                    $(".sale-pdf-preview-modal").addClass('modal-xl');
                    $(".sale-pdf-preview").attr("style", "height: auto !important; width: auto !important");
                    $(".modal-body").attr("style","overflow-x: hidden !important");
                } else {
                    $(".sale-pdf-preview-modal").removeClass('modal-md');
                    $(".sale-pdf-preview-modal").addClass('modal-xl');
                    $(".sale-pdf-preview").attr("style", "margin:0 auto !important; width: auto !important");
                    $(".modal-body").attr("style","overflow-x: hidden !important");
                }
                $("#salePdfPreviewModal").appendTo("body").modal("show");
            }

            thermalPrintFrame.onload = function () {
                if(!result.data.data.isA5Pdf){
                    var iframeDocument = thermalPrintFrame.contentDocument || thermalPrintFrame.contentWindow.document;
                    var body = iframeDocument.body;
                    if (body) {
                        body.style.overflowX = 'hidden';
                    }
                }
                setTimeout(function () {
                    let height = 0;
                    let width = 0
                    if (result.data.data.isA5Pdf) {
                        height = '794'
                        width = '582'
                    } else if (result.data.data.is_thermal_print) {
                        height = '0'
                        width = '350'
                    } else if (result.data.data.isLandscape) {
                        width = "1190";
                        $(".modal-xl").css("max-width", "1220px");
                    } else {
                        height = "1123";
                        width = '794';
                    }

                    // if (height < thermalPrintFrame.contentWindow.document.body.scrollHeight + 20) {
                    //     height = thermalPrintFrame.contentWindow.document.body.scrollHeight + 20
                    // }

                    // if (!result.data.data.is_thermal_print) {
                    //     if (width < thermalPrintFrame.contentWindow.document.body.scrollWidth + 20) {
                    //         width = thermalPrintFrame.contentWindow.document.body.scrollWidth + 20
                    //     }
                    // }
                    // thermalPrintFrame.style.height = height + 'px';
                    thermalPrintFrame.style.width = width + 'px';
                    stopPageLoader();
                }, 500);
            };
        },
    });
};

window.printPDF = function (url) {
    $.ajax({
        type: "GET",
        url: url,
        success: function (result) {
            $(".download-print-pdf").attr("href", result.data.data.downloadRoute);
            if (result.data.data.emailRoute == null) {
                $(".share-pdf-to-email").addClass('d-none');
            } else {
                $(".share-pdf-to-email").removeClass('d-none');
                $(".share-pdf-to-email").attr("href", result.data.data.emailRoute);
            }
            if ((result.data.data.transactionId == null && result.data.data.transactionType == null) || result.data.data.is_thermal_print) {
                $(".duplicate-triplicate-checkbox").addClass('d-none');
            } else {
                $("#duplicateInvoice").attr("data-type", result.data.data.transactionType);
                $("#triplicateInvoice").attr("data-type", result.data.data.transactionType);
                $(".duplicate-triplicate-checkbox").removeClass('d-none');
            }
            transactionId = result.data.data.transactionId
            originalTitle = document.title;
            var contents = result.data.html;
            fileName = result.data.fileName;
            thermalPrintFrame = document.createElement("iframe");
            thermalPrintFrame.style.width = '794px';
            thermalPrintFrame.style.height = '700px';
            thermalPrintFrame.id = 'thermalPrintFrame';
            thermalPrintFrame.srcdoc = contents;
            var transactionPdfPreview = document.querySelector('.transaction-pdf-preview');
            if (transactionPdfPreview) {
                transactionPdfPreview.innerHTML = '';
                transactionPdfPreview.appendChild(thermalPrintFrame);
                if (result.data.data.is_thermal_print) {
                    $(".share-pdf-to-email").addClass("d-none");
                    $(".download-print-pdf").addClass("d-none");
                    $(".sale-pdf-preview").attr("style", "height: auto !important; width: auto !important");
                    $(".sale-pdf-preview-modal").removeClass('modal-xl');
                    $(".sale-pdf-preview-modal").addClass('modal-md');
                } else if (result.data.data.isA5Pdf) {
                    $(".sale-pdf-preview-modal").removeClass('modal-xl');
                    $(".sale-pdf-preview-modal").addClass('modal-xl');
                    $(".sale-pdf-preview").attr("style", "height: auto !important; width: auto !important");
                } else {
                    $(".sale-pdf-preview-modal").removeClass('modal-md');
                    $(".sale-pdf-preview-modal").addClass('modal-xl');
                    $(".sale-pdf-preview").attr("style", "margin:0 auto !important; width: auto !important");
                }
                $("#salePdfPreviewModal").appendTo("body").modal("show");
            }

            thermalPrintFrame.onload = function () {
                setTimeout(function () {
                    let height = 0;
                    let width = 0
                    if (result.data.data.isA5Pdf) {
                        height = '794'
                        width = '560'
                    } else if (result.data.data.is_thermal_print) {
                        height = '0'
                        width = '350'
                    } else if (result.data.data.isLandscape) {
                        width = "1190";
                        $(".modal-xl").css("max-width", "1220px");
                    } else {
                        height = "1123";
                        width = '794';
                    }
                    if (!result.data.data.is_thermal_print) {
                        if (width < thermalPrintFrame.contentWindow.document.body.scrollWidth + 20) {
                            width = thermalPrintFrame.contentWindow.document.body.scrollWidth + 20
                        }
                    }
                    thermalPrintFrame.style.width = width + 'px';
                    stopPageLoader();
                    let printWindow = thermalPrintFrame.contentWindow || thermalPrintFrame.contentDocument;
                    if (printWindow) {
                        printWindow.focus();
                        printWindow.print();
                    }
                }, 500);

            };
        }
    });
}
window.pdfPreviewPurchaseModal = function (url) {
    $.ajax({
        type: "GET",
        url: url,
        success: function (result) {
            if (result.data.status == 'completed') {
                $("#purchasePdfPreviewModal").appendTo("body").modal("show");
                startPageLoader();
                $(".transaction-pdf-preview").empty().append(result.data.html);
                $(".download-print-pdf").attr("href", result.data.downloadRoute);
                $(".view-print-pdf").attr("href", result.data.viewRoute);
                $(".share-pdf-to-email").attr("href", result.data.emailRoute);
                if (!result.data.mailConfigurationIsset) {
                    $(".share-pdf-to-email").addClass("disabled");
                }
            }
            if (result.data.status == 'failed') {
                stopPageLoader();
                Swal.fire({
                    title: "",
                    text: result.data.message,
                    confirmButtonColor: "#009ef7",
                    icon: "error",
                    timer: 5000,
                });
            }
        },
    });
};

listen("wheel", "input[type=number]", function (e) {
    $(this).blur();
});

listenChange(".email-attachments", function (e) {
    var files = e.target.files,
        filesLength = files.length;
    for (var i = 0; i < filesLength; i++) {
        var f = files[i];
        $(".file-list").append(
            '<span class="pip">' +
            "<br/><span class=\"remove badge bg-info mt-1\"><i class='fa fa-times ms-1 me-1'></i>" +
            f.name +
            "</span>" +
            "</span>"
        );
        let removedFile = [];
        $(".remove").click(function () {
            removedFile.push($(this).text());
            $("#removedAttachment").val(removedFile.join(','));
            $(this).parent(".pip").remove();
        });
    }
});

listenClick(".company-impersonate", function () {
    let id = $(this).data("id");

    let element = document.createElement("a");
    element.setAttribute("href", route("franchise.impersonate", id));
    element.setAttribute("data-turbo", "false");
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    $(".company-impersonate").prop("disabled", true);
});

$(document).on("keydown", function (event) {
    if (event.key == "Escape") {
        $(".text-reset").trigger("click");
    }
});

window.getINRCurrencyFormat = function (amount) {
    return Number(amount).toLocaleString("en-IN", {
        maximumFractionDigits: fixDigit,
    });
};

window.screenLock = function () {
    $("body").css({ "pointer-events": "none", opacity: "0.6" });
};

window.screenUnLock = function () {
    $("body").css({ "pointer-events": "auto", opacity: "1" });
};

window.startLoader = function () {
    $(".loader").removeClass("d-none");
};
window.startPageLoader = function () {
    $(".page-loader").removeClass("d-none");
};

window.stopLoader = function () {
    $(".loader").addClass("d-none");
};
window.stopPageLoader = function () {
    $(".page-loader").addClass("d-none");
};

function initLoader() {
    stopLoader();
}

window.onload = function () {
    stopLoader();
};

document.addEventListener("success", function (data) {
    displaySuccessMessage(data.detail);
});
document.addEventListener("error", function (data) {
    displayErrorMessage(data.detail);
});

listenClick(".franchise-impersonate", function () {
    let id = $(this).data("id");

    let element = document.createElement("a");
    element.setAttribute("href", route("admin.impersonate", id));
    element.setAttribute("data-turbo", "false");
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    $(".franchise-impersonate").prop("disabled", true);
});

window.getQueryStringParams = function (sParam) {
    var sPageURL = window.location.search.substring(1);
    var sURLVariables = sPageURL.split("&");

    for (var i = 0; i < sURLVariables.length; i++) {
        var sParameterName = sURLVariables[i].split("=");
        if (sParameterName[0] == sParam) {
            return sParameterName[1];
        }
    }
};

listenClick(".company-dashboard-add-btn", function (e) {
    e.preventDefault();
    $(".company-dashboard-add-btn").addClass("d-none");
    $(".company-dashboard-report-select2").removeClass("d-none");
});

listenChange(".company-dashboard-report-select2", function (e) {
    e.preventDefault();
    $(".company-dashboard-add-btn").addClass("d-none");
    $.ajax({
        url: route("company.get-dashboard-button"),
        type: "get",
        dataType: "json",
        data: { buttonId: $(this).val() },
        success: function (data) {
            displaySuccessMessage(data.message);
            location.href = route("company.dashboard");
        },
    });
});

listenClick(".company-dashboard-button", function (e) {
    e.preventDefault();
    let buttonId = $(this).attr("data-button-id");
    Swal.fire({
        title: "Delete !",
        text: "Are you sure want to delete this Report ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#D9214E",
        cancelButtonText: "No, Cancel",
        confirmButtonText: "Yes, Delete",
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: route("company.dashboard-button.destroy"),
                type: "delete",
                dataType: "json",
                data: { buttonId: buttonId },
                success: function (obj) {
                    Swal.fire({
                        icon: "success",
                        title: "Deleted!",
                        confirmButtonColor: "#009ef7",
                        text: "Report has been deleted.",
                        timer: 2000,
                    });
                    if (obj.success) {
                        displaySuccessMessage(obj.message);
                        location.href = route("company.dashboard");
                    }
                },
                error: function (data) {
                    Swal.fire({
                        title: "",
                        text: data.responseJSON.message,
                        confirmButtonColor: "#009ef7",
                        icon: "error",
                        timer: 5000,
                    });
                },
            });
        }
    });
});

window.setCurrentFinancialYearDate = () => {
    let financialYear = companyFilter.current_financial_year
        ? companyFilter.current_financial_year.split(" - ")
        : "";

    let startYear = financialYear[0] ?? financialYearStartDate().split("/")[2];
    let endYear = financialYear[1] ?? financialYearEndDate().split("/")[2];

    currentFinancialYearStartDate = "01/04/" + startYear;
    currentFinancialYearEndDate = "31/03/" + endYear;

    let currentStartYear = currentFinancialYear().startYear;
    let currentEndYear = currentFinancialYear().endYear;

    cancelFYStartDate =
        currentStartYear > startYear
            ? endYear + "-03-01"
            : moment().startOf("month").format("YYYY-MM-DD");
    cancelFYEndDate =
        currentEndYear > endYear
            ? endYear + "-03-31"
            : moment().endOf("month").format("YYYY-MM-DD");
};

window.currentFinancialYear = () => {
    const currentYear = moment().year();
    let startYear;
    let endYear;

    if (moment().month() < 3) {
        startYear = currentYear - 1;
        endYear = currentYear;
    } else {
        startYear = currentYear;
        endYear = currentYear + 1;
    }

    return {
        startYear: startYear,
        endYear: endYear,
    };
};

window.financialYearStartDate = function () {
    if (!currentFinancialYearStartDate) {
        return "01/04/" + currentFinancialYear().startYear;
    }
    return currentFinancialYearStartDate;
};

window.financialYearEndDate = function () {
    if (!currentFinancialYearEndDate) {
        return "31/03/" + currentFinancialYear().endYear;
    }
    return currentFinancialYearEndDate;
};

window.tillStartDate = function () {
    return currentFinancialYearStartDate;
};

window.tillEndDate = function () {
    const now = moment();
    const currentYearEndDate = moment(
        currentFinancialYearEndDate,
        "DD/MM/YYYY"
    );
    return now.isBefore(currentYearEndDate) ? now : currentYearEndDate;
};

listenClick("body", function () {
    $(".offcanvas").removeClass("show");
});

window.fixDigit = fixDigitNumber;
window.fix5Digit = fix5DigitNumber;

listenSubmit("#columnSelectorForm", function (e) {
    e.preventDefault();

    let type = $(this).find("[name=column_selector_type]").val();

    $.ajax({
        type: "POST",
        url: route("company.column-selector-field.update", { type: type }),
        data: $(this).serialize(),
        success: function (result) {
            displaySuccessMessage(result.message);
            $("#columnSelectorModal").modal("hide");
            Livewire.emit("refresh");
        },
        error: function (error) {
            displayErrorMessage(error.responseJSON.message);
        },
    });
});

listenClick("#columnSelectorButton", function (e) {
    e.preventDefault();

    let type = $(this).attr("data-type");

    $.ajax({
        type: "GET",
        url: route("company.column-selector-field", { type: type }),
        data: $(this).serialize(),
        success: function (result) {
            let html = result.data;
            $("#columnSelectorDiv").empty();
            $("#columnSelectorDiv").append(html);
            $("#columnSelectorModal").appendTo("body").modal("show");
        },
        error: function (error) {
            displayErrorMessage(error.responseJSON.message);
        },
    });
});

listenClick("#selectAllColumnFields", function () {
    if ($(this).prop("checked")) {
        $(".column-field").prop("checked", true);
    } else {
        $(".column-field").prop("checked", false);
    }
});

listenShownBsModal("#columnSelectorModal", function () {
    if ($(".column-field:not(:checked)").length) {
        $("#selectAllColumnFields").prop("checked", false);
    } else {
        $("#selectAllColumnFields").prop("checked", true);
    }
});

function setUrlForSendWhatsApp(invoiceType, phone, id, date = "") {
    let url = "";

    if (invoiceType == "income-estimate-quote" && phone != "") {
        url = route("company.income-estimate-quote.send-whatsapp", {
            estimate_quote: id,
            phone: phone,
        });
    }

    if (invoiceType == "sale" && phone != "") {
        url = route("company.sale-send-whatsapp", { sale: id, phone: phone });
    }

    if (invoiceType == "sale-return" && phone != "") {
        url = route("company.sale-return-send-whatsapp", {
            sale_return: id,
            phone: phone,
        });
    }

    if (invoiceType == "income-debit-note" && phone != "") {
        url = route("company.income-dr-note-send-whatsapp", {
            income_debit_note: id,
            phone: phone,
        });
    }

    if (invoiceType == "income-credit-note" && phone != "") {
        url = route("company.income-cr-note-send-whatsapp", {
            income_credit_note: id,
            phone: phone,
        });
    }

    if (invoiceType == "receipt" && phone != "") {
        url = route("company.receipt-transaction-send-whatsapp", {
            receipt: id,
            phone: phone,
        });
    }

    if (invoiceType == "payment" && phone != "") {
        url = route("company.payment-transaction-send-whatsapp", {
            payment: id,
            phone: phone,
        });
    }

    if (invoiceType == "delivery-challan" && phone != "") {
        url = route("company.delivery-challan-transaction-send-whatsapp", {
            delivery_challan: id,
            phone: phone,
        });
    }

    if (invoiceType == "purchase-order" && phone != "") {
        url = route("company.purchase-order-transaction-send-whatsapp", {
            order: id,
            phone: phone,
        });
    }

    if (invoiceType == "customer-summary-report" && phone != "" && date != "") {
        url = route("company.customer-summary-report.send-whatsapp-manually", {
            ledger: id,
            phone: phone,
            date: date,
        });
    }

    if (invoiceType == "supplier-summary-report" && phone != "" && date != "") {
        url = route("company.supplier-summary-report.send-whatsapp-manually", {
            ledger: id,
            phone: phone,
            date: date,
        });
    }

    if (url != "") {
        $("#sendWhatsAppButton").attr("href", url);
        $("#sendWhatsAppButton").attr("target", "_blank");
    }
}

listenClick("#sendWhatsappModel", function () {
    let invoiceType = $(this).attr("data-type");
    let phone = $(this).attr("data-phone");
    let id = $(this).attr("data-id");

    $("#sendInvoiceType").val(invoiceType);
    $("#sendInvoiceId").val(id);
    $("#sendInvoicePhoneNumber").val(phone);

    $("#sendWhatsAppButton").attr("href", 'javascript:void(0)');
    $("#sendWhatsAppButton").removeAttr("target");

    if ($("#customerSummaryReportIndex").length > 0) {
        let date = $("#customerSummaryReportDateFilter").val() ?? "";

        setUrlForSendWhatsApp(invoiceType, phone, id, date);
    } else if ($("#supplierSummaryReportIndex").length > 0) {
        let date = $("#supplierSummaryReportDateFilter").val() ?? "";

        setUrlForSendWhatsApp(invoiceType, phone, id, date);
    } else {
        setUrlForSendWhatsApp(invoiceType, phone, id);
    }

    $("#sendInvoiceModal").appendTo("body").modal("show");
});
listenClick("#contactUsSupport", function () {
    $("#contactUsSupportModal").appendTo("body").modal("show");
});

listenKeyup("#sendInvoicePhoneNumber", function () {
    let invoiceType = $("#sendInvoiceType").val();
    let id = $("#sendInvoiceId").val();
    let phone = $(this).val();

    if ($("#customerSummaryReportIndex").length > 0) {
        let date = $("#customerSummaryReportDateFilter").val() ?? "";

        setUrlForSendWhatsApp(invoiceType, phone, id, date);
    } else if ($("#supplierSummaryReportIndex").length > 0) {
        let date = $("#supplierSummaryReportDateFilter").val() ?? "";

        setUrlForSendWhatsApp(invoiceType, phone, id, date);
    } else {
        setUrlForSendWhatsApp(invoiceType, phone, id);
    }
});

listenClick("#sendWhatsAppButton", function () {
    if ($("#sendInvoicePhoneNumber").val() != "") {
        $("#sendInvoiceModal").modal("hide");
    } else {
        displayErrorMessage(
            "Please enter mobile number for send invoice in WhatsApp."
        );
    }
});

window.appendOptionToStateSelect = async function (attr, countryId) {
    let response = await $.ajax({
        url: route("states.list"),
        type: "get",
        dataType: "json",
        data: { countryId: countryId },
    });

    if (response.success) {
        attr.empty();
        attr.append($('<option value=""></option>').text("Select State"));
        $.each(response.data, function (i, v) {
            attr.append($("<option></option>").attr("value", v).text(i));
        });
        // initSelect2();
    }
};

window.appendOptionToCitySelect = async function (attr, stateId) {
    let response = await $.ajax({
        url: route("cities.list"),
        type: "get",
        dataType: "json",
        data: {
            stateId: stateId,
        },
    });

    if (response.success) {
        attr.empty();
        attr.append($('<option value=""></option>').text("Select City"));
        $.each(response.data, function (i, v) {
            attr.append($("<option></option>").attr("value", i).text(v));
        });
        // initSelect2();
    }
};

window.updateCompanyFilter = function (key, value) {
    $.ajax({
        url: route("company.company-filters"),
        type: "post",
        dataType: "json",
        data: {
            key: key,
            value: value,
        },
    });
};

window.reformatDateString = function (date) {
    return date.split(/\D/).reverse().join("-");
};

// Get the URL parameter value
window.getUrlParameter = function (name) {
    name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
    var regex = new RegExp("[\\?&]" + name + "=([^&#]*)");
    var results = regex.exec(location.search);
    return results === null
        ? ""
        : decodeURIComponent(results[1].replace(/\+/g, " "));
};

window.bulkDeleteItem = function (url, header, ids, callFunction = null) {
    Swal.fire({
        title: "Delete !",
        text: 'Are you sure want to delete this "' + header + '" ?',
        // icon: "warning",
        imageUrl: deleteImage,
        imageWidth: 221,
        imageHeight: 193,
        showCancelButton: true,
        confirmButtonColor: "#D9214E",
        cancelButtonText: "No, Cancel",
        confirmButtonText: "Yes, Delete",
    }).then((result) => {
        if (result.isConfirmed) {
            bulkDelete(ids, url, (callFunction = null));
        } else {
            livewire.emit("refresh");
            $('input[type="checkbox"]').not('.status-checkbox').prop('checked', false);
        }
    });
};

window.bulkDelete = function (ids, url, callFunction = null) {
    $.ajax({
        type: "POST",
        url: url,
        data: { ids: ids },
        success: function (result) {
            if (result.message != "") {
                livewire.emit("refreshDatatable");
                livewire.emit("refresh");
                $("#selectAllTransaction").prop("checked", false);
                $(".select-all-transaction").prop("checked", false);
                $(".delete-transaction").prop("checked", false);
                $('#selectAllNotifications').prop('checked', false);
                $('.select-notification').prop('checked', false);
                if (result.data && result.data.showMode) {
                    $("#deleteItemModel").modal("show");
                    $(".item-list-append").empty().append(result.data.html);
                }
                Swal.fire({
                    icon: "success",
                    title: "Deleted!",
                    confirmButtonColor: "#009ef7",
                    text: result.message,
                    timer: 2000,
                });
                if (callFunction) {
                    eval(callFunction);
                }
            }
        },
        error: function (data) {
            livewire.emit("refreshDatatable");
            livewire.emit("refresh");
            $('input[type="checkbox"]').prop("checked", false);
            Swal.fire({
                title: "",
                text: data.responseJSON.message,
                confirmButtonColor: "#009ef7",
                icon: "error",
                timer: 5000,
            });
        },
    });
};
window.showItemCurrentStock = function (itemId, element, rowElement = null) {
    $.ajax({
        url: route("company.get-item-closing-stock", itemId),
        type: "GET",

        success: function success(result) {
            if (rowElement != null) {
                rowElement.find("[good-stock-quantity=" + element + "]")
                    .text(formatIntAndFloat(result.data[0].closing_balance_qty) + " " + result.data[1].model.unit_of_measurement.code);
            } else {
                $(document).find("[good-stock-quantity=" + element + "]")
                    .text(formatIntAndFloat(result.data[0].closing_balance_qty) + " " + result.data[1].model.unit_of_measurement.code);
            }
        },
        error: function error(_error) {
            displayErrorMessage(_error.responseJSON.message);
        },
    });
};

window.updateIncomeConfigurationFormRender = function (type) {
    $.ajax({
        type: "GET",
        url: route("company.update-income-configuration-form-render", {
            type: type,
        }),
        success: function (result) {
            if (type == 1) {
                /* 1 == App\Models\Master\Income::SALES */
                $("#saleIncomeConfiguration").empty().append(result.data.html);
            }
            if (type == 2) {
                /* 2 == App\Models\Master\Income::SALES_RETURN */
                $("#saleReturnIncomeConfiguration")
                    .empty()
                    .append(result.data.html);
            }
            if (type == 3) {
                /* 3 == App\Models\Master\Income::DIRECT_INCOME */
                $("#incomeDebitNoteConfiguration")
                    .empty()
                    .append(result.data.html);
            }
            if (type == 4) {
                /* 4 == App\Models\Master\Income::INDIRECT_INCOME */
                $("#incomeCreditNoteConfiguration")
                    .empty()
                    .append(result.data.html);
            }
            if (type == 5) {
                /* 5 == App\Models\Master\Income::INCOME_ESTIMATE_QUOTE */
                $("#incomeEstimateQuoteConfigurationData")
                    .empty()
                    .append(result.data.html);
            }

            if (type == 6) {
                /* 6 == App\Models\Master\Income::DELIVERY_CHALLAN */
                $("#deliveryChallanIncomeConfiguration")
                    .empty()
                    .append(result.data.html);
            }
        },
    });
};

window.updateExpenseConfigurationFormRender = function (type) {
    $.ajax({
        type: "GET",
        url: route("company.update-expense-configuration-form-render", {
            type: type,
        }),
        success: function (result) {
            if (type == 1) {
                /* 1 == App\Models\Master\Expense::PURCHASE */
                $("#purchaseConfiguration").empty().append(result.data.html);
            }
            if (type == 2) {
                /* 2 == App\Models\Master\Expense::PURCHASE_RETURN */
                $("#purchaseReturnConfiguration")
                    .empty()
                    .append(result.data.html);
            }
            if (type == 3) {
                /* 3 == App\Models\Master\Expense::DIRECT_EXPENSE */
                $("#expenseDebitNoteConfiguration")
                    .empty()
                    .append(result.data.html);
            }
            if (type == 4) {
                /* 4 == App\Models\Master\Expense::INDIRECT_EXPENSE */
                $("#expenseCreditNoteConfiguration")
                    .empty()
                    .append(result.data.html);
            }

            if (type == 5) {
                /* 5 == App\Models\Master\Expense::PURCHASE_ORDER */
                $("#purchaseOrderConfigurationData")
                    .empty()
                    .append(result.data.html);
            }
        },
    });
};

listenClick('.logout-all-device', function () {
    $('#logoutAllDeviceModal').modal('show');
})

window.getDifferenceBetweenDebitAndCreditSide = function (addLedger = null, divElement = 1) {
    let journalCreditAmount = $('.journal-credit-amount').map((i, e) => parseFloat(e.value) || 0).get();
    let journalDebitAmount = $('.journal-debit-amount').map((i, e) => parseFloat(e.value) || 0).get();

    let journalDebitTotalAmountSum = journalDebitAmount.reduce((sum, value) => sum + value, 0);
    let journalCreditTotalAmountSum = journalCreditAmount.reduce((sum, value) => sum + value, 0);

    let journalCreditIsEmpty = $('.journal-credit-amount:last').data('journal-credit-amount');
    let journalDebitIsEmpty = $('.journal-debit-amount:last').data('journal-debit-amount');
    let creditLastVal = $('[data-journal-credit-amount="' + journalCreditIsEmpty + '"]').val();
    let debitLastVal = $('[data-journal-debit-amount="' + journalDebitIsEmpty + '"]').val();

    if (addLedger === 'IsCredit' && creditLastVal == 0) {
        divElement = journalCreditIsEmpty;
        let differenceCreditSide = journalDebitTotalAmountSum - journalCreditTotalAmountSum;

        if (differenceCreditSide >= 0) {
            $('[data-journal-credit-amount="' + divElement + '"]').val(differenceCreditSide);
            $(".credit-currency-symbol").addClass("d-none");
            $(".journal-credit-total-amount").text(getSettingCurrencySymbol + '' + inrCurrencyFormat(journalDebitTotalAmountSum));
            $(".hidden-credit-total-amount").val(journalDebitTotalAmountSum.toFixed(fixDigit));
        }
    }

    if (addLedger === 'IsDebit' && debitLastVal == 0) {
        divElement = journalDebitIsEmpty;
        let differenceDebitSide = journalCreditTotalAmountSum - journalDebitTotalAmountSum;

        if (differenceDebitSide >= 0) {
            $('[data-journal-debit-amount="' + divElement + '"]').val(differenceDebitSide);
            $(".debit-currency-symbol").addClass("d-none");
            $(".journal-debit-total-amount").text(getSettingCurrencySymbol + '' + inrCurrencyFormat(journalCreditTotalAmountSum));
            $(".hidden-debit-total-amount").val(journalCreditTotalAmountSum.toFixed(fixDigit));
        }
    }
};

window.debounce = function (func, delay) {
    let timeoutId;
    return function () {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(this, arguments);
        }, delay);
    };
}

window.formatDate = function (date, instance) {
    // Split the date into parts

    var dateParts = date.split('-');
    var twoDigitYear = parseInt(dateParts[2], 10);
    // Extract the year

    if (parseInt(twoDigitYear).toString().length == 2) {
        // Add century to the year
        var fourDigitYear = (twoDigitYear < 50 ? 2000 + twoDigitYear : 1900 + twoDigitYear);

        // Update the year in the dateParts array
        dateParts[2] = fourDigitYear.toString();
        // Reconstruct the date string
        date = dateParts.join('-');
        instance.setDate(date, "DD-MM-YYYY");
    }
}

window.customDateFormate = function (date, minDate, maxDate, instance) {
    var inputValue = date.replace(/[-.]/g, ".");
    var dateParts = inputValue.split(/[-.]/);

    var day = dateParts[0].padStart(2, '0');  // Pad day with leading zero if needed
    var month = dateParts[1].padStart(2, '0');  // Pad month with leading zero if needed

    // Extract year from min and max dates
    var minYear = moment(minDate, "DD/MM/YYYY").toDate().getFullYear();
    var maxYear = moment(maxDate, "DD/MM/YYYY").toDate().getFullYear();
    // Decide the year based on the month
    var year;
    if (parseInt(month) < 4) {
        // If month is January to March, take year from min date
        year = maxYear;
    } else {
        // For April onwards, take year from max date
        year = minYear;
    }

    return `${day}-${month}-${year}`;
}

function debounce(func, delay) {
    let timeoutId;
    return function () {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(this, arguments);
        }, delay);
    };
}

const debouncedGlobalSearch = debounce(handleGlobalSearch, 300);
listenKeyup($('.global-search-box'), debouncedGlobalSearch);

function handleGlobalSearch() {
    let isGlobalSearchBoxFocus = $('.global-search-box').is(":focus");
    if (isGlobalSearchBoxFocus) {
        let searchValue = $('.global-search-box').val();
        $.ajax({
            type: "POST",
            url: route('company.global-search'),
            data: {
                searchValue: searchValue
            },
            success: function (result) {
                if (result.success) {
                    $(".global-search-list").empty().append(result.data.html);
                }
            }, error: function (data) {
                console.log(data.responseJSON.message);
            }
        });
    }
}

listenKeyup($('.opening_balance'), function () {
    let openingBalance = $('.opening_balance').val();
    if (openingBalance == "") {
        $('.opening_balance').val(0);
    }
});

window.bulkDownloadItem = function (url, header, ids, callFunction = null) {
    // Swal.fire({
    //     title: "Donwload !",
    //     text: 'Are you sure want to download this "' + header + '" ?',
    //     icon: "warning",
    //     showCancelButton: true,
    //     confirmButtonColor: "#D9214E",
    //     cancelButtonText: "No, Cancel",
    //     confirmButtonText: "Yes, Download",
    // }).

    // then((result) => {
    if (ids.length > 0) {
        bulkDownload(ids, url, (callFunction = null));
    }
    // });
};

window.bulkDownload = function (ids, url, callFunction = null) {
    $.ajax({
        type: "POST",
        url: url,
        data: { ids: ids },
        success: function (result) {
            if (result.message != "") {
                livewire.emit("refreshDatatable");
                livewire.emit("refresh");
                $("#selectAllTransaction").prop("checked", false);
                $(".delete-transaction").prop("checked", false);
                // Swal.fire({
                //     icon: "success",
                //     title: "Your export is started, you will be able to check progress into notification bar.!",
                //     confirmButtonColor: "#009ef7",
                //     text: result.message,
                //     timer: 4000,
                // });
                if (callFunction) {
                    eval(callFunction);
                }
            }
        },
        error: function (data) {
            livewire.emit("refreshDatatable");
            livewire.emit("refresh");
            $('input[type="checkbox"]').prop("checked", false);
            Swal.fire({
                title: "",
                text: data.responseJSON.message,
                confirmButtonColor: "#009ef7",
                icon: "error",
                timer: 5000,
            });
        },
    });
};

window.downloadReportAjax = function (url, header, data, callFunction = null) {
    Swal.fire({
        title: "Download !",
        text: 'Are you sure want to download this "' + header + '" ?',
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#D9214E",
        cancelButtonText: "No, Cancel",
        confirmButtonText: "Yes, Download",
    }).then((result) => {
        if (result.isConfirmed) {
            downloadReport(data, url, (callFunction = null));
        }
    });
};

window.downloadReport = function (data, url, callFunction = null) {
    $.ajax({
        type: "POST",
        url: url,
        data: data,
        success: function (result) {
            if (result.message != "") {
                livewire.emit("refreshDatatable");
                livewire.emit("refresh");
                Swal.fire({
                    icon: "success",
                    title: "Your export is started, you will be able to check progress into notification bar.!",
                    confirmButtonColor: "#009ef7",
                    text: result.message,
                    timer: 4000,
                });
                if (callFunction) {
                    eval(callFunction);
                }
            }
        },
        error: function (data) {
            Swal.fire({
                title: "",
                text: data.responseJSON.message,
                confirmButtonColor: "#009ef7",
                icon: "error",
                timer: 5000,
            });
        },
    });
};

listenClick('.logout-password-model', function () {
    $('#logoutPasswordModel').modal('show')
    let password = $('.logout-password').val('');
})

listenClick('.logout-password-btn', function () {
    let password = $('.logout-password').val();

    window.livewire.emit('logoutAllDevice', password)

    $('#logoutPasswordModel').modal('hide');
})

document.addEventListener("DOMContentLoaded", function () {
    const menuItems = document.querySelectorAll(".menu-item");
    menuItems.forEach((item) => {
        const submenu = item.querySelector(".menu-sub-accordion");
        const isInUtilities = item.classList.contains("utilities");
        if (submenu) {
            item.addEventListener("mouseenter", function () {
                if (window.innerWidth >= 576) {
                    const itemRect = item.getBoundingClientRect();
                    const containerRect = item
                        .closest(".aside-menu")
                        .getBoundingClientRect();
                    submenu.style.visibility = "hidden";
                    submenu.style.display = "block";
                    const submenuHeight = submenu.offsetHeight;
                    submenu.style.display = "none";
                    submenu.style.visibility = "";
                    const spaceBelow = window.innerHeight - itemRect.bottom;
                    const willOverflow = submenuHeight > spaceBelow;
                    if (isInUtilities && willOverflow) {
                        submenu.classList.add("bottom-fixed");
                    } else {
                        submenu.classList.remove("bottom-fixed");
                    }
                    let topOffset = itemRect.top - containerRect.top;
                    if (isInUtilities && willOverflow) {
                        topOffset -= 100;
                    }
                    submenu.style.top = `${topOffset}px`;
                    submenu.style.display = "block";
                }
            });

            item.addEventListener("mouseleave", function () {
                if (window.innerWidth >= 576) {
                    submenu.style.display = "none";
                    submenu.classList.remove("dropdown-fixed");
                }
            });
        }
    });
});

document.addEventListener("DOMContentLoaded", function () {
    var accordions = document.querySelectorAll(".menu-accordion");
    var moreItem = document.querySelector(".menu-link.more-item");
    var insideMenuLinks = document.querySelectorAll(".inside-menu .menu-link");
    var masterItem = document.querySelector(".menu-link.master-item");
    var menuLinks = document.querySelectorAll(".master-menu .menu-link");
    var hasActiveLink = Array.from(insideMenuLinks).some(function (link) {
        return link.classList.contains("active");
    });
    if (hasActiveLink) {
        moreItem.classList.add("active");
    }
    menuLinks.forEach(function (menuLink) {
        if (menuLink.classList.contains("active")) {
            masterItem.classList.add("active");
        }
    });
    accordions.forEach(function (accordion) {
        var firstMenuLink = accordion.querySelector(".menu-link");
        var subMenu = accordion.querySelector(".menu-sub-accordion");
        var activeLink = subMenu && subMenu.querySelector(".menu-link.active");
        if (firstMenuLink && activeLink) {
            firstMenuLink.classList.add("active");
        }
    });
});

listenChange('.file-upload-validate', function (e) {
    validateFileSize(e.target.files);
});

window.validateFileSize = function (files) {

    let validateStatue = true;

    files.forEach(function (file, index) {
        const fsize = file.size;
        const fileSize = Math.round(fsize / 1024);
        if (fileSize >= 2048) {
            displayErrorMessage(`File : ${file.name} too Big, please select a file less than 2mb.`);
            validateStatue = false;
        }
    });

    return validateStatue;
}
document.addEventListener("DOMContentLoaded", function () {
    const moreMenuItem = document.getElementById("more-menu-item");
    const moreItem = document.getElementById("more-item");
    const insideMenu = document.getElementById("inside-menu");
    const toggleActive = () => {
        const isActive =
            insideMenu.classList.contains("active") ||
            insideMenu.querySelector(".menu-link.active");
        moreItem.classList.toggle("active", isActive);
    };
    const observerCallback = (mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.attributeName === "class") {
                toggleActive();
            }
        });
    };
    const observer = new MutationObserver(observerCallback);
    if (insideMenu != null) {
        observer.observe(insideMenu, { attributes: true });
        insideMenu
            .querySelectorAll(".menu-link")
            .forEach((link) => observer.observe(link, { attributes: true }));
        moreMenuItem.addEventListener("click", () => {
            insideMenu.classList.toggle("active");
            toggleActive();
        });
        toggleActive();
    }

});

document.addEventListener("DOMContentLoaded", function () {
    const subAccordions = document.querySelectorAll(".menu-sub-accordion");

    subAccordions.forEach((subAccordion) => {
        subAccordion.addEventListener("mouseenter", function () {
            const menuLink = subAccordion
                .closest(".menu-item")
                .querySelector(".menu-link");
            menuLink.classList.add("hover");
        });

        subAccordion.addEventListener("mouseleave", function () {
            const menuLink = subAccordion
                .closest(".menu-item")
                .querySelector(".menu-link");
            menuLink.classList.remove("hover");
        });
    });
});

document.addEventListener("DOMContentLoaded", function () {
    var insideMenu = document.getElementById("inside-menu");
    if (insideMenu != null) {
        var menuLinks = insideMenu.querySelectorAll(".menu-link");
        var isActiveLinkPresent = Array.from(menuLinks).some((link) =>
            link.classList.contains("active")
        );
        if (isActiveLinkPresent) {
            insideMenu.classList.add("active");
        }
    }
});

window.bulkMailItem = function (url, header, ids, callFunction = null) {
    Swal.fire({
        title: "Bulk Mail !",
        text: 'Are you sure you want send bulk mail for this "' + header + '" ?',
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#D9214E",
        cancelButtonText: "No, Cancel",
        confirmButtonText: "Yes, BulkMail",
    }).then((result) => {
        if (result.isConfirmed && ids.length > 0) {
            bulkMail(ids, url, (callFunction = null));
        } else {
            window.location.reload();
        }
    });
};

window.bulkMail = function (ids, url, callFunction = null) {
    $.ajax({
        type: "POST",
        url: url,
        data: { ids: ids },
        success: function (result) {
            if (result.message != "") {
                livewire.emit("refreshDatatable");
                livewire.emit("refresh");
                $("#selectAllTransaction").prop("checked", false);
                $(".delete-transaction").prop("checked", false);
                Swal.fire({
                    icon: "success",
                    title: "Your bulk mail is started, you will be able to check progress into notification bar.!",
                    confirmButtonColor: "#009ef7",
                    // text: result,
                    timer: 4000,
                });
                if (callFunction) {
                    eval(callFunction);
                }
            }
        },
        error: function (data) {
            Swal.fire({
                title: "",
                text: data.responseJSON.message,
                confirmButtonColor: "#009ef7",
                icon: "error",
                timer: 5000,
            });
        },
    });
};

window.storeValueWithExpiry = function (key, value) {
    localStorage.setItem(key, value);
};

document.addEventListener("DOMContentLoaded", (event) => {
    const phoneInput = document.querySelector(".custom-floating-input");
    const phoneLabel = document.querySelector(".phone-no-label");
    const flagContainer = document.querySelector(".iti__selected-flag");

    if (phoneInput != null) {
        phoneInput.addEventListener("focus", () => {
            phoneInput.classList.add("border-primary");
            if (phoneLabel) {
                phoneLabel.classList.add("phone-no-label-focus");
            }
            if (flagContainer) {
                flagContainer.classList.add("iti__selected-flag-focused");
            }
        });

        phoneInput.addEventListener("blur", () => {
            if (phoneInput.value.trim() === "") {
                phoneInput.classList.remove("border-primary");
                if (phoneLabel) {
                    phoneLabel.classList.remove("phone-no-label-focus");
                }
                if (flagContainer) {
                    flagContainer.classList.remove("iti__selected-flag-focused");
                }
            }
        });
    }

});

listenClick('.view-print-pdf', function () {
    if (thermalPrintFrame) {
        document.title = fileName;
        setTimeout(function () {
            console.log(thermalPrintFrame.contentWindow)
            thermalPrintFrame.contentWindow.focus();
            thermalPrintFrame.contentWindow.print();
            stopPageLoader();
            document.body.removeChild(thermalPrintFrame);
            document.title = originalTitle;
        }, 500);
    } else {
        Swal.fire({
            title: "",
            text: 'Something Went Wrong !',
            confirmButtonColor: "#009ef7",
            icon: "error",
            timer: 5000,
        });
    }
});

listenHiddenBsModal('#salePdfPreviewModal', function () {
    $('title').text(pageTitle);
    $('#triplicateInvoice').prop('checked', false)
    $('#duplicateInvoice').prop('checked', false)
});

listenClick("#duplicateInvoice", function () {
    let transactionType = $(this).attr("data-type");
    let url = "";

    if (transactionType === "sale") {
        if ($(this).is(":checked")) {
            url = route("company.sales-pdf-preview", {
                salePdfPreviewId: transactionId,
                printType: 2,
            });
        } else {
            url = route("company.sales-pdf-preview", {
                salePdfPreviewId: transactionId,
                printType: 1,
            });
        }
    } else if (transactionType === "delivery_challan") {
        if ($(this).is(":checked")) {
            url = route("company.delivery-challan.pdf-preview", {
                delivery_challan: transactionId,
                printType: 2,
            });
        } else {
            url = route("company.delivery-challan.pdf-preview", {
                delivery_challan: transactionId,
                printType: 1,
            });
        }
    } else if (transactionType === "estimate_quote") {
        if ($(this).is(":checked")) {
            url = route("company.income-estimate-quote.pdf-preview", {
                estimate_quote: transactionId,
                printType: 2,
            });
        } else {
            url = route("company.income-estimate-quote.pdf-preview", {
                estimate_quote: transactionId,
                printType: 1,
            });
        }
    }

    if (url !== "") {
        if ($(this).is(":checked")) {
            pdfPreviewModal(url);
            $("#triplicateInvoice").prop("checked", false);
        } else {
            pdfPreviewModal(url);
        }
    }
});
listenClick('#triplicateInvoice', function () {
    let transactionType = $(this).attr("data-type");
    let url = "";

    if (transactionType === "sale") {
        if ($(this).is(":checked")) {
            url = route("company.sales-pdf-preview", {
                salePdfPreviewId: transactionId,
                printType: 3,
            });
        } else {
            url = route("company.sales-pdf-preview", {
                salePdfPreviewId: transactionId,
                printType: 1,
            });
        }
    } else if (transactionType === "delivery_challan") {
        if ($(this).is(":checked")) {
            url = route("company.delivery-challan.pdf-preview", {
                delivery_challan: transactionId,
                printType: 3,
            });
        } else {
            url = route("company.delivery-challan.pdf-preview", {
                delivery_challan: transactionId,
                printType: 1,
            });
        }
    } else if (transactionType === "estimate_quote") {
        if ($(this).is(":checked")) {
            url = route("company.income-estimate-quote.pdf-preview", {
                estimate_quote: transactionId,
                printType: 3,
            });
        } else {
            url = route("company.income-estimate-quote.pdf-preview", {
                estimate_quote: transactionId,
                printType: 1,
            });
        }
    }

    if (url !== "") {
        if ($(this).is(":checked")) {
            pdfPreviewModal(url);
            $('#duplicateInvoice').prop('checked', false);
        } else {
            pdfPreviewModal(url);
        }
    }
});

$(document).ready(function () {
    $('.change-sale-eq-document-number').on('select2:close', function () {
        let estimateQuoteIds = $(this).val();
        let filteredData = estimateQuoteIds.filter(item => item !== '');

        if (filteredData.length) {
            $('.default-sale-estimate-no').remove();
            handleInvoiceNumber(filteredData);
        }
    });
});

$(document).ready(function () {
    $(".change-sale-dc-document-number").on("select2:close", function () {
        let deliveryChallanIds = $(this).val();
        let filteredData = deliveryChallanIds.filter((item) => item !== "");

        if (filteredData.length) {
            $(this).val(filteredData).trigger('change');
            $(".default-sale-challan-no").remove();
            handleDeliveryChallanNumber(filteredData);
        }
    });
});

$(document).ready(function () {
    $(".change-purchase-order-number").on("select2:close", function () {
        let purchaseOrderIds = $(this).val();
        let filteredData = purchaseOrderIds.filter((item) => item !== "");

        if (filteredData.length) {
            $(this).val(filteredData).trigger("change");
            $(".default-purchase-order-no").remove();
            handlePurchaseOrederNumber(filteredData);
        }
    });
});

function showMultiplePurchaseOrderDataErrorModal(data) {
    $(".append-delivery-challan-content").text('There is a difference between multiple purchase order transaction, Choose one transaction.')
    $('#dataItemList').empty();
    if (data.matchedInputName.length) {
        let unMatchedFieldMessage = `Different Value Field : ${data.matchedInputName}`;
        $('#diffrentFieldName').text(unMatchedFieldMessage);
    }
    $('#dataItemList').attr({ 'data-purchase-order': data.purchaseOrderIds, 'data-invoice-type': data.invoiceType });
    Object.entries(data.invoiceNumberModalList).forEach(function ([id, invoice]) {
        $('#dataItemList').append(`
            <tr>
                <th class="text-start">${invoice}</th>
                <td class="ms-5 text-end"><a href="javascript:void(0)" data-id=${id} type="button" class="btn btn-sm btn-primary modal-purchase-order-invoice">Continue</a></td>
            </tr>`);
    });
    $('#multipleInvoiceDataErrorModal').appendTo('body').modal('show');
}

function showMultiplePurchaseOrderErorrModal(data) {
    $('.invoive-type-button').attr('data-purchase-order', data.purchaseOrderIds);
    $('.invoive-type-button').addClass('purchase-order-type-button').removeClass('invoive-type-button');
    $('#invoiceTypeTable').empty();
    Object.entries(data.invoiceTypeModalList).forEach(function (item) {
        $('#invoiceTypeTable').append(`
        <tr>
        <td>${item[0]}</td>
        <td>${item[1]}</td>
        </tr>
        `);
    });
    $('#multipleInvoiceErrorModal').appendTo('body').modal('show');
}

listenClick('.modal-purchase-order-invoice', function () {
    let purchaseOrderTransactionId = $(this).attr('data-id');
    let purchaseOrderIds = $('#dataItemList').attr('data-purchase-order');
    let invoiceType = $('#dataItemList').attr('data-invoice-type');
    let purchaseOrderTransactionIdsArray = purchaseOrderIds ? purchaseOrderIds.split(',') : [];
    handlePurchaseOrederNumber(purchaseOrderTransactionIdsArray, invoiceType, purchaseOrderTransactionId);
    $('#multipleInvoiceDataErrorModal').modal('hide');
});
listenClick('.purchase-order-type-button', function () {
    let invoiceType = $(this).attr('data-invoice-type');
    let purchaseOrderTransactionIds = $(this).attr('data-purchase-order');
    let purchaseOrderTransactionIdsArray = purchaseOrderTransactionIds ? purchaseOrderTransactionIds.split(',') : [];
    handlePurchaseOrederNumber(purchaseOrderTransactionIdsArray, invoiceType);
    $('#multipleInvoiceErrorModal').modal('hide');
})
// document.addEventListener("DOMContentLoaded", (event) => {
//     const phoneInput = document.querySelector(".phone-input");
//     const phoneLabel = document.querySelector(".phone-no-label");
//     const flagContainer = document.querySelector(".iti__selected-flag");

//     if(phoneInput != null){
//         if (phoneInput.value) {
//             phoneInput.classList.add("border-primary");
//             phoneLabel.classList.add("phone-no-label-focus");
//             flagContainer.classList.add("iti__selected-flag-focused");
//         }

//         phoneInput.addEventListener("focus", () => {
//             phoneInput.classList.add("border-primary");
//             phoneLabel.classList.add("phone-no-label-focus");
//             flagContainer.classList.add("iti__selected-flag-focused");
//         });

//         phoneInput.addEventListener("blur", () => {
//             if (phoneInput.value.trim() === "") {
//                 phoneInput.classList.remove("border-primary");
//                 phoneLabel.classList.remove("phone-no-label-focus");
//                 flagContainer.classList.remove("iti__selected-flag-focused");
//             }
//         });
//     }
// });

function handlePurchaseOrederNumber(
    purchaseOrderTransactionTds,
    invoiceType = null,
    invoiceNumber = null
) {
    $.ajax({
        type: "POST",
        url: route(
            "company.purchase.manage-multiple-purchase-order-transaction",
            {
                purchaseOrderTransactionTds: purchaseOrderTransactionTds,
                invoiceType: invoiceType ?? "",
                invoiceNumber: invoiceNumber ?? "",
            }
        ),
        beforeSend: function () {
            startLoader();
        },
        success: function (data) {
            let result = data.data;
            if (result.isShowInvoiceTypeModal) {
                showMultiplePurchaseOrderErorrModal(result);
            } else if (result.isShowInvoiceNumberModal) {
                showMultiplePurchaseOrderDataErrorModal(result);
            }

            stopLoader();
            screenUnLock();

            if (
                !result.isShowInvoiceTypeModal &&
                !result.isShowInvoiceNumberModal
            ) {
                startLoader();
                screenLock();
                $(".change-purchase-order-number").val(result.purchaseOrderIds);
                fillPurchaseOrderTransactionData(result);
                $(".sale-items-append").empty().append(result.html);
                initSelect2();
                generatePurchaseUniqueIdForAppendScreen();
            }
        },
    });
}

function fillPurchaseOrderTransactionData(data) {
    let result = data.purchaseOrderTransaction;

    $(".sale-broker-name").val(result.broker_id).trigger("change");
    setTimeout(function () {
        $(".sale-append-brokrage").val(result.brokerage);
        if (result.brokerage_on_value_type == 1) {
            $(".sale-append-brokerage-on-sale").val(1).prop("checked", true);
        }
        if (result.brokerage_on_value_type == 2) {
            $(".sale-append-brokerage-on-purchase")
                .val(2)
                .prop("checked", true);
        }
    }, 1500);
    if ($(".purchase-append-credit-period").length) {
        $(".purchase-append-credit-period").val(result.credit_period);
    }
    $(".sale-append-transport-name").val(result.transport_id).trigger("change");
    $(".sale-append-document-number").val(result.transporter_document_number);
    $(".transporter-vehicle-number").val(result.transporter_vehicle_number);

    $("#isCgstSgstIgstCalculated").val(result.is_cgst_sgst_igst_calculated);
    $("#isGstNa").val(result.is_gst_na);
    let purchaseVoucherDate = new Date();
    let transportDate = new Date();
    let poDate = new Date();
    if (result.voucher_date) {
        purchaseVoucherDate = new Date(result.voucher_date);
    }
    if (result.transporter_document_date) {
        transportDate = new Date(result.transporter_document_date);
        $(".sale-append-transport-date").val(
            moment(transportDate).format("DD-MM-YYYY")
        );
    } else {
        $(".sale-append-transport-date").val("");
    }
    if (result.po_date) {
        poDate = new Date(result.po_date);
    }
    $(".purchase-append-date").val(
        moment(purchaseVoucherDate).format("DD-MM-YYYY")
    );

    //for addresses
    let shippingAddresses = result.shippingAddress;
    let billingAddresses = result.billingAddress;
    let gstin = result.gstin;
    let shippingGstin = result.shipping_gstin;
    let shippingPartyName = result.shipping_name;

    $(".shipping-address-name").val(shippingPartyName);
    $(".shipping-address-gstin").val(shippingGstin);
    appendAddresses(shippingAddresses, billingAddresses, gstin);

    setTimeout(function () {
        $(".sale-append-shipping-charge").val(result.shipping_freight);
        $(".shipping-freight-with-gst").text(result.shipping_freight_with_gst);
        $(".hidden-shipping-freight-with-gst").val(
            result.shipping_freight_with_gst
        );
        $(".sale-append-packing-charge").val(result.packing_charge);
        $(".packing-charge-with-gst").text(result.packing_charge_with_gst);
        $(".purchase-append-hidden-packing-charge-with-gst").val(
            result.packing_charge_with_gst
        );
        $(".hidden-shipping-charge-cgst-tax-amount").val(
            result.shipping_freight_sgst_amount ?? 0
        );
        $(".hidden-shipping-charge-sgst-tax-amount").val(
            result.shipping_freight_cgst_amount ?? 0
        );
        $(".hidden-shipping-charge-igst-tax-amount").val(
            result.shipping_freight_igst_amount ?? 0
        );
        $(".hidden-packing-charge-cgst-tax-amount").val(
            result.packing_charge_sgst_amount ?? 0
        );
        $(".hidden-packing-charge-sgst-tax-amount").val(
            result.packing_charge_cgst_amount ?? 0
        );
        $(".hidden-packing-charge-igst-tax-amount").val(
            result.packing_charge_igst_amount ?? 0
        );
        $(".change-cess-rate").val(result.cess);
        $(".sale-append-cgst-value").val(result.cgst);
        $(".sale-append-sgst-value").val(result.sgst);
        $(".sale-append-igst-value").val(result.igst);
        $(".sale-append-cgst-text").text(result.cgst);
        $(".sale-append-sgst-text").text(result.sgst);
        $(".sale-append-igst-text").text(result.igst);
        $(".sale-append-total-text").text(result.total);
        $(".sale-append-total-value").val(result.total);
        $(".sale-append-round-amount").val(
            result.round_off_amount.toFixed(fixDigit)
        );
        $(".sale-append-grand-total-value").val(result.grand_total);
        $(".sale-append-grand-total-text").text(result.grand_total);
        $(".sale-append-narration").text(result.narration ?? null);
    }, 2500);
    $(".sale-append-classification-nature-type").val(
        result.classificationNatureType ?? null
    );
    $(".sale-append-classification-is-rcm-applicable").val(
        result.isRcmApplicable ?? null
    );

    setTimeout(function () {
        calculateTCSAmount();
        grandTotalCalculation();
        stopLoader();
        screenUnLock();
        loadMaxLength();
    }, 2500);
}

// listenChange(".change-purchase-order-number", function () {
//     let orderId = $(this).val();
//     if (purchaseTransactionId) {
//         screenLock();
//         $.ajax({
//             type: "GET",
//             url: route("company.purchase.get-purchase-order-transaction", {
//                 order: orderId,
//             }),
//             beforeSend: function () {
//                 startLoader();
//             },
//             success: function (data) {
//                 let result = data.data.purchaseOrderTransaction;
//                 $(".sale-broker-name")
//                     .val(result.broker_id)
//                     .trigger("change");
//                 setTimeout(function () {
//                     $(".sale-append-brokrage").val(
//                         result.brokerage
//                     );
//                     if (result.brokerage_on_value_type == 1) {
//                         $(".sale-append-brokerage-on-sale")
//                             .val(1)
//                             .prop("checked", true);
//                     }
//                     if (result.brokerage_on_value_type == 2) {
//                         $(".sale-append-brokerage-on-purchase")
//                             .val(2)
//                             .prop("checked", true);
//                     }
//                 }, 1500);
//                 if ($(".purchase-append-credit-period").length) {
//                     $(".purchase-append-credit-period").val(
//                         result.credit_period
//                     );
//                 }
//                 $(".sale-append-transport-name")
//                     .val(result.transport_id)
//                     .trigger("change");
//                 $(".sale-append-document-number").val(
//                     result.transporter_document_number
//                 );

//                 $("#isCgstSgstIgstCalculated").val(
//                     result.is_cgst_sgst_igst_calculated
//                 );
//                 $("#isGstNa").val(result.is_gst_na);
//                 let purchaseVoucherDate = new Date();
//                 let transportDate = new Date();
//                 let poDate = new Date();
//                 if (result.voucher_date) {
//                     purchaseVoucherDate = new Date(result.voucher_date);
//                 }
//                 if (result.transporter_document_date) {
//                     transportDate = new Date(result.transporter_document_date);
//                     $(".sale-append-transport-date").val(moment(transportDate).format("DD-MM-YYYY"));
//                 } else {
//                     $(".sale-append-transport-date").val('');
//                 }
//                 if (result.po_date) {
//                     poDate = new Date(result.po_date);
//                 }
//                 $(".purchase-append-date").val(moment(purchaseVoucherDate).format("DD-MM-YYYY"));

//                 //for addresses
//                 let shippingAddresses = data.data.shippingAddress;
//                 let billingAddresses = data.data.billingAddress;
//                 let gstin = result.gstin;
//                 let shippingGstin = result.shipping_gstin;
//                 let shippingPartyName = result.shipping_name;

//                 $(".shipping-address-name").val(shippingPartyName);
//                 $(".shipping-address-gstin").val(shippingGstin);
//                 appendAddresses(shippingAddresses, billingAddresses, gstin);

//                 setTimeout(function () {
//                     $(".sale-append-shipping-charge").val(
//                         result.shipping_freight
//                     );
//                     $(".shipping-freight-with-gst").text(
//                         result.shipping_freight_with_gst
//                     );
//                     $(".hidden-shipping-freight-with-gst").val(
//                         result.shipping_freight_with_gst
//                     );
//                     $(".sale-append-packing-charge").val(
//                         result.packing_charge
//                     );
//                     $(".packing-charge-with-gst").text(
//                         result.packing_charge_with_gst
//                     );
//                     $(".purchase-append-hidden-packing-charge-with-gst").val(
//                         result.packing_charge_with_gst
//                     );
//                     $(".hidden-shipping-charge-cgst-tax-amount").val(
//                         result.shipping_freight_sgst_amount ?? 0
//                     );
//                     $(".hidden-shipping-charge-sgst-tax-amount").val(
//                         result.shipping_freight_cgst_amount ?? 0
//                     );
//                     $(".hidden-shipping-charge-igst-tax-amount").val(
//                         result.shipping_freight_igst_amount ?? 0
//                     );
//                     $(".hidden-packing-charge-cgst-tax-amount").val(
//                         result.packing_charge_sgst_amount ?? 0
//                     );
//                     $(".hidden-packing-charge-sgst-tax-amount").val(
//                         result.packing_charge_cgst_amount ?? 0
//                     );
//                     $(".hidden-packing-charge-igst-tax-amount").val(
//                         result.packing_charge_igst_amount ?? 0
//                     );
//                     $(".change-cess-rate").val(result.cess);
//                     $(".sale-append-cgst-value").val(result.cgst);
//                     $(".sale-append-sgst-value").val(result.sgst);
//                     $(".sale-append-igst-value").val(result.igst);
//                     $(".sale-append-cgst-text").text(result.cgst);
//                     $(".sale-append-sgst-text").text(result.sgst);
//                     $(".sale-append-igst-text").text(result.igst);
//                     $(".sale-append-total-text").text(result.total);
//                     $(".sale-append-total-value").val(result.total);
//                     $(".sale-append-round-amount").val(
//                         result.round_off_amount.toFixed(fixDigit)
//                     );
//                     $(".sale-append-grand-total-value").val(
//                         result.grand_total
//                     );
//                     $(".sale-append-grand-total-text").text(
//                         result.grand_total
//                     );
//                     $(".sale-append-narration").text(
//                         result.narration ?? null
//                     );
//                 }, 2500);
//                 $(".sale-append-classification-nature-type").val(
//                     data.data.classificationNatureType ?? null
//                 );
//                 $(".sale-append-classification-is-rcm-applicable").val(
//                     data.data.isRcmApplicable ?? null
//                 );

//                 $(".sale-items-append")
//                     .empty()
//                     .append(data.data.html);
//                 initSelect2();

//                 generatePurchaseUniqueIdForAppendScreen();
//             },
//             complete: function () {
//                 stopLoader();
//                 screenUnLock();
//             },
//         });
//     } else {
//         location.href = window.location.href;
//     }
// });


/* Comment for getting some rounding issue (customToFixed(1478.796969, 2) = 1479.10) */
// window.customToFixed = function (number, precision) {
//     // Coerce inputs to numbers
//     number = Number(number);
//     precision = Number(precision);

//     // Handle edge cases
//     if (isNaN(number) || isNaN(precision) || precision < 0) {
//         return "Invalid input";
//     }

//     // Split the number into integer and decimal parts
//     const [integerPart, decimalPart] = number.toString().split('.');
//     if (decimalPart === undefined) {
//         // If precision is 0, return integer part
//         return precision === 0 ? integerPart : number.toFixed(precision);
//     }

//     // If the desired precision is greater than the actual decimal length, use toFixed
//     if (precision >= decimalPart.length) {
//         return number.toFixed(precision);
//     }

//     // Extract the relevant decimal digits and the next digit for rounding
//     const relevantDecimals = decimalPart.substring(0, precision);
//     const nextDigit = parseInt(decimalPart[precision], 10);

//     // Perform rounding based on the next digit
//     let roundedDecimals = relevantDecimals;
//     if (nextDigit >= 5) {
//         // Increment the last digit of the relevant decimals
//         const lastDigit = parseInt(roundedDecimals[roundedDecimals.length - 1], 10);
//         roundedDecimals = roundedDecimals.substring(0, roundedDecimals.length - 1) + (lastDigit + 1);

//         // Handle carry-over if necessary
//         if (roundedDecimals.length > precision) {
//             roundedDecimals = roundedDecimals.substring(1);
//             let carryOver = 1;
//             let newIntegerPart = parseInt(integerPart, 10);
//             newIntegerPart += carryOver;
//             return newIntegerPart.toString() + "." + roundedDecimals;
//         }
//     }

//     return integerPart + "." + roundedDecimals;
// }

/* Last change 2025-01-11 for getting minus rounding not working properly ex:- -165.545 = -165.54 */
window.customToFixed = function (number, precision) {
    // Coerce inputs to numbers
    number = Number(number);
    precision = Number(precision);

    // Handle edge cases
    if (isNaN(number) || isNaN(precision) || precision < 0) {
        return '0';
    }

    // Scale the number
    const multiplier = Math.pow(10, precision);

    // Perform rounding with consistent behavior for both positive and negative numbers
    const roundedNumber = (Math.sign(number) * Math.round((Math.abs(number) * multiplier) + 1e-10)) / multiplier;

    // Convert the rounded number to a string
    let [integerPart, decimalPart] = roundedNumber.toString().split(".");

    // If no decimal part exists, handle precision
    decimalPart = decimalPart || "";

    // Pad decimal part with zeros if necessary
    while (decimalPart.length < precision) {
        decimalPart += "0";
    }

    return precision > 0 ? `${integerPart}.${decimalPart}` : integerPart;
}

listenClick(".add-report-to-favorite-section", function (e) {
    e.preventDefault();
    let reportId = $(this).attr("data-report-id");
    let reportSection = $(this).attr("data-report-section");
    $.ajax({
        type: "POST",
        url: route("company.add-favorite-report"),
        data: { reportId: reportId, reportSection: reportSection },
        success: function (response) {
            displaySuccessMessage(response.message);
            window.location.reload();
        },
        error: function (response) {
            displayErrorMessage(response.responseJSON.message);
        },
    });
});

window.disableInvoiceDate = function (datePickerDate, lockDate) {
    if (lockDate) {
        let lockDateObj = moment(lockDate, "YYYY-MM-DD").toDate();
        return datePickerDate <= lockDateObj;
    }
    return false;
};

listenClick(".open-lock-transaction-alert", function (e) {
    e.preventDefault();
    let lockDate = $(this).attr("data-lock-date");
    if (lockDate && lockDate !== '') {
        openLockTransactionAlert(lockDate);
    }
    return false;
});

window.openLockTransactionAlert = function (lockDate) {
    lockDate = moment(lockDate).format("DD/MM/YYYY");
    Swal.fire({
        title: "Transaction Locked",
        text: "Transactions have been locked till " + lockDate + ".",
        icon: "warning",
        confirmButtonText: "Okay",
        confirmButtonColor: "#4F158C",
        customClass: {
            confirmButton: "swal-custom-confirm",
        },
    });
};

function handleBeforeUnload(e) {
    e.preventDefault();
    e.originalEvent.returnValue = "";
}

window.openTabCloseWarning = function () {
    window.addEventListener('beforeunload', handleBeforeUnload);
};

window.removeTabCloseWarning = function () {
    $(window).unbind('beforeunload');
};


window.setupEditAndSave = function (formSelector, backButtonSelector, saveCallback) {
    const $form = $(formSelector);
    const initialData = $form.serialize();
    let isFormChanged = false;
    let isCustomPopupShown = false;

    $form.find(':input').on('input', function () {
        const currentData = $form.serialize();
        isFormChanged = initialData !== currentData;
    });

    $(backButtonSelector).on('click', function (event) {
        isCustomPopupShown = true;
        event.preventDefault();
        if (isFormChanged) {
            Swal.fire({
                title: "Leave site? ",
                text: 'Changes that you made may not be saved. ',
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#D9214E",
                cancelButtonText: "No, Cancel",
                confirmButtonText: "Yes, Leave",
            }).then((result) => {
                if (result.isConfirmed) {
                    if ($(backButtonSelector).attr('href') !== '') {
                        window.location.href = $(backButtonSelector).attr('href');
                    } else {
                        window.history.back();
                    }
                    return false;
                } else {
                    isCustomPopupShown = false;
                }
            });
        } else {
            if ($(backButtonSelector).attr('href') !== '') {
                window.location.href = $(backButtonSelector).attr('href');
            } else {
                window.history.back();
            }
        }
    });
    $(window).on('beforeunload', function (event) {
        if (isFormChanged && !isCustomPopupShown) {
            const message = 'You have unsaved changes. Are you sure you want to leave?';
            event.returnValue = message;
            return message;
        }
    })
};
document.addEventListener("DOMContentLoaded", function () {
    const aside = document.querySelector(".aside");
    const header = document.querySelector(".header");

    if (aside && header) {
        const observer = new MutationObserver(() => {
            if (aside.classList.contains("driver-active-element")) {
                header.classList.add("driver-active-element");
            } else {
                header.classList.remove("driver-active-element");
            }
        });

        observer.observe(aside, { attributes: true, attributeFilter: ["class"] });
    }
});
document.addEventListener("DOMContentLoaded", function () {
    const asideLogo = document.querySelector(".aside-logo");
    const header = document.querySelector(".header");

    if (!asideLogo || !header) return;

    const observer = new MutationObserver(() => {
        if (asideLogo.classList.contains("driver-active-element")) {
            header.classList.add("header-after");
        } else {
            header.classList.remove("header-after");
        }
    });

    observer.observe(asideLogo, { attributes: true, attributeFilter: ["class"] });
});
document.addEventListener("DOMContentLoaded", function () {
    const generalMenu = document.querySelector(".general-menu");
    const aside = document.querySelector(".aside"); // Selecting the <aside> element

    if (!generalMenu || !aside) return;

    const observer = new MutationObserver(() => {
        if (generalMenu.classList.contains("driver-active-element")) {
            aside.classList.add("general-menu-after");
        } else {
            aside.classList.remove("general-menu-after");
        }
    });

    observer.observe(generalMenu, { attributes: true, attributeFilter: ["class"] });
});

document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll('.payment-key').forEach(el => el.classList.remove('driver-active-element'));
    document.querySelectorAll('.step-4-mb-0').forEach(el => el.classList.remove('step-4-mb-above-content'));
});
document.addEventListener("DOMContentLoaded", function () {
    const phoneInput = document.getElementById("fpPhone");
    const phoneLabel = document.querySelector(".phone-no-label");

    if (phoneInput && phoneInput.hasAttribute("readonly")) {
        if (phoneLabel) {
            phoneLabel.classList.add("phone-no-label-read-only");
        }
    }
    const LoginphoneInput = document.getElementById("loginPhone");
    const LoginphoneLabel = document.querySelector(".phone-no-label");

    if (LoginphoneInput && LoginphoneInput.hasAttribute("readonly")) {
        if (LoginphoneLabel) {
            LoginphoneLabel.classList.add("phone-no-label-read-only");
        }
    }
});
document.addEventListener("DOMContentLoaded", function () {
    const phoneInput = document.getElementById("loginPhone");
    const phoneLabel = document.querySelector(".phone-no-label");

    function updateLabelClass() {
        if (phoneInput.value.trim() !== "") {
            phoneLabel.classList.add("phone-no-label-value");
        } else {
            phoneLabel.classList.remove("phone-no-label-value");
        }
    }

    if (phoneInput && phoneLabel) {
        updateLabelClass(); // check initial value
        phoneInput.addEventListener("input", updateLabelClass); // update on change
    }
});
document.addEventListener("DOMContentLoaded", function () {
    const phoneInput2 = document.getElementById("fpPhone");
    const phoneLabel2 = document.querySelector(".phone-no-label");

    function updateLabelClass() {
        if (phoneInput2.value.trim() !== "") {
            phoneLabel2.classList.add("phone-no-label-value");
        } else {
            phoneLabel2.classList.remove("phone-no-label-value");
        }
    }

    if (phoneInput2 && phoneLabel2) {
        updateLabelClass(); // check initial value
        phoneInput2.addEventListener("input", updateLabelClass); // update on change
    }
});
document.addEventListener('DOMContentLoaded', function () {
    const containerPhone = document.querySelector('.phone-form-input');
    if (!containerPhone) return;
    const input = containerPhone.querySelector('input[type="tel"]');
    if (!input) return;
    const setTabindex = () => {
      const flag = containerPhone.querySelector('.iti__selected-flag');
      if (flag && flag.getAttribute('tabindex') !== '-1') {
        flag.setAttribute('tabindex', '-1');
      }
    };
    setTimeout(setTabindex, 200);
    input.addEventListener('countrychange', setTabindex);
    const observer = new MutationObserver(setTabindex);
    observer.observe(containerPhone, { childList: true, subtree: true });
  });
