<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>docs</title>
</head>
<body>
<table>
    <thead>
    <tr>
        <td style="background-color: #0070c0;color: white;font-weight: bold;width: 600%">
            Summary of documents issued during the tax period (13)
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <tr>
        <td style="background-color: #0070c0;border: 1px solid black;color: white;width: 600%"></td>
        <td style="background-color: #0070c0;border: 1px solid black;color: white"></td>
        <td style="background-color: #0070c0;border: 1px solid black;color: white"></td>
        <td style="background-color: #0070c0;border: 1px solid black;color: white;text-align: end">Total Number</td>
        <td style="background-color: #0070c0;border: 1px solid black;color: white;text-align: end">Total Cancelled</td>
    </tr>
    @php
        $count = 0;
        $missingCount = 0;
        foreach ($data['invoice_for_outward_supply'] as $key => $invoiceNo) {
            $count += count($invoiceNo);
            $missingCount += getMissingInvoiceSeries($invoiceNo);
        }

        foreach ($data['sale_credit_note'] as $invoiceNo) {
            $count += count($invoiceNo);
            $missingCount += getMissingInvoiceSeries($invoiceNo);
        }
        foreach ($data['income_debit_note'] as $invoiceNo) {
            $count += count($invoiceNo);
            $missingCount += getMissingInvoiceSeries($invoiceNo);
        }
        foreach ($data['income_credit_note'] as $invoiceNo) {
            $count += count($invoiceNo);
            $missingCount += getMissingInvoiceSeries($invoiceNo);
        }
    @endphp
    <tr>
        <td style="border: 1px solid black;text-align: center;width: 600%"></td>
        <td style="border: 1px solid black"></td>
        <td style="border: 1px solid black"></td>
        <td style="border: 1px solid black;text-align: end">{{ $count }}</td>
        <td style="border: 1px solid black;text-align: end">{{ $missingCount }}</td>
    </tr>
    <tr>
        <th style="width: 600%;height: 300%;background-color: #f7caac;">Nature of Document</th>
        <th style="width: 250%;height: 300%;background-color: #f7caac;">Sr. No. From</th>
        <th style="width: 250%;height: 300%;background-color: #f7caac;">Sr. No. To</th>
        <th style="width: 250%;height: 300%;background-color: #f7caac;text-align: end">Total Number</th>
        <th style="width: 250%;height: 300%;background-color: #f7caac;text-align: end">Cancelled</th>
    </tr>
    </thead>
    <tbody>
    @foreach($data['invoice_for_outward_supply'] as $key => $invoiceNo)
        <tr>
            <td>Invoices for outward supply</td>
            <td> {{ current($invoiceNo) }}</td>
            <td>{{ end($invoiceNo) }}</td>
            <td>{{ count($invoiceNo) }}</td>
            <td>{{ getMissingInvoiceSeries($invoiceNo) }}</td>
        </tr>
    @endforeach
    @foreach($data['sale_credit_note'] as $key => $invoiceNo)
        <tr>
            <td>Credit Note</td>
            <td> {{ current($invoiceNo) }}</td>
            <td>{{ end($invoiceNo) }}</td>
            <td>{{ count($invoiceNo) }}</td>
            <td>{{ getMissingInvoiceSeries($invoiceNo) }}</td>
        </tr>
    @endforeach
    @foreach($data['income_debit_note'] as $key => $invoiceNo)
        <tr>
            <td>Debit Note</td>
            <td> {{ current($invoiceNo) }}</td>
            <td>{{ end($invoiceNo) }}</td>
            <td>{{ count($invoiceNo) }}</td>
            <td>{{ getMissingInvoiceSeries($invoiceNo) }}</td>
        </tr>
    @endforeach
    @foreach($data['income_credit_note'] as $key => $invoiceNo)
        <tr>
            <td>Credit Note</td>
            <td> {{ current($invoiceNo) }}</td>
            <td>{{ end($invoiceNo) }}</td>
            <td>{{ count($invoiceNo) }}</td>
            <td>{{ getMissingInvoiceSeries($invoiceNo) }}</td>
        </tr>
    @endforeach
    </tbody>
</table>
</body>
</html>
