<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{{ ucfirst($reportType) }} Report</title>
</head>

<body>
    <table>
        <thead>
            <tr>
                <th style="width: 200%"><b>{{ $company->trade_name }}</b></th>
            </tr>
            <tr>
                <th style="width: 500px">{{ $companyAddress->address_1 ?? null }}</th>
            </tr>
            <tr>
                <th style="width: 500px">{{ $companyAddress->address_2 ?? null }}</th>
            </tr>
            <tr>
                <th style="width: 200%">{{ getCityName($companyAddress->city_id ?? null) }}</th>
            </tr>
            <tr>
                <th style="width: 500px">{{ getStateName($companyAddress->state_id ?? null).'
                    -'.($companyAddress->pin_code ?? null) }}</th>
            </tr>
            @if ($company->is_gst_applicable)
                <tr>
                    <th style="width: 500px">GSTIN : {{ $company->companyTax->gstin ?? null }}</th>
                </tr>
            @endif
            <tr>
                <th style="width: 500px">PAN : {{ $company->companyTax->pan_number ?? null }}</th>
            </tr>
            <tr>
                <th style="width: 500px">Email: {{ (isset(getCompanySettings()['alternate_email']) ? getCompanySettings()['alternate_email'] : $company->user->email) ?? null }}</th>
            </tr>
            <tr>
                <th style="width: 500px">Contact : {{ '  ' . (isset(getCompanySettings()['alternate_phone']) ? getCompanySettings()['alternate_phone'] : $company->phone) ?? null }}</th>
            </tr>
            <tr></tr>

            <tr></tr>
        </thead>
        @foreach($ledgerReportDatas as $ledgerReportData)
        <tr></tr>
        <tr>
            <td><b>Ledger Name</b></td>
            <td><b>{{ $ledgerReportData['ledgerName'] ?? ''}}</b></td>
            <td></td>
            <td>Duration:</td>
            <td><b>{{$durationDate}}</b></td>
        </tr>
        <tr></tr>
        @if (isset($ledgerReportData['billing_address']))
        <tr>
            <td><b>Address</b></td>
            <td> {{ $ledgerReportData['billing_address'] }} </td>
        </tr>
        <tr></tr>
        @endif
        <thead>
            <tr>
                <th style="width: 200%"><b>Date</b></th>
                <th style="width: 250%"><b>Ledger Name</b></th>
                <th style="width: 300%"><b>Transaction Type</b></th>
                {{-- @if ($checkNarration == "true")
                <th style="width: 300%"><b>Narration</b></th>
                @endif --}}
                <th style="width: 300%"><b>Vch. No</b></th>
                {{-- @if($reportType != 'cashBank')
                <th style="width: 200%"><b>Inv. No</b></th>
                @endif --}}
                <th style="width: 200%; text-align: right;"><b>Debit Amt.</b></th>
                <th style="width: 200%; text-align: right;"><b>Credit Amt.</b></th>
                <th style="width: 200%; text-align: right;"><b>Balance</b></th>
            </tr>
        </thead>
        <tbody>
            @php
            $ledgerReportBalanceType = $ledgerReportData['closing_balance']['balanceType'] ?? null;
            $ledgerReportBalance = $ledgerReportData['closing_balance']['balance'] ?? 0;
            $ledgerDetails = $ledgerReportData['data'] ?? [];

            $debitAmountTotal = 0;
            $creditAmountTotal = 0;
            @endphp
            @if(count($ledgerReportData['closing_balance'] ?? []))
            <tr>
                <td colspan="6">
                    <b>Opening Balance</b>
                </td>
                @php
                $debitAndCreditType = \App\Models\Ledger::OPENING_BALANCE_DR_CR[$ledgerReportBalanceType];
                $balance = $ledgerReportBalance.' '.$debitAndCreditType
                @endphp
                <td style="text-align: right;">{{$balance}}</td>
            </tr>
            @endif
            @foreach($ledgerReportData['data'] as $item)
            <tr>
                <td>{{ $item['date'] }}</td>
                @if($config == 1 )
                <td style="{{ $checkNarration == 'true' && isset($item['narration']) && !empty($item['narration']) ? 'height: 400%;width: 500%' : ''}}">
                    {{ $item['ledger_name'] }}
                    @if($checkNarration == 'true')
                    @php
                    $narration = $item['narration'] ?? null;
                    @endphp
                    @if(!empty($narration))
                    <br>
                    <span>Narration :{!! nl2br(str_replace("&", "",$narration)) !!}</span>
                    @endif
                    @endif
                </td>
                @else
                <td style="width: 500%">
                    @if(isset($item['tcs_amount']))
                    <div>
                        @foreach($item['ledger_list'] as $ledgerItem)
                        <span>{{ $ledgerItem['name'] }} (Taxable Val) :{{ $ledgerItem['amount'] }}</span>
                        <br>
                        @endforeach
                        <span>TCS Amount :{{ $item['tcs_amount'] }}</span><br>
                        @if(isCompanyGstApplicable())
                        <span>CGST :{{ $item['cgst'] }}</span>
                        <br>
                        <span>IGST:{{ $item['igst'] }}</span>
                        <br>
                        <span>SGST:{{ $item['sgst'] }}</span>
                        <br>
                        @foreach ($item['additional_charges_addless'] as $additionalChargeAndAddless)
                        <span>{{ $additionalChargeAndAddless['name'] }}:{{ $additionalChargeAndAddless['amount'] }}</span>
                        <br>
                    @endforeach
                        <span>Cess:{{ $item['cess'] }}</span>
                        <br>
                        @endif
                        <span>Round off:{{ $item['rounding_amount'] }}</span>

                        @if($checkNarration == 'true')
                        @php
                        $narration = $item['narration'] ?? null;
                        @endphp
                        @if(!empty($narration))
                        <br>
                        <span>Narration :{!! nl2br(str_replace("&", "",$narration)) !!}</span>
                        @endif
                        @endif
                    </div>
                    @else
                    {{ $item['ledger_name'] }}

                    @if($checkNarration == 'true')
                    @php
                    $narration = $item['narration'] ?? null;
                    @endphp
                    @if(!empty($narration))
                    <br>
                    <span>Narration :{!! nl2br(str_replace("&", "",$narration)) !!}</span>
                    @endif
                    @endif
                    @endif
                    @php
                        $settleInvoiceNo = $item['settle_invoice_no'] ?? [];
                    @endphp
                    @if(!empty($settleInvoiceNo) && is_array($settleInvoiceNo))
                        <div>
                            @foreach($settleInvoiceNo as $type => $invoices)
                                <br>
                                <div style="font-size: 10px">{{ $type }}: {{ implode(', ', $invoices) }}</div>
                            @endforeach
                        </div>
                    @endif
                    @if (isset($item['transaction_item_list']) && !empty($item['transaction_item_list']))
                        <div>
                            <br>
                            <div style="font-size: 10px">Transaction Items:<br>{!! $item['transaction_item_list'] !!}</div>
                        </div>
                    @endif
                </td>
                @endif
                <td>{{ $item['transaction_type'] }}</td>

                <td style="text-align: left">{{ $item['voucher_no'] }}</td>
                {{-- @if($reportType != 'cashBank')
                <td style="text-align: left">{{ $item['invoice_no'] }}</td>
                @endif --}}
                <td style="text-align: right;">{{ $item['debit_amount'] }}</td>
                <td style="text-align: right;">{{ $item['credit_amount'] }}</td>
                @php
                $debitAmountTotal += $item['debit_amount'];
                $creditAmountTotal += $item['credit_amount'];

                if ($ledgerReportBalanceType == 'undefined' ||
                $ledgerReportBalanceType == null) {
                if ($item['credit_amount'] > 0) {
                $ledgerReportBalanceType = 2;
                } else {
                $ledgerReportBalanceType = 1;
                }
                }
                if ($item['credit_amount'] != 0) {
                if ($ledgerReportBalanceType == 2) {
                $ledgerReportBalance += $item['credit_amount'];
                } else {
                $ledgerReportBalance -= $item['credit_amount'];
                }
                } else {
                if ($ledgerReportBalanceType == 1) {
                $ledgerReportBalance += $item['debit_amount'];
                } else {
                $ledgerReportBalance -= $item['debit_amount'];
                }
                }
                if ($ledgerReportBalance < 0) { if ($ledgerReportBalanceType==1) { $ledgerReportBalanceType=2; } else if
                    ($ledgerReportBalanceType==2) { $ledgerReportBalanceType=1; } }
                    $debitAndCreditType=\App\Models\Ledger::OPENING_BALANCE_DR_CR[$ledgerReportBalanceType];
                    $ledgerReportBalance=abs($ledgerReportBalance); $balance=$ledgerReportBalance.' '.$debitAndCreditType
                @endphp
                <td style="text-align: right;">{{ $balance }}</td>
            </tr>
        @endforeach
        @if(isset($balance) && !empty($balance))
            <tr>
                <td colspan="4">
                    <b>Closing Balance</b></td>
                <td style="text-align: right;"><b>{{$debitAmountTotal}}</b></td>
                <td style="text-align: right;"><b>{{$creditAmountTotal}}</b></td>
                <td style="text-align: right;"><b>{{ $balance }}</b></td>
            </tr>
        @endif
        @endforeach
        </tbody>
    </table>
</body>
</html>
