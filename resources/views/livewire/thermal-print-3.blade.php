<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Thermal Print 3in</title>
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/font.css') }}">
    @if ($iS3InchPdf)
        <style>
            .fs-13 {
                font-size: 13px;
            }
            .fs-12 {
                font-size: 11px;
            }
            .fs-15{
                font-size: 15px
            }
            body {
                padding-right: 2.75rem !important;
            }
            .w-100{
                width: auto;
            }
        </style>
    @else
        <style>
            .fs-13 {
                font-size: 9px;
            }
            .fs-12 {
                font-size: 9px;
            }
            .fs-15{
                font-size: 12px
            }
            .w-100{
                width: 100%;
            }
        </style>
    @endif
    <style>
     * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-size: 8px;
            font-weight: normal;
            box-sizing: border-box;
            font-family: 'trebuc';
        }

        @page {
            margin: 0px;
            padding: 0px;
        }

        table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }

        td {
            vertical-align: top;
        }

        h1 {
            font-size: 16px;
        }

        .text-primary {
            color: #4f158c;
        }

        /* .fs-13 {
            font-size: 13px;
        }

        .fs-12 {
            font-size: 11px;
        } */

        .fw-6 {
            /* font-weight: 600; */
        }

        .whitespace-nowrap {
            white-space: nowrap;
        }

        .border-bottom {
            border-bottom: 1px dashed black;
        }

        .border-top {
            border-top: 1px dashed black;
        }

        .vertical-top {
            vertical-align: top;
        }

        .vertical-middle {
            vertical-align: middle;
        }

        .vertical-bottom {
            vertical-align: bottom;
        }

        .text-center {
            text-align: center !important;
        }

        .text-start {
            text-align: left !important;
        }

        .text-end {
            text-align: right;
        }

        .table-heading {
            padding: 3px 8px;
            text-align: left;
            position: relative;
            /* background-color: #eeeeee !important; */
        }

        .d-none {
            display: none;
        }

        .mb-5 {
            margin-bottom: 1.25rem !important;
        }

        .mb-8 {
            margin-bottom: 2rem !important;
        }

        .mt-8 {
            margin-top: 2rem !important;
        }

        .px-2 {
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }

        .mt-8 {
            margin-top: 2rem !important;
        }

        .ps-2 {
            padding-left: 0.5rem !important;
        }

        .pe-2 {
            padding-right: 0.5rem !important;
        }

        .pe-4 {
            padding-right: 2rem !important;
        }

        .ms-12{
            margin-left: 3rem !important;
        }

        .me-2 {
            margin-right: 0.5rem !important;
        }

        .mb-2 {
            margin-bottom: 0.5rem !important;
        }

        .mt-2 {
            margin-top: 0.50rem !important;
        }

        .mt-5 {
            margin-top: 1.25rem !important;
        }

        .hsn-table td {
            padding: 3px;
        }

        .padding-4805 {
            padding: 4px 8px 0px 5px;
        }

        .ps-0 {
            padding-left: 0px !important;
        }

        .item-table-head-padding {
            padding: 6px 5px;
        }

        .item-hsn-padding {
            padding: 0px 0px 5px 5px;
        }

        .text-wrap{
            text-wrap: wrap !important;
        }

        .word-wrap{
            word-wrap: break-word !important;
        }
    </style>
</head>
<body>
    <div id="thermalPrintArea">
        <div class=" d-flex justify-content-center ">
            <div class="main-table">
                <div class="text-center" style="padding: 0px 2px 2px 2px">
                    <h6 class="fw-6 text-primary mb-8 mt-8" style="font-size: 15px">
                        TAX INVOICE
                    </h6>
                </div>
                <table cellpadding="0">
                    @if($invoiceSetting['thermal_logo'] ?? true)
                    <tr class="text-center">
                        <td class="w-100">
                            @if (isset($currentCompany['company_logo']) &&
                            $currentCompany['company_logo'] !=
                            asset('assets/images/company-logo.png'))
                            <img src="{{ asset($currentCompany['company_logo']) }}" alt="Logo" style="object-fit: contain;"
                                width="100">
                            @endif
                        </td>
                    </tr>
                    @endif
                    <tr>
                        <td class="vertical-middle text-center fs-12 pe-4">
                            <h1 class="mt-2">{{ strtoupper($currentCompany['trade_name']) }}</h1>
                            <p class="fs-13">
                                {{ $currentCompany['billingAddress']['address_1'] ?? null }},
                                {{ $currentCompany['billingAddress']['address_2'] ?? null }},
                                {{ getCityName($currentCompany['billingAddress']['city_id'] ?? null) }},
                                {{ getStateName($currentCompany['billingAddress']['state_id'] ?? null) }},
                                {{ getCountryName($currentCompany['billingAddress']['country_id'] ?? null) }},
                                {{ $currentCompany['billingAddress']['pin_code'] ?? null }}
                            </p>
                            <p class="mb-0 fs-13">
                                @if ($invoiceSetting['thermal_mobile_number'] ?? true)
                                {{ $changeLabel['thermal_tel'] ?? 'Tel' }} :
                                {{
                                    (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                                    (isset($invoiceSetting['alternate_phone']) ? $invoiceSetting['alternate_phone'] : ('+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone)) .
                                    (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', +' . $invoiceSetting['region_code_2'] . ' ' : '') .
                                    (isset($invoiceSetting['alternate_phone_2']) ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2'] : '')
                                }}
                                @endif
                                @if (($invoiceSetting['thermal_mobile_number'] ?? true) && ($invoiceSetting['thermal_email'] ?? true))
                                |
                                @endif
                                @if ($invoiceSetting['thermal_email'] ?? true)
                                    {{ $invoiceSetting['alternate_email'] ?? ($currentCompany['user']['email'] ?? null) }}
                                @endif
                            </p>
                            @if ($isCompanyGstApplicable)
                            <p class="fs-13"> {{ $changeLabel['thermal_gstin'] ?? 'GSTIN' }}:
                                {{ ' ' . $currentCompany['companyTax']['gstin'] ?? null }}</p>
                            @endif
                            @foreach (printCustomPDFLabelsForSale() as $key => $customLabel)
                                <p class="fs-13">{{ $key ?? null }}:{{ $customLabel ?? null }}</p>
                            @endforeach
                        </td>
                    </tr>
                </table>
                @if($iS3InchPdf)
                <table class="mt-8">
                    <tr>
                        <td class="w-100">
                            <span class="fs-13 ps-2">{{ $changeLabel['thermal_invoice_no'] ?? 'Invoice no' }}:</span>
                            <span class="fs-12 text-end" style="padding: 4px 8px 0 0px">{{
                                $transaction['full_invoice_number'] }}</span>
                        </td>
                        <td class="text-end pe-4">
                            <span class="fs-12">
                                {{ $changeLabel['thermal_date'] ?? 'Date' }}:</span>
                            <span class="fs-12 text-end">{{ isset($transaction['date']) ?
                                \Carbon\Carbon::parse($transaction['date'])->format('d-m-Y') : null }}</span>
                        </td>
                    </tr>
                </table>
                @else
                <table class="mt-8">
                    <tr>
                        <td class="w-100">
                            <span class="fs-13 ps-2">{{ $changeLabel['thermal_invoice_no'] ?? 'Invoice no' }}:</span>
                            <span class="fs-12 text-end" style="padding: 4px 8px 0 0px">{{
                                $transaction['full_invoice_number'] }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="">
                            <span class="fs-12 ps-2">
                                {{ $changeLabel['thermal_date'] ?? 'Date' }}:</span>
                            <span class="fs-12 text-end">{{ isset($transaction['date']) ?
                                \Carbon\Carbon::parse($transaction['date'])->format('d-m-Y') : null }}</span>
                        </td>
                    </tr>
                </table>
                @endif
                <div class="row ps-2 pe-2 mb-2">
                    <div class="col vertical-top pb-3">
                        <div class="mb-0">
                            <span class="fs-12">{{ $changeLabel['thermal_customer'] ?? 'Customer' }}:</span>
                            <span class="fs-12" style="padding: 4px 8px 0 1px">{{
                                $customer ?? '' }}</span>
                        </div>
                        <div class="ms-2">
                            <span class="fs-12">
                                {{ $changeLabel['thermal_address'] ?? 'Address' }}:</span>
                            @if (isset($billingAddress))
                                <span class="fs-12" style="padding: 4px 8px 0 1px">
                                    @if ($billingAddress->address_1 != null)
                                        {{ strtoupper($billingAddress->address_1) }}
                                    @endif
                                    @if ($billingAddress->address_2 != null)
                                        {{ strtoupper($billingAddress->address_2) }},<br>
                                    @endif
                                    {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                                    {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                                    {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                                    {{ $billingAddress->pin_code ?? null }}
                                </span>
                            @endif
                        </div>
                        @if($showGst)
                        <div class="ms-2">
                            <p class="fs-12">
                                {{ $changeLabel['thermal_gstin'] ?? 'GSTIN' }}: {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                            </p>
                        </div>
                        @endif
                        @if (!empty($transaction->party_phone_number))
                        <div class="ms-2">
                            <p class="fs-12">
                                Mobile No: +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                            </p>
                        </div>
                        @endif
                        @if(! empty($panNumber) && $showPanNumber)
                        <div class="ms-2">
                            <p class="fs-12">
                                PAN: {{ $panNumber ?? null }}
                            </p>
                        </div>
                    @endif
                    </div>
                </div>

                {{-- Custom Fields Section Start --}}
                @if (count($customFieldValues) > 0)
                    @php
                        $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
                    @endphp
                    <div class="row ps-2 pe-2 mb-2 border-top">
                        <div class="col vertical-top pb-3 mt-2 mb-2">
                            @foreach ($customFields as $customField)
                                <div class="ms-2">
                                    <p class="fs-12">
                                        {{ $customField['label_name'] ?? '' }}: {{ $customField['value'] ?? '' }}
                                    </p>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
                {{-- Custom Fields Section End --}}

                @php
                    $is3Inch = isset($invoiceSetting['thermal_print_size']) && $invoiceSetting['thermal_print_size'] == \App\Models\CompanySetting::INCH_3;
                    $totalQty = 0;
                    $totalAmount = 0;
                @endphp
                @if($itemType == \App\Models\SaleTransaction::ITEM_INVOICE)

                    <table>
                    <tr class="border-top border-bottom">
                        <td class="fs-13 word-wrap item-table-head-padding item-heading" style="white-space: nowrap; word-break:keep-all;{{ $is3Inch ? 'width:150px' : 'width:60px' }}">
                            {{ $changeLabel['thermal_item_name'] ?? 'Item Name' }}
                        </td>
                        <td class="fs-13 word-wrap text-center item-table-head-padding quantity-heading"  style="vertical-align:bottom;">
                            {{ $changeLabel['thermal_qty'] ?? 'Qty' }}
                        </td>
                        <td class="fs-13 word-wrap text-end item-table-head-padding gst-heading"  style="word-break: break-all; vertical-align:bottom;">
                            {{ $changeLabel['thermal_rate'] ?? 'Rate' }}
                        </td>
                        @if(isset($invoiceSetting['thermal_print_size']) && $invoiceSetting['thermal_print_size'] == \App\Models\CompanySetting::INCH_3)
                            @if($transactionItems->sum('total_discount_amount') != 0)
                                <td class="fs-13 word-wrap text-end item-table-head-padding discount-heading"  style="word-break: break-all; vertical-align:bottom;">
                                    {{ $changeLabel['thermal_discount'] ?? 'Dis.' }}
                                </td>
                            @endif
                        @endif
                        <td class="fs-13 word-wrap text-end item-table-head-padding pe-4"  style=" word-break: break-all; vertical-align:bottom; width:100px">
                            {{ $changeLabel['thermal_taxable_value'] ?? 'Amount' }}
                        </td>
                    </tr>
                    @foreach ($transactionItems as $item)
                        <tr style="padding: 0px 3px">
                            <td colspan="{{ $transactionItems->sum('total_discount_amount') != 0 ? '5' : '4'}}" class="fs-12 item-hsn-padding pe-4" style="padding: 0px 5px">
                                @php
                                $gstLabel = $changeLabel['thermal_gst'] ?? 'GST';
                                $hsnLabel = $changeLabel['thermal_hsn_sac'] ?? 'HSN/SAC';
                                $productHSN = isset($item['items']['model']['hsn_sac_code']) ? $hsnLabel.': '.$item['items']['model']['hsn_sac_code'] : null;
                                $itemGSt = isset($item['gst_tax_percentage']) ? $gstLabel.': '.$item['gst_tax_percentage'].'%' : null;
                                $seperator = isset($item['items']['model']['hsn_sac_code'])  && isset($item['gst_tax_percentage']) ? ', ' : null;
                                $gstHsn = (!empty($item['gst_tax_percentage']) ||  !empty($item['items']['model']['hsn_sac_code'])) ? '('.$productHSN.$seperator.$itemGSt.')'  : '';
                               @endphp
                               {{ ($item['items']['item_name'] .' '.$gstHsn) ?? '-'  }}
                            </td>
                        </tr>
                        <tr>
                            <td class="fs-12 fw-6 item-heading" style="padding: 0px 5px">

                            </td>
                            <td class="fs-12 whitespace-nowrap text-center quantity-heading" >
                                {{ $item['quantity'] ?? 0 }}
                            </td>
                            <td class="fs-12 whitespace-nowrap text-end gst-heading">
                                {{ getCurrencyFormat($item['rpu_without_gst']) ?? 0 }}
                            </td>
                            @if(isset($invoiceSetting['thermal_print_size']) && $invoiceSetting['thermal_print_size'] == \App\Models\CompanySetting::INCH_3)
                                @if($transactionItems->sum('total_discount_amount') != 0)
                                    @if ($item->discount_type == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                    <td class="fs-12 whitespace-nowrap text-end discount-heading" style="padding: 0px 10px;">
                                        {{ $item['discount_value'] ?? 0 }}
                                    </td>
                                    @else
                                    <td class="fs-12 whitespace-nowrap text-end discount-heading" style="padding: 0px 10px;">
                                        {{ $item['discount_value'].'%' ?? 0 }}
                                    </td>
                                    @endif
                                @endif
                            @endif
                            <td class="fs-12 whitespace-nowrap text-end pe-4">
                                {{ getCurrencyFormat($item['total'])  ?? 0 }}
                            </td>
                        </tr>
                    @endforeach
                    <tr class="border-bottom border-top">
                        <td class="fs-12 fw-6" style="padding: 4px 5px">
                            Total
                        </td>
                        <td class="fs-12 whitespace-nowrap text-center quantity-heading fw-6" style="padding: 4px 5px">{{ $transactionItems->sum('quantity') }}
                        </td>
                        @if(isset($invoiceSetting['thermal_print_size']) && $invoiceSetting['thermal_print_size'] == \App\Models\CompanySetting::INCH_3)
                            @if($transactionItems->sum('total_discount_amount') != 0)
                                <td class="fs-12 whitespace-nowrap text-start fw-6" style="padding: 4px 5px"></td>
                            @endif
                        @endif
                        <td class="fs-12 whitespace-nowrap text-start fw-6" style="padding: 4px 5px"></td>
                        </td>
                        <td class="fs-12 whitespace-nowrap text-end fw-6 pe-4" style="padding: 4px 5px">
                            {{ getCurrencyFormat($transaction->gross_value) }}
                        </td>
                    </tr>
                </table>
                @endif
                {{-- Accounting Invoice Section Start--}}
                @php
                    $totalDiscountAmount = 0;
                    $totalTaxableAmount = 0;
                @endphp
                @if($itemType == \App\Models\SaleTransaction::ACCOUNTING_INVOICE)
                <table>
                    <tr class="border-bottom border-top">
                        <td class="fs-13 word-wrap item-table-head-padding" style="min-width: 60px;">
                            {{ $changeLabel['thermal_item_name'] ?? 'Particulars' }}
                        </td>
                        <td class="fs-13 word-wrap item-table-head-padding">
                            {{ $changeLabel['thermal_rate'] ?? 'Rate' }}
                        </td>
                        @if(isset($invoiceSetting['thermal_print_size']) && $invoiceSetting['thermal_print_size'] == \App\Models\CompanySetting::INCH_3)
                        @if($transactionLedgers->sum('total_discount_amount') != 0.0)
                        <td class="fs-13 word-wrap text-start item-table-head-padding">
                            {{ $changeLabel['thermal_discount'] ?? 'Dis.' }}
                        </td>
                        @endif
                        @endif
                        @if ($isCompanyGstApplicable)
                        <td class="fs-13 word-wrap text-end item-table-head-padding pe-4">
                            {{ $changeLabel['thermal_taxable_value'] ?? 'Taxable Value' }}
                        </td>
                        @else
                        <td class="fs-13 word-wrap text-end item-table-head-padding pe-4">
                            Amount
                        </td>
                        @endif
                    </tr>
                    @foreach ($transactionLedgers as $key => $ledger)
                    @php
                        $uniqueId = ++$key;
                        $totalTaxableAmount += $ledger['taxable_value'];
                     @endphp
                    <tr>
                        <td class="fs-12 fw-6 padding-4805">
                            {{ $ledger['ledgers']['name'] ?? '-' }}
                        </td>
                        <td class="fs-12 fw-6 padding-4805">
                            {{ getCurrencyFormat($ledger['rpu_without_gst']) }}
                        </td>
                        @if(isset($invoiceSetting['thermal_print_size']) && $invoiceSetting['thermal_print_size'] == \App\Models\CompanySetting::INCH_3)
                            @if ($transactionLedgers->sum('total_discount_amount') != 0.0)
                                @if ($ledger['discount_type'] == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                    <td class="fs-12 text-start fw-6 padding-4805">
                                        {{ getCurrencyFormat(round($ledger['discount_value'] ?? '0.0', getCompanyFixedDigitNumber())) }}
                                    </td>
                                @else
                                    <td class="fs-12 fw-6 text-start padding-4805">
                                        {{ $ledger['discount_value'] . '(%)' ?? '0.0(%)' }}
                                    </td>
                                @endif
                            @endif
                        @endif
                    <td class="fs-12 fw-6 padding-4805 text-end pe-4">
                        {{ getCurrencyFormat(round($ledger['taxable_value'] ?? '0.0', getCompanyFixedDigitNumber())) }}
                    </td>
                    </tr>
                    <tr>
                        <td class="fs-12 fw-6 item-hsn-padding">{!! !empty($ledger['additional_description']) ? nl2br('(Additional Description : ' . $ledger['additional_description'] . ($isCompanyGstApplicable && $ledger['gst_tax_percentage'] != 0.0 ? ', GST : ' . $ledger['gst_tax_percentage'] . '%)': ')')) : null !!}</td>
                    </tr>
                    @endforeach

                    <tr class="border-bottom border-top">
                        <td class="fs-12 fw-6" style="padding: 4px 8px">
                            Total
                        </td>
                        @if(isset($invoiceSetting['thermal_print_size']) && $invoiceSetting['thermal_print_size'] == \App\Models\CompanySetting::INCH_3)
                            @if($transactionLedgers->sum('total_discount_amount') != 0.0)
                                <td class="fs-12 whitespace-nowrap text-start fw-6" style="padding: 4px 5px">
                                </td>
                            @endif
                        @endif
                        <td class="fs-12 whitespace-nowrap text-start fw-6" style="padding: 4px 5px"></td>
                        </td>

                        <td class="fs-12 whitespace-nowrap text-end fw-6 pe-4" style="padding: 4px 5px">
                            {{ getCurrencyFormat($totalTaxableAmount) ?? 0 }}
                        </td>
                    </tr>
                </table>
                @endif
                {{-- Accounting Invoice Section End--}}
                <table class="border-bottom">
                    @foreach ($additionalCharges as $additionalCharge)
                    <tr>
                        <td class="fs-12 vertical-top text-start fw-6 ps-2">
                            {{ $additionalCharge['ledger_name'] }}:
                        </td>
                        <td class="fs-12 vertical-top text-end fw-6 pe-4">
                            {{ getCurrencyFormat($additionalCharge['amount']) }}
                        </td>
                    </tr>
                    @endforeach
                    <tr class="border-bottom border-top">
                        <td class="fs-12 vertical-top text-start fw-6 ps-2" style="padding: 4px 8px">
                            {{ $isCompanyGstApplicable ?  'Taxable Value' : ' Sub Total' }}:
                        </td>
                        <td class="fs-12 vertical-top text-end fw-6 pe-4" style="padding: 4px 8px">
                            {{ getCurrencyFormat($transaction->taxable_value ?? '0.00') }}
                        </td>
                    </tr>
                    @if($isCompanyGstApplicable)
                    @if($transaction['cgst'] != 0)
                    <tr>
                        <td class="fs-12 vertical-top text-start fw-6 ps-2" >
                            {{ $changeLabel['thermal_cgst'] ?? 'CGST' }}:
                        </td>
                        <td class="fs-12 vertical-top text-end fw-6 pe-4">
                            {{ getCurrencyFormat($transaction['cgst'] ?? '0.00') }}
                        </td>
                    </tr>
                    @endif
                    @if($transaction['sgst'] != 0)
                    <tr>
                        <td class="fs-12 vertical-top text-start fw-6 ps-2">
                            {{ $changeLabel['thermal_sgst'] ?? 'SGST' }}:
                        </td>
                        <td class="fs-12 vertical-top text-end fw-6 pe-4">
                            {{ getCurrencyFormat($transaction['sgst'] ?? '0.00') }}
                        </td>
                    </tr>
                    @endif
                    @if($transaction['igst'] != 0)
                        <tr>
                            <td class="fs-12 vertical-top text-start fw-6 ps-2">
                                {{ $changeLabel['thermal_igst'] ?? 'IGST' }}:
                            </td>
                            <td class="fs-12 vertical-top text-end fw-6 pe-4">
                                {{ getCurrencyFormat($transaction['igst'] ?? '0.00') }}
                            </td>
                        </tr>
                    @endif
                    @endif
                    @if($transaction['tcs_amount'] != 0)
                        <tr>
                            <td class="fs-12 vertical-top text-start fw-6 ps-2">
                                {{ $changeLabel['thermal_tcs'] ?? 'TCS' }}:
                            </td>
                            <td class="fs-12 vertical-top text-end fw-6 pe-4">
                                {{ getCurrencyFormat($transaction['tcs_amount'] ?? '0.00') }}
                            </td>
                        </tr>
                    @endif
                    @if($isCompanyGstApplicable)
                        @if($transaction['cess'] != 0)
                            <tr>
                                <td class="fs-12 vertical-top text-start fw-6 ps-2">
                                    {{ $changeLabel['thermal_cess'] ?? 'Cess' }}:
                                </td>
                                <td class="fs-12 vertical-top text-end fw-6 pe-4">
                                    {{ getCurrencyFormat($transaction['cess'] ?? '0.00') }}
                                </td>
                            </tr>
                        @endif
                    @endif
                    @if($transaction['rounding_amount'] != 0)
                    <tr>
                        <td class="fs-12 vertical-top text-start fw-6 ps-2">
                            {{ $changeLabel['thermal_round_off'] ?? 'Round off' }}:
                        </td>
                        <td class="fs-12 vertical-top text-end fw-6 pe-4">
                            {{ getCurrencyFormat($transaction['rounding_amount'] ?? '0.00') }}
                        </td>
                    </tr>
                    @endif
                    @foreach ($addLess as $value)
                    <tr>
                        <td class="fs-12 vertical-top text-start fw-6 ps-2">
                            {{ $value['ledger_name'] }}:
                        </td>
                        <td class="fs-12 vertical-top text-end fw-6 pe-4">
                            {{ getCurrencyFormat($value['amount']) }}
                        </td>
                    </tr>
                    @endforeach
                    <tr class="border-bottom border-top">
                        @if($iS3InchPdf)
                        <td class="ps-2 fs-15" style="padding: 4px 8px">
                            {{ $changeLabel['total'] ?? 'Total Payable Amount' }}:
                        </td>
                        @else
                        <td class="ps-2 fs-15" style="padding: 4px 8px">
                            {{ $changeLabel['total'] ?? 'Total Amount' }}:
                        </td>
                        @endif
                        <td class="pe-4 text-end fs-15" style="padding: 4px 8px">
                            {{ getCurrencyFormat($transaction['grand_total'] ?? '0.00') }}
                        </td>
                    </tr>
                    @if (($invoiceSetting['current_outstanding'] ?? false) && ($currentOutstanding ?? false))
                        <tr class="border-top">
                            <td class="fs-12 vertical-top text-start fw-6 ps-2 " >
                                Balance:
                            </td>
                            <td class="fs-12 vertical-top text-end fw-6 pe-4">
                                {{ getCurrencyFormat($balance ?? '0.00') }}
                            </td>
                        </tr>
                        <tr>
                            <td class="fs-12 vertical-top text-start fw-6 ps-2">
                                Previous O/S:
                            </td>
                            <td class="fs-12 vertical-top text-end fw-6 pe-4">
                                {{ getCurrencyFormat($totalDueAmount ?? '0.00') }}
                            </td>
                        </tr>
                        <tr>
                            <td class="fs-12 vertical-top text-start fw-6 ps-2">
                                Current O/S:
                            </td>
                            <td class="fs-12 vertical-top text-end fw-6 pe-4">
                                {{ getCurrencyFormat($currentBalance ?? '0.00') }}
                            </td>
                        </tr>
                    @endif
                </table>
                @if(isset($invoiceSetting['thermal_print_size']) && $invoiceSetting['thermal_print_size'] == \App\Models\CompanySetting::INCH_3)
                    @if($isCompanyGstApplicable &&
                    !empty($checkHsnCodeExist) && ($invoiceSetting['thermal_hsn_summary'] ?? true))
                    <div>
                    <table class="hsn-table">
                        <tr class="border-bottom">
                            <td class="fs-13" width="16.66%">
                                HSN/ SAC
                            </td>
                            <td class="fs-13" width="17%">
                                Taxable Amount
                            </td>
                            <td class="fs-13 ps-0" width="12%">
                                GST (%)
                            </td>
                            @if ($cgst != 0.0)
                            <td class="fs-13 ps-0" width="17%">
                                CGST
                            </td>
                            @endif
                            @if ($sgst != 0.0)
                            <td class="fs-13 ps-0" width="17%">
                                SGST
                            </td>
                            @endif
                            @if ($igst != 0.0)
                            <td class="fs-13 ps-0" width="17%">
                                IGST
                            </td>
                            @endif
                            <td class="fs-13 ps-0" width="17%">
                                Total Tax
                            </td>
                        </tr>
                        @if($isCompanyGstApplicable && !empty($checkHsnCodeExist))
                            @foreach ($checkHsnCodeExist as $key => $item)
                                @foreach ($item as $hsnCode => $data)
                                <tr>
                                    <td class="fs-12" width="16.66%">
                                        @if(!empty($hsnCode))
                                        {{ $hsnCode }}
                                        @else
                                        <span class="ms-12">-</span>
                                        @endif
                                    </td>
                                    <td class="fs-12" width="17%">
                                        {{ getCurrencyFormat(round($checkTAXtExist[$key]['taxableValue'][$hsnCode],
                                        getCompanyFixedDigitNumber()) ?? 0) }}
                                    </td>
                                    <td class="fs-12 ps-0" width="12%">
                                        {{ !empty($key) ? $key : '-' }}
                                    </td>
                                    @if ($cgst != 0.0)
                                        <td class="fs-12 ps-0" width="17%">
                                            {{ getCurrencyFormat(round($checkTAXtExist[$key]['cgst'][$hsnCode] ?? 0,
                                            getCompanyFixedDigitNumber())) }}
                                        </td>
                                    @endif
                                    @if ($sgst != 0.0)
                                        <td class="fs-12 ps-0" width="17%">
                                            {{ getCurrencyFormat(round($checkTAXtExist[$key]['sgst'][$hsnCode] ?? 0,
                                            getCompanyFixedDigitNumber())) }}
                                        </td>
                                    @endif
                                    @if ($igst != 0.0)
                                        <td class="fs-12 ps-0" width="17%">
                                            {{ getCurrencyFormat(round($checkTAXtExist[$key]['igst'][$hsnCode] ?? 0,
                                            getCompanyFixedDigitNumber())) }}
                                        </td>
                                    @endif
                                    @php
                                        $totalTax = round($checkTAXtExist[$key]['cgst'][$hsnCode], getCompanyFixedDigitNumber()) +
                                        round($checkTAXtExist[$key]['sgst'][$hsnCode], getCompanyFixedDigitNumber()) +
                                        round($checkTAXtExist[$key]['igst'][$hsnCode], getCompanyFixedDigitNumber());
                                    @endphp
                                    <td class="fs-12 ps-0" width="17%">
                                        {{ getCurrencyFormat(round($totalTax ?? 0, getCompanyFixedDigitNumber())) }}
                                    </td>
                                </tr>
                                @endforeach
                            @endforeach
                        @endif
                        <tr class="fs-12 border-bottom border-top fw-6">
                            <td class="fs-12 fw-6">
                                Total
                            </td>
                            <td class="fs-12 fw-6">
                                {{ getCurrencyFormat(round($totalAmount, getCompanyFixedDigitNumber())) }}
                            </td>
                            <td class="fs-12 fw-6 ps-0"></td>
                            @if ($cgst != 0.0)
                            <td class="fs-12 fw-6 ps-0" width="14%">
                                {{ getCurrencyFormat(round($cgst, getCompanyFixedDigitNumber())) }}
                            </td>
                            @endif
                            @if ($sgst != 0.0)
                            <td class="fs-12 fw-6 ps-0" width="14%">
                                {{ getCurrencyFormat(round($sgst, getCompanyFixedDigitNumber())) }}
                            </td>
                            @endif
                            @if ($igst != 0.0)
                            <td class="fs-12 fw-6 ps-0" width="14%">
                                {{ getCurrencyFormat(round($igst, getCompanyFixedDigitNumber())) }}
                            </td>
                            @endif
                            <td class="fs-12 fw-6 ps-0">
                                @php
                                $grandTotalTax = $cgst + $sgst + $igst;
                                @endphp
                                {{ getCurrencyFormat(round($grandTotalTax, getCompanyFixedDigitNumber())) }}
                            </td>
                        </tr>
                    </table>
                    </div>
                    @endif
                @endif
                <table cellpadding="0">
                    @if(! empty($transaction['term_and_condition']))
                    <tr>
                        <td class="vertical-top">
                            <div class="mt-2">
                                <h4 class="fs-13 fw-6 pe-2 ps-2 mt-5">
                                    {{ $changeLabel['thermal_terms_and_conditions'] ?? 'Terms and conditions' }}:
                                </h4>
                                <div class="ps-2 pe-4">
                                    <p class="fs-12">
                                        {{ $transaction['term_and_condition'] ?? '' }}
                                    </p>
                                </div>
                            </div>
                        </td>
                    </tr>
                    @endif
                    @if( ($invoiceSetting['thermal_qr_code'] ?? true) && isset($bankDetail->upi_id))
                    <tr>
                        <td class="text-center" style="padding: 28px 0px 0px 0px; width: 100px; max-width:100px; height: 100px">
                            <img src="data:image/svg+xml;base64,{{ base64_encode(generateUPIQRCode($bankDetail->upi_id, $transaction->due_amount)) }}"
                            width="75" height="75" />
                            <p style="margin-top: 8px; font-size: 12px;">
                                {{ $bankDetail->upi_id }}
                            </p>
                        </td>
                    </tr>
                    @endif
                    <tr>
                        <td class="fs-13 text-center" style="padding-right:6px !important; padding-top:18px !important;">
                            {{ $changeLabel['thermal_additional_description'] ?? 'Thank you for choosing us, visit again.' }}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
